package com.happyfresh.fulfillment.unit.slot.availability;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.common.exception.type.ReservedSlotException;
import com.happyfresh.fulfillment.common.model.RoundTripDistance;
import com.happyfresh.fulfillment.common.service.CoralogixAPIService;
import com.happyfresh.fulfillment.common.service.DistanceService;
import com.happyfresh.fulfillment.common.service.SegmentIOService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.lalamove.service.LalamoveService;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import com.happyfresh.fulfillment.shipment.service.ShipmentService;
import com.happyfresh.fulfillment.slot.bean.SlotAvailabilityContext;
import com.happyfresh.fulfillment.slot.bean.availability.AbstractAvailabilityChain;
import com.happyfresh.fulfillment.slot.bean.availability.LalamoveFourWheelsAvailability;
import com.happyfresh.fulfillment.slot.bean.availability.LalamoveTwoWheelsAvailability;
import com.happyfresh.fulfillment.slot.service.SlotReservedService;
import org.apache.commons.lang3.RandomUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class LalamoveFourWheelsAvailabilityTest {

    private SlotAvailabilityContext context;
    private Shipment shipment;
    private Slot slot;
    private StockLocation stockLocation;
    private User user;
    private Country country;
    private AbstractAvailabilityChain chain;
    private LalamoveServiceTypeFactory lalamoveServiceTypeFactory;

    @Mock
    private ShipmentService shipmentService;

    @Mock
    private LalamoveService lalamoveService;

    @Mock
    private CoralogixAPIService coralogixAPIService;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private SlotReservedService slotReservedService;

    @Mock
    private DistanceService distanceService;

    @Mock
    private ShipmentRepository shipmentRepository;

    @Rule
    public ExpectedException expectedEx = ExpectedException.none();

    @Before
    public void setup() throws JsonProcessingException {
        this.shipmentService = new ShipmentService();

        UserFactory userFactory = new UserFactory();
        user = userFactory.createUserData();

        lalamoveServiceTypeFactory = new LalamoveServiceTypeFactory();

        StockLocationFactory stockLocationFactory = new StockLocationFactory();
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user);
        stockLocation = stockLocations.get(0);

        country = new Country();
        country.setName("Indonesia");
        country.setIsoName("ID");
        country.setCreatedAt(LocalDateTime.now());
        country.setCreatedBy(user.getId());
        country.setTenant(user.getTenant());
        Map<String, String> countryPreferences = country.getPreferences();
        countryPreferences.put("enable_lalamove_cod", "true");
        country.setPreferences(countryPreferences);

        SlotFactory slotFactory = new SlotFactory();
        slot = slotFactory.createSlot(stockLocation, user);
        slot.setId(RandomUtils.nextLong());
        slot.setDriverCount(2);
        slot.setStartTime(LocalDateTime.now().withHour(8).withMinute(0).withSecond(0).withNano(0));
        slot.setEndTime(LocalDateTime.now().withHour(9).withMinute(0).withSecond(0).withNano(0));

        ShipmentFactory shipmentFactory = new ShipmentFactory();
        shipment = shipmentFactory.createShipment(slot, user);
        shipment.setOrderTotal(new BigDecimal(400000));
        shipment.setOrderCompanyId(null);

        CategoryFactory categoryFactory = new CategoryFactory();
        Category category = categoryFactory.createCategory(user);

        ItemFactory itemFactory = new ItemFactory();
        Item item = itemFactory.createItem(category,null, user);

        item.setConsideredAsAlcohol(false);
        shipment.setItems(Lists.newArrayList(item));

        doNothing().when(coralogixAPIService).sendLog(any(), any(), any(), any(), any(), (Map)any());
        doReturn(coralogixAPIService).when(applicationContext).getBean(CoralogixAPIService.class);

        context = new SlotAvailabilityContext();
        context.setShipment(shipment);
        context.setCountry(country);
        context.setStockLocation(stockLocation);
        context.setApplicationContext(applicationContext);
        context.setDeliveryDurations(ImmutableMap.of());
        context.setDeliveryDistances(ImmutableMap.of());

        stockLocation.setEnableLalamove(true);
        shipment.setCustomerAllowGeDelivery(true);
    }

    @Test
    public void whenLalamoveDisabled(){
        chain = new LalamoveFourWheelsAvailability(context, slotReservedService, shipmentService, distanceService);
        stockLocation.setEnableLalamove(false);
        Assert.assertFalse(chain.isAvailable(slot));
    }

    @Test
    public void whenLalamoveEnabled(){
        chain = new LalamoveFourWheelsAvailability(context, slotReservedService, shipmentService, distanceService);

        when(lalamoveService.getServiceTypeByEnum(any(), any(Country.class), any(StockLocation.class))).thenReturn(null);
        LalamoveServiceType fourWheelsDelivery = lalamoveServiceTypeFactory.createVanServiceType(country, user);
        when(lalamoveService.getLargestServiceTypeByCountry(any(Country.class), any(StockLocation.class))).thenReturn(fourWheelsDelivery);
        when(lalamoveService.getServiceVolumeInLitre(any(LalamoveServiceType.class))).thenReturn(40.0);
        ReflectionTestUtils.setField(shipmentService, "lalamoveService", lalamoveService);

        when(shipmentRepository.findByNumber(shipment.getNumber())).thenReturn(null);
        ReflectionTestUtils.setField(shipmentService, "shipmentRepository", shipmentRepository);

        context.setReservedSlot(true);
        doReturn(new RoundTripDistance(500, 500)).when(distanceService).getRoundTripDistance(any(Shipment.class), any(StockLocation.class), anyBoolean());
        doNothing().when(slotReservedService).deliveryTPLSlot(any(Shipment.class), any(StockLocation.class),
                any(Slot.class), any(), anyDouble());

        Assert.assertTrue(chain.isAvailable(slot));
        verify(slotReservedService, atLeastOnce()).deliveryTPLSlot(any(Shipment.class), any(StockLocation.class),
                any(Slot.class), any(), anyDouble());
    }

    @Test
    public void whenException_andNotReservedSlot_shouldReturnFalse(){
        chain = new LalamoveFourWheelsAvailability(context, slotReservedService, shipmentService, distanceService);

        when(lalamoveService.getServiceTypeByEnum(any(), any(Country.class), any(StockLocation.class))).thenThrow(new NullPointerException());
        ReflectionTestUtils.setField(shipmentService, "lalamoveService", lalamoveService);
        context.setReservedSlot(false);

        Assert.assertFalse(chain.isAvailable(slot));
    }

    @Test
    public void whenException_andReservedSlot_shouldThrowException(){
        chain = new LalamoveFourWheelsAvailability(context, slotReservedService, shipmentService, distanceService);

        when(lalamoveService.getServiceTypeByEnum(any(), any(Country.class), any(StockLocation.class))).thenThrow(new NullPointerException());
        ReflectionTestUtils.setField(shipmentService, "lalamoveService", lalamoveService);
        context.setReservedSlot(true);

        expectedEx.expect(ReservedSlotException.class);
        chain.isAvailable(slot);
    }

    @Test
    public void whenLalamoveEnabled_notEligibleFourWheel(){
        chain = new LalamoveFourWheelsAvailability(context, slotReservedService, shipmentService, distanceService);

        when(lalamoveService.getServiceTypeByEnum(any(), any(Country.class), any(StockLocation.class))).thenReturn(null);
        ReflectionTestUtils.setField(shipmentService, "lalamoveService", lalamoveService);

        Assert.assertFalse(chain.isAvailable(slot));
        verify(slotReservedService, never()).deliveryTPLSlot(any(Shipment.class), any(StockLocation.class),
                any(Slot.class), any(), anyDouble());
    }
}

