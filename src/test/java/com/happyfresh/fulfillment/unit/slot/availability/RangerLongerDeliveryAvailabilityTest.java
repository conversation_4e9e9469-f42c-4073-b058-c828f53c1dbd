package com.happyfresh.fulfillment.unit.slot.availability;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.common.service.CoralogixAPIService;
import com.happyfresh.fulfillment.common.service.SegmentIOService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.slot.bean.SlotAvailabilityContext;
import com.happyfresh.fulfillment.slot.bean.availability.AbstractAvailabilityChain;
import com.happyfresh.fulfillment.slot.bean.availability.RangerLongerDeliveryAvailability;
import com.happyfresh.fulfillment.slot.model.ShopperDriverSlot;
import com.happyfresh.fulfillment.slot.model.VehicleSlotCount;
import com.happyfresh.fulfillment.slot.model.VehicleSlotCountId;
import com.happyfresh.fulfillment.slot.service.ShopperDriverService;
import com.happyfresh.fulfillment.slot.service.SlotReservedService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;

@RunWith(MockitoJUnitRunner.class)
public class RangerLongerDeliveryAvailabilityTest {

    private SlotAvailabilityContext context;

    private AbstractAvailabilityChain chain;

    private Slot slot;

    private Item item;

    private StockLocation stockLocation;

    private Shipment shipment;

    private Country country;


    @Mock
    private SlotReservedService slotReservedService;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private CoralogixAPIService coralogixAPIService;

    @Mock
    private BatchRepository batchRepository;

    private List<Shift> driverShifts;
    private Shift driverShift;
    private LocalDateTime now;
    private List<Slot> slots;
    private ShopperDriverService shopperDriverService;
    private StockLocation stockLocation2;
    private List<Slot> slots2;
    private ShiftFactory shiftFactory;
    private User user;

    @Before
    public void setup() throws JsonProcessingException {

        now = LocalDateTime.now();
        shopperDriverService = new ShopperDriverService();

        UserFactory userFactory = new UserFactory();
        user = userFactory.createUserData();

        StockLocationFactory stockLocationFactory = new StockLocationFactory();
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user);
        stockLocation = stockLocations.get(0);
        stockLocation.setId(1L);
        stockLocation.getCluster().setSlotType(Slot.Type.LONGER_DELIVERY);
        stockLocation.setType(StockLocation.Type.SPECIAL);

        stockLocation2 = stockLocations.get(1);
        stockLocation2.setId(2L);
        stockLocation2.getCluster().setSlotType(Slot.Type.LONGER_DELIVERY);
        stockLocation2.setType(StockLocation.Type.SPECIAL);

        country = new Country();
        country.setName("Indonesia");
        country.setIsoName("ID");
        country.setPreferences(new HashMap<String, String>() {{
            put("allow_ewallet_for_tpl", "true");
        }});
        country.setCreatedAt(LocalDateTime.now());
        country.setCreatedBy(user.getId());
        country.setTenant(user.getTenant());

        SlotFactory slotFactory = new SlotFactory();
        slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 1, 3, now);
        slot = slots.get(2);
        slot.setId(3L);

        slots2 = slotFactory.createLongerDeliverySlots(stockLocation2, user, 1, 3, now);

        ShipmentFactory shipmentFactory = new ShipmentFactory();
        shipment = shipmentFactory.createShipment(slot, user);
        shipment.setOrderTotal(new BigDecimal(400000));

        CategoryFactory categoryFactory = new CategoryFactory();
        Category category = categoryFactory.createCategory(user);

        ItemFactory itemFactory = new ItemFactory();
        item = itemFactory.createItem(category, null, user);

        item.setConsideredAsAlcohol(false);
        shipment.setItems(Lists.newArrayList(item));

        doNothing().when(coralogixAPIService).sendLog(any(), any(), any(), any(), any(), (Map)any());
        doReturn(coralogixAPIService).when(applicationContext).getBean(CoralogixAPIService.class);

        shiftFactory = new ShiftFactory();
        driverShifts = shiftFactory.createDriverShifts(stockLocation, now, 2, 8, 5, user);
        driverShift = driverShifts.get(0);
        driverShift.setId(1L);

        context = new SlotAvailabilityContext();
        context.setShipment(shipment);
        context.setCountry(country);
        context.setStockLocation(stockLocation);
        context.setApplicationContext(applicationContext);
        context.setDeliveryTimePerShipment(10);

        ShopperDriverSlot shopperDriverSlot = shopperDriverService.calculateShopperAndDriver(slots, stockLocation);
        context.setSlots(shopperDriverSlot.getResultSlots());
        context.setClusteredSlots(shopperDriverSlot.getClusteredSlots());

    }

    private VehicleSlotCount getDriverVehicleSlotCount(Slot slot, Shift shift) {
        VehicleSlotCount v1 = new VehicleSlotCount();
        v1.setShiftDriverCount(0);
        if(shift != null) {
            v1.setShiftStockLocationId(shift.getStockLocation().getId());
            v1.setShiftDriverCount(shift.getCount());
            v1.setShiftStartTime(shift.getStartTime());
            v1.setShiftEndTime(shift.getEndTime());
        }
        v1.setSlotEndTime(slot.getEndTime());
        v1.setVehicleCount(1);
        v1.setEarliestOfLatestBatchEndTime(slot.getEndTime());

        VehicleSlotCountId id1 = new VehicleSlotCountId();
        id1.setSlotStartTime(slot.getStartTime());
        id1.setShiftId(shift != null ? shift.getId() : null);

        v1.setId(id1);

        return v1;
    }

    @Test
    public void whenRangerAvailable_shouldReturnTrue() {
        List<Shift> driverShifts1 = shiftFactory.createDriverShifts(stockLocation2, now, 2, 8, 5, user);
        Shift driverShift2 = driverShifts1.get(0);
        driverShift2.setId(2L);
        VehicleSlotCount v1 = getDriverVehicleSlotCount(slots.get(2), driverShift);
        VehicleSlotCount v2 = getDriverVehicleSlotCount(slots.get(2), null);
        VehicleSlotCount v3 = getDriverVehicleSlotCount(slots.get(0), driverShift);
        VehicleSlotCount v4 = getDriverVehicleSlotCount(slots2.get(2), driverShift2);

        context.setDriverVehicleSlotCounts(Lists.newArrayList(v1, v2, v3, v4));

        chain = new RangerLongerDeliveryAvailability(context, slotReservedService, batchRepository);

        Assert.assertTrue(chain.isAvailable(slot));
    }

    @Test
    public void whenDriverShiftIsNotAvailable_shouldReturnFalse() {
        VehicleSlotCount v1 = getDriverVehicleSlotCount(slots.get(2), null);
        VehicleSlotCount v2 = getDriverVehicleSlotCount(slots.get(2), null);

        context.setDriverVehicleSlotCounts(Lists.newArrayList(v1, v2));

        chain = new RangerLongerDeliveryAvailability(context, slotReservedService, batchRepository);

        Assert.assertFalse(chain.isAvailable(slot));
    }

    @Test
    public void whenOneOfVehicleSlotShiftFieldsIsNull_shouldReturnFalse() {
        VehicleSlotCount v1 = getDriverVehicleSlotCount(slots.get(2), driverShift);

        context.setDriverVehicleSlotCounts(Lists.newArrayList(v1));

        chain = new RangerLongerDeliveryAvailability(context, slotReservedService, batchRepository);

        v1.setShiftStockLocationId(null);
        Assert.assertFalse(chain.isAvailable(slot));

        v1.setShiftStockLocationId(driverShift.getStockLocation().getId());
        v1.setShiftStartTime(null);
        Assert.assertFalse(chain.isAvailable(slot));

        v1.setShiftStartTime(driverShift.getStartTime());
        v1.setShiftDriverCount(0);

        Assert.assertFalse(chain.isAvailable(slot));

        v1.setShiftDriverCount(driverShift.getCount());
        Assert.assertTrue(chain.isAvailable(slot));
    }

    @Test
    public void whenSlotDriverCountBelowZero_shouldReturnFalse() {
        VehicleSlotCount v1 = getDriverVehicleSlotCount(slots.get(2), driverShift);
        VehicleSlotCount v2 = getDriverVehicleSlotCount(slots.get(2), null);
        VehicleSlotCount v3 = getDriverVehicleSlotCount(slots.get(0), driverShift);

        context.setDriverVehicleSlotCounts(Lists.newArrayList(v1, v2, v3));

        chain = new RangerLongerDeliveryAvailability(context, slotReservedService, batchRepository);
        slot.setDriverCount(-1);
        Assert.assertFalse(chain.isAvailable(slot));
    }

    @Test
    public void whenOverMaximumDeliveryRadius_shouldReturnFalse() {
        VehicleSlotCount v1 = getDriverVehicleSlotCount(slots.get(2), driverShift);
        VehicleSlotCount v2 = getDriverVehicleSlotCount(slots.get(2), null);
        VehicleSlotCount v3 = getDriverVehicleSlotCount(slots.get(0), driverShift);

        context.setDriverVehicleSlotCounts(Lists.newArrayList(v1, v2, v3));

        chain = new RangerLongerDeliveryAvailability(context, slotReservedService, batchRepository);
        context.setOverMaximumDeliveryRadius(true);
        Assert.assertFalse(chain.isAvailable(slot));
    }

    @Test
    public void whenNotAvailableDeliveryTime_shouldReturnFalse() {
        VehicleSlotCount v1 = getDriverVehicleSlotCount(slots.get(2), driverShift);
        VehicleSlotCount v2 = getDriverVehicleSlotCount(slots.get(2), driverShift);
        VehicleSlotCount v3 = getDriverVehicleSlotCount(slots.get(0), driverShift);

        context.setDriverVehicleSlotCounts(Lists.newArrayList(v1, v2, v3));
        context.setDeliveryTimePerShipment(240);

        chain = new RangerLongerDeliveryAvailability(context, slotReservedService, batchRepository);
        Assert.assertFalse(chain.isAvailable(slot));
    }
}
