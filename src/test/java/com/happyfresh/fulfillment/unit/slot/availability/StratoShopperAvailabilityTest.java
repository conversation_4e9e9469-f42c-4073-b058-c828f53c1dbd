package com.happyfresh.fulfillment.unit.slot.availability;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.common.service.CoralogixAPIService;
import com.happyfresh.fulfillment.common.service.SegmentIOService;
import com.happyfresh.fulfillment.enabler.model.StratoShopperCapacity;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.slot.bean.SlotAvailabilityContext;
import com.happyfresh.fulfillment.slot.bean.availability.AbstractAvailabilityChain;
import com.happyfresh.fulfillment.slot.bean.availability.DriverLongerDeliveryAvailability;
import com.happyfresh.fulfillment.slot.bean.availability.StratoShopperAvailability;
import com.happyfresh.fulfillment.slot.service.SlotReservedService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;

@RunWith(MockitoJUnitRunner.class)
public class StratoShopperAvailabilityTest {

    private SlotAvailabilityContext context;

    private AbstractAvailabilityChain chain;

    @Mock
    private SlotReservedService slotReservedService;

    @Mock
    private CoralogixAPIService coralogixAPIService;

    @Mock
    private ApplicationContext applicationContext;

    private StockLocation stockLocation;
    private LocalDateTime now;
    private List<Slot> slots;
    private Slot slot;
    private Shipment shipment;
    private Item item;

    @Before
    public void setup() throws JsonProcessingException {
        now = LocalDateTime.now();

        UserFactory userFactory = new UserFactory();
        User user = userFactory.createUserData();

        StockLocationFactory stockLocationFactory = new StockLocationFactory();
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user);
        stockLocation = stockLocations.get(0);
        stockLocation.getCluster().setSlotType(Slot.Type.LONGER_DELIVERY);
        stockLocation.setEnabler(StockLocation.Enabler.HFC);
        stockLocation.setEnablerPlatform(StockLocation.EnablerPlatform.STRATO);

        SlotFactory slotFactory = new SlotFactory();
        slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 1, 3, now);
        slot = slots.get(0);
        slot.setId(3L);

        ShipmentFactory shipmentFactory = new ShipmentFactory();
        shipment = shipmentFactory.createShipment(slot, user);
        shipment.setOrderTotal(new BigDecimal(400000));

        CategoryFactory categoryFactory = new CategoryFactory();
        Category category = categoryFactory.createCategory(user);

        ItemFactory itemFactory = new ItemFactory();
        item = itemFactory.createItem(category, null, user);

        item.setConsideredAsAlcohol(false);
        shipment.setItems(Lists.newArrayList(item));

        doNothing().when(coralogixAPIService).sendLog(any(), any(), any(), any(), any(), (Map)any());
        doReturn(coralogixAPIService).when(applicationContext).getBean(CoralogixAPIService.class);

        context = new SlotAvailabilityContext();
        context.setStockLocation(stockLocation);
        context.setApplicationContext(applicationContext);
        context.setShipment(shipment);
        context.setDeliveryChecker(true);
    }

    @Test
    public void whenStratoShopperAvailable_shouldReturnTrue() {
        List<StratoShopperCapacity> resultCapacities = slots.stream()
                .map(dSlot -> new StratoShopperCapacity(dSlot.getId(), dSlot.getStartTime(), true))
                .collect(Collectors.toList());

        context.setStratoShopperCapacities(resultCapacities);

        chain = new StratoShopperAvailability(context, slotReservedService);
        boolean isAvailable = chain.isAvailable(slot);

        Assert.assertTrue(isAvailable);
    }

    @Test
    public void whenStratoServiceReturnEmpty_shouldReturnFalse() {
        List<StratoShopperCapacity> resultCapacities = slots.stream()
                .map(dSlot -> new StratoShopperCapacity(99L, dSlot.getStartTime(), true))
                .collect(Collectors.toList());

        context.setStratoShopperCapacities(resultCapacities);

        chain = new StratoShopperAvailability(context, slotReservedService);
        boolean isAvailable = chain.isAvailable(slot);

        Assert.assertFalse(isAvailable);
        Assert.assertEquals(Slot.UnavailableReason.NO_SHOPPER_CAPACITY.toString(), context.getSlotIdAndUnavailableReasonMap().get(slot.getId()));
    }

    @Test
    public void whenStratoCapacityReturnFalse_shouldReturnFalse() {
        List<StratoShopperCapacity> resultCapacities = slots.stream()
                .map(dSlot -> new StratoShopperCapacity(dSlot.getId(), dSlot.getStartTime(), false))
                .collect(Collectors.toList());

        context.setStratoShopperCapacities(resultCapacities);

        chain = new StratoShopperAvailability(context, slotReservedService);
        boolean isAvailable = chain.isAvailable(slot);

        Assert.assertFalse(isAvailable);
        Assert.assertEquals(Slot.UnavailableReason.NO_SHOPPER_CAPACITY.toString(), context.getSlotIdAndUnavailableReasonMap().get(slot.getId()));
    }

    @Test
    public void whenThrowException_shouldReturnFalse() {

        context.setStratoShopperCapacities(null);

        chain = new StratoShopperAvailability(context, slotReservedService);
        boolean isAvailable = chain.isAvailable(slot);

        Assert.assertFalse(isAvailable);
        Assert.assertEquals(Slot.UnavailableReason.FAILED_CHAIN.toString(), context.getSlotIdAndUnavailableReasonMap().get(slot.getId()));
    }

    @Test
    public void whenIsNotHFS_shouldReturnTrue() {

        context.setStratoShopperCapacities(null);
        stockLocation.setEnabler(null);

        chain = new StratoShopperAvailability(context, slotReservedService);
        boolean isAvailable = chain.isAvailable(slot);

        Assert.assertTrue(isAvailable);
    }

}
