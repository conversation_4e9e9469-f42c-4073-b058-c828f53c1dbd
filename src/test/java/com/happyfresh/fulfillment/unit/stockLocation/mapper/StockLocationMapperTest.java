package com.happyfresh.fulfillment.unit.stockLocation.mapper;

import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.factory.StockLocationFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.UserFactory;
import com.happyfresh.fulfillment.stockLocation.mapper.StockLocationMapper;
import com.happyfresh.fulfillment.stockLocation.mapper.StockLocationMapperImpl;
import com.happyfresh.fulfillment.stockLocation.presenter.NearbyStockLocationPresenter;
import com.happyfresh.fulfillment.stockLocation.presenter.StockLocationPresenter;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

@ContextConfiguration(classes = StockLocationMapperTest.SpringTestConfig.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class StockLocationMapperTest {

    @Configuration
    @ComponentScan(basePackageClasses = {
            StockLocationMapperImpl.class
    })
    public static class SpringTestConfig {
    }

    @Autowired
    private StockLocationMapper mapper;

    private StockLocation stockLocation;

    @Before
    public void setup() {
        UserFactory userFactory = new UserFactory();
        User admin = userFactory.createUserData(Role.Name.ADMIN);

        StockLocationFactory stockLocationFactory = new StockLocationFactory();
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin, Slot.Type.LONGER_DELIVERY);

        stockLocation = stockLocations.get(0);
        stockLocation.setId(1L);

    }

    private void setPreference(String prefKey, String prefValue) {
        Map<String, String> preferences = stockLocation.getPreferences();
        preferences.put(prefKey, prefValue);
    }

    @Test
    public void shouldMapFieldsCorrectly() {
        setPreference("enable_shopper_auto_assignment", "true");

        StockLocationPresenter stockLocationPresenter = mapper.stockLocationToStockLocationPresenter(stockLocation);

        assertEquals(stockLocation.getId(), stockLocationPresenter.getId());
        assertEquals(stockLocation.getName(), stockLocationPresenter.getName());
        assertEquals(stockLocation.getCluster().getName(), stockLocationPresenter.getClusterName());
        assertEquals(stockLocation.getState().getName(), stockLocationPresenter.getState().getName());
        assertEquals(stockLocation.getTenant().getId(), stockLocationPresenter.getTenantId());
        assertEquals(stockLocation.getExternalId(), stockLocationPresenter.getExternalId());
        assertTrue(stockLocationPresenter.isEnableShopperAutoAssignment());
    }

    @Test
    public void toNearbyStockLocationPresenter_driverAutoAssignmentEnabled_driverRole_clockInTypeShouldBeShift() {
        Cluster cluster = stockLocation.getCluster();
        Map<String, String> prefs = cluster.getPreferences();
        prefs.put("enable_driver_auto_assignment", "true");
        cluster.setPreferences(prefs);
        stockLocation.setCluster(cluster);

        NearbyStockLocationPresenter stockLocationPresenter = mapper.stockLocationToNearbyStockLocationPresenter(
                stockLocation,
                List.of(Role.Name.DRIVER));
        assertEquals(Cluster.ClockInType.SHIFT, stockLocationPresenter.getClockInType());
    }

    @Test
    public void toNearbyStockLocationPresenter_driverAutoAssignmentDisabled_driverRole_clockInTypeShouldBeHour() {
        Cluster cluster = stockLocation.getCluster();
        Map<String, String> prefs = cluster.getPreferences();
        prefs.put("enable_driver_auto_assignment", "false");
        cluster.setPreferences(prefs);
        stockLocation.setCluster(cluster);

        NearbyStockLocationPresenter stockLocationPresenter = mapper.stockLocationToNearbyStockLocationPresenter(
                stockLocation,
                List.of(Role.Name.DRIVER));
        assertEquals(Cluster.ClockInType.HOUR, stockLocationPresenter.getClockInType());
    }

    @Test
    public void toNearbyStockLocationPresenter_driverAutoAssignmentEnabled_roleOtherThanDriver_clockInTypeShouldBeHour() {
        Cluster cluster = stockLocation.getCluster();
        Map<String, String> prefs = cluster.getPreferences();
        prefs.put("enable_driver_auto_assignment", "true");
        cluster.setPreferences(prefs);
        stockLocation.setCluster(cluster);

        NearbyStockLocationPresenter stockLocationPresenter = mapper.stockLocationToNearbyStockLocationPresenter(
                stockLocation,
                List.of(Role.Name.SHOPPER));
        assertEquals(Cluster.ClockInType.HOUR, stockLocationPresenter.getClockInType());

        stockLocationPresenter = mapper.stockLocationToNearbyStockLocationPresenter(
                stockLocation,
                List.of(Role.Name.ON_DEMAND_RANGER));
        assertEquals(Cluster.ClockInType.HOUR, stockLocationPresenter.getClockInType());
    }

    @Test
    public void toNearbyStockLocationPresenter_driverAutoAssignmentDisabled_roleOtherThanDriver_clockInTypeShouldBeHour() {
        Cluster cluster = stockLocation.getCluster();
        Map<String, String> prefs = cluster.getPreferences();
        prefs.put("enable_driver_auto_assignment", "false");
        cluster.setPreferences(prefs);
        stockLocation.setCluster(cluster);

        NearbyStockLocationPresenter stockLocationPresenter = mapper.stockLocationToNearbyStockLocationPresenter(
                stockLocation,
                List.of(Role.Name.SHOPPER));
        assertEquals(Cluster.ClockInType.HOUR, stockLocationPresenter.getClockInType());

        stockLocationPresenter = mapper.stockLocationToNearbyStockLocationPresenter(
                stockLocation,
                List.of(Role.Name.ON_DEMAND_RANGER));
        assertEquals(Cluster.ClockInType.HOUR, stockLocationPresenter.getClockInType());
    }

}
