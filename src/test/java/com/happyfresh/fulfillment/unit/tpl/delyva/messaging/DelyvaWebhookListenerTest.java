package com.happyfresh.fulfillment.unit.tpl.delyva.messaging;

import com.happyfresh.fulfillment.tpl.delyva.messaging.DelyvaWebhookEvent;
import com.happyfresh.fulfillment.tpl.delyva.messaging.DelyvaWebhookListener;
import com.happyfresh.fulfillment.tpl.delyva.service.DelyvaDeliveryUpdateService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DelyvaWebhookListenerTest {

    @Mock
    private DelyvaDeliveryUpdateService delyvaDeliveryUpdateService;

    @InjectMocks
    private DelyvaWebhookListener delyvaWebhookListener;

    @Test
    public void test_listen_shouldCallHandleDelyvaWebhookUpdate() {
        DelyvaWebhookEvent event = new DelyvaWebhookEvent();
        delyvaWebhookListener.listen(event);

        Mockito.verify(delyvaDeliveryUpdateService).handleDelyvaWebhookUpdate(event);
    }
}
