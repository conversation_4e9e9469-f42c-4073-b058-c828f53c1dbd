package com.happyfresh.fulfillment.unit.fee.service;

import com.happyfresh.fulfillment.common.service.DistanceService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.entity.ddf.DDFMatrix;
import com.happyfresh.fulfillment.entity.ddf.FreeDeliveryThresholdMatrix;
import com.happyfresh.fulfillment.entity.ddf.TimeBasedFeeMatrix;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.slot.bean.AppliedFee;
import com.happyfresh.fulfillment.slot.bean.DDFCalculationContext;
import com.happyfresh.fulfillment.slot.service.ddf.DDFCalculationServiceChain;
import com.happyfresh.fulfillment.slot.service.ddf.TimeBasedFeeService;
import com.happyfresh.fulfillment.repository.DDFMatrixRepository;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class TimeBasedFeeServiceTest {

    private DDFCalculationServiceChain service;

    private DDFCalculationServiceChain.Builder serviceBuilder;

    private DDFCalculationContext context;

    @Mock
    private DDFMatrixRepository ddfMatrixRepository;

    @Mock
    private DistanceService distanceService;

    private List<Slot> slots;

    private List<StockLocation> stockLocations;

    private Shipment shipment;

    @Before
    public void setUp() throws InstantiationException, IllegalAccessException {
        MockitoAnnotations.initMocks(this.getClass());
        this.serviceBuilder = new DDFCalculationServiceChain.Builder();
        this.service = serviceBuilder.addServiceChain(TimeBasedFeeService.class).build(StockLocation.DDFType.ORIGINAL);

        UserFactory userFactory = new UserFactory();
        StockLocationFactory stockLocationFactory = new StockLocationFactory();
        SlotFactory slotFactory = new SlotFactory();

        User user = userFactory.createUserData();
        this.stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user, false);
        this.slots = slotFactory.createSlots(this.stockLocations, 6, 6, 14, 15, Slot.Type.ONE_HOUR, user);
        this.shipment = new ShipmentFactory().createShipment(this.slots.get(0), user);
        List<Item> items = new ItemFactory().createItems(shipment, user, 1);
        this.shipment.setItems(items);
    }

    @Test
    public void updateAppliedFeeContext() {
        ZonedDateTime slotTime = this.slots.get(0).getStartTime().atZone(ZoneId.of("UTC"))
                .withZoneSameInstant(ZoneId.of(this.slots.get(0).getStockLocation().getState().getTimeZone()));
        List<DDFMatrix> ddfMatrix = Arrays.asList(
                new TimeBasedFeeMatrix(slotTime.getDayOfWeek().getValue(), slotTime.getHour(), BigDecimal.valueOf(1000D), stockLocations.get(0))
        );

        Mockito.when(this.ddfMatrixRepository.findAllByStockLocationOrderByIdAsc(stockLocations.get(0))).thenReturn(ddfMatrix);

        this.context = new DDFCalculationContext.Builder()
                .withStockLocation(stockLocations.get(0))
                .withShipmentContext(shipment, distanceService, 1, false)
                .withDDFMatrixConfig(ddfMatrixRepository)
                .build();

        AppliedFee fee = this.service.calculateFee(this.context, this.slots.get(0));

        Assert.assertEquals(BigDecimal.valueOf(1000D), fee.getTotal());
        Assert.assertEquals(BigDecimal.valueOf(1000D), fee.getDetail().getTimeBasedFee());
    }

    @Test
    public void return0IfNotExists() {
        ZonedDateTime slotTime = this.slots.get(0).getStartTime().atZone(ZoneId.of("UTC"))
                .withZoneSameInstant(ZoneId.of(this.slots.get(0).getStockLocation().getState().getTimeZone()));
        List<DDFMatrix> ddfMatrix = Arrays.asList(
                new TimeBasedFeeMatrix(slotTime.getDayOfWeek().getValue(), slotTime.getHour(), BigDecimal.valueOf(1000D), stockLocations.get(0))
        );

        Mockito.when(this.ddfMatrixRepository.findAllByStockLocationOrderByIdAsc(stockLocations.get(0))).thenReturn(ddfMatrix);

        this.context = new DDFCalculationContext.Builder()
                .withStockLocation(stockLocations.get(0))
                .withShipmentContext(shipment, distanceService, 1, false)
                .withDDFMatrixConfig(ddfMatrixRepository)
                .build();

        AppliedFee fee = this.service.calculateFee(this.context, this.slots.get(1));

        Assert.assertEquals(BigDecimal.valueOf(0), fee.getTotal());
        Assert.assertEquals(BigDecimal.valueOf(0), fee.getDetail().getTimeBasedFee());
    }

    @Ignore("Free Delivery mechanism is removed from Fulfillment and moved to Spree.")
    @Test
    public void return0IfOrderTotalIsGreaterOrEqualThanFreeDeliveryThreshold() {
        this.shipment.setOrderTotal(new BigDecimal( 10000));
        ZonedDateTime slotTime = this.slots.get(0).getStartTime().atZone(ZoneId.of("UTC"))
                .withZoneSameInstant(ZoneId.of(this.slots.get(0).getStockLocation().getState().getTimeZone()));
        List<DDFMatrix> ddfMatrix = Arrays.asList(
                new TimeBasedFeeMatrix(slotTime.getDayOfWeek().getValue(), slotTime.getHour(), BigDecimal.valueOf(1000D), stockLocations.get(0)),
                new FreeDeliveryThresholdMatrix(BigDecimal.valueOf(10000D), stockLocations.get(0))
        );

        Mockito.when(this.ddfMatrixRepository.findAllByStockLocationOrderByIdAsc(stockLocations.get(0))).thenReturn(ddfMatrix);

        this.context = new DDFCalculationContext.Builder()
                .withStockLocation(stockLocations.get(0))
                .withShipmentContext(shipment, distanceService, 1, false)
                .withDDFMatrixConfig(ddfMatrixRepository)
                .build();

        AppliedFee fee = this.service.calculateFee(this.context, this.slots.get(1));

        Assert.assertEquals(BigDecimal.valueOf(0), fee.getTotal());
        Assert.assertEquals(BigDecimal.valueOf(0), fee.getDetail().getTimeBasedFee());
    }

    @Test
    public void shouldIgnoreFreeDeliveryThreshold() {
        this.shipment.setOrderTotal(new BigDecimal( 15000));

        Slot slot = this.slots.get(0);
        StockLocation sl = stockLocations.get(0);
        ZoneId zoneId = ZoneId.of(sl.getState().getTimeZone());
        ZonedDateTime slotTime = slot.getStartTime()
                .atZone(ZoneId.of("UTC"))
                .withZoneSameInstant(zoneId);

        List<DDFMatrix> ddfMatrix = Arrays.asList(
                new TimeBasedFeeMatrix(slotTime.getDayOfWeek().getValue(), slotTime.getHour(), BigDecimal.valueOf(1000D), sl),
                new FreeDeliveryThresholdMatrix(BigDecimal.valueOf(15000D), sl));
        Mockito.when(this.ddfMatrixRepository.findAllByStockLocationOrderByIdAsc(sl)).thenReturn(ddfMatrix);

        this.context = new DDFCalculationContext.Builder()
                .withStockLocation(sl)
                .withShipmentContext(shipment, distanceService, 1, false)
                .withDDFMatrixConfig(ddfMatrixRepository)
                .build();

        AppliedFee fee = this.service.calculateFee(this.context, slot);

        Assert.assertEquals(BigDecimal.valueOf(1000D), fee.getTotal());
        Assert.assertEquals(BigDecimal.valueOf(1000D), fee.getDetail().getTimeBasedFee());
        Assert.assertFalse(fee.getDetail().isFreeDelivery());
    }
}
