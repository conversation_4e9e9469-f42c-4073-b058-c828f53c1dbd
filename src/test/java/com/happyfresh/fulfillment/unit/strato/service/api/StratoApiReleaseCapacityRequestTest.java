package com.happyfresh.fulfillment.unit.strato.service.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.common.property.StratoProperty;
import com.happyfresh.fulfillment.enabler.form.StratoReleaseCapacityForm;
import com.happyfresh.fulfillment.enabler.presenter.strato.StratoReleaseCapacityPresenter;
import com.happyfresh.fulfillment.enabler.service.api.StratoApiReleaseCapacityRequest;
import com.happyfresh.fulfillment.integrationTest.test.common.NoResetRequestExpectationManager;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.springframework.test.web.client.match.MockRestRequestMatchers.method;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.requestTo;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withStatus;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withSuccess;

@RunWith(MockitoJUnitRunner.class)
public class StratoApiReleaseCapacityRequestTest {

    private MockRestServiceServer mockServer;
    private StratoApiReleaseCapacityRequest request;

    @Before
    public void setup() {
        StratoProperty stratoProperty = new StratoProperty();
        stratoProperty.setBaseUrl("https://strato-test-url.io");

        RestTemplate template = new RestTemplate();
        mockServer = MockRestServiceServer.bindTo(template).build(new NoResetRequestExpectationManager());

        ObjectMapper mapper = new ObjectMapper();

        StratoReleaseCapacityForm form = new StratoReleaseCapacityForm();
        form.setOrderNumber("R123");
        request = new StratoApiReleaseCapacityRequest(form, "12", mapper);
        request.setRestTemplate(template);
        request.setStratoProperty(stratoProperty);
    }

    @Test
    public void shouldCallApi() {
        String response = "{" +
                "\"delivery_slot\":\"2021-07-18T22:00:00Z\"," +
                "\"book_available\":false," +
                "\"total_line_item_capacity\":90," +
                "\"total_line_item_available\":73}";

        String url = "https://strato-test-url.io/store/12/capacity/release";
        mockServer.expect(ExpectedCount.once(), requestTo(url))
            .andExpect(method(HttpMethod.POST))
            .andRespond(withSuccess(response, MediaType.APPLICATION_JSON)); // dummy response

        request.makeRequest();
        mockServer.verify();
    }

    @Test
    public void shouldResponseWithOptionalPresenter() {
        String response = "{" +
                "\"delivery_slot\":\"2021-07-18T22:00:00Z\"," +
                "\"book_available\":false," +
                "\"total_line_item_capacity\":90," +
                "\"total_line_item_available\":73}";

        String url = "https://strato-test-url.io/store/12/capacity/release";
        mockServer.expect(ExpectedCount.once(), requestTo(url))
            .andExpect(method(HttpMethod.POST))
            .andRespond(withSuccess(response, MediaType.APPLICATION_JSON)); // dummy response

        Optional<StratoReleaseCapacityPresenter> maybePresenter = request.makeRequest();
        mockServer.verify();

        Assert.assertTrue(maybePresenter.isPresent());
        StratoReleaseCapacityPresenter presenter = maybePresenter.get();
        Assert.assertEquals(LocalDateTime.class, presenter.getDeliverySlot().getClass());
        Assert.assertEquals((Integer) 90, presenter.getTotalLineItemCapacity());
        Assert.assertEquals((Integer) 73, presenter.getTotalLineItemAvailable());
    }

    @Test
    public void withResponseErrorShouldReturnOptionalEmpty() throws Exception {
        String url = "https://strato-test-url.io/store/12/capacity/release";
        mockServer.expect(ExpectedCount.once(), requestTo(url))
            .andExpect(method(HttpMethod.POST))
            .andRespond(withStatus(HttpStatus.valueOf(409))
                .contentType(MediaType.APPLICATION_JSON)
                .body("{ \"message\": \"ERROR_STRATO\" }")
            );

        Optional<StratoReleaseCapacityPresenter> maybePresenter = request.makeRequest();
        mockServer.verify();

        Assert.assertFalse(maybePresenter.isPresent());
    }
}
