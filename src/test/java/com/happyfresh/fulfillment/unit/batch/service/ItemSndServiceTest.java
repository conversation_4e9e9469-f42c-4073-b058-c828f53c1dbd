package com.happyfresh.fulfillment.unit.batch.service;

import com.happyfresh.fulfillment.batch.service.ItemSndService;
import com.happyfresh.fulfillment.common.exception.type.JobAlreadyFinalizedException;
import com.happyfresh.fulfillment.common.exception.type.JobAlreadyFinishedException;
import com.happyfresh.fulfillment.common.exception.type.JobNotFoundException;
import com.happyfresh.fulfillment.common.exception.type.JobNotStartedException;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.ItemRepository;
import com.happyfresh.fulfillment.shipment.service.ShipmentService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import javax.persistence.EntityNotFoundException;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ItemSndServiceTest {

    @InjectMocks
    private ItemSndService itemSndService;

    @Mock
    private ShipmentService shipmentService;

    @Mock
    private ItemRepository itemRepository;

    private User shopper;
    private Shipment shipment;
    private Item item1;
    private Item replacement1;

    @Before
    public void setup(){
        UserFactory userFactory = new UserFactory();
        User user = userFactory.createUserData();
        shopper = userFactory.createUserData(Role.Name.SHOPPER);

        StockLocationFactory stockLocationFactory = new StockLocationFactory();
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user);
        StockLocation stockLocation = stockLocations.get(0);

        SlotFactory slotFactory = new SlotFactory();
        Slot slot = slotFactory.createSlot(stockLocation, user);

        ShipmentFactory shipmentFactory = new ShipmentFactory();
        shipment = shipmentFactory.createShipmentWithBatch(slot, user);
        shipment.setId(1L);

        List<Item> items = new ItemFactory().createItems(shipment, user, 2);
        shipment.setItems(items);
        shipment.getJobs().stream().filter(j -> j.isShopping() || j.isOnDemandShopping() || j.isRanger() || j.isOnDemandRanger()).findFirst().get().setState(Job.State.STARTED);

        item1 = shipment.getItems().get(0);
        item1.setId(11L);
        ItemFactory itemFactory = new ItemFactory();
        replacement1 = itemFactory.createItem(item1.getCategory(), shipment, shopper);
    }

    @Test
    public void deleteReplacement(){
        when(shipmentService.findByNumber(shipment.getNumber())).thenReturn(shipment);
        when(itemRepository.findByShipmentIdAndReplacedItemIdAndSku(shipment.getId(), null, item1.getSku()))
                .thenReturn(item1);
        when(itemRepository.findByShipmentIdAndReplacedItemIdAndSku(shipment.getId(), item1.getId(), replacement1.getSku()))
                .thenReturn(replacement1);
        Mockito.when(itemRepository.save(any(Item.class))).thenAnswer(i -> i.getArguments()[0]);

        Item result = itemSndService.deleteReplacement(shipment.getNumber(), item1.getSku(), replacement1.getSku());
        Assert.assertEquals(0, result.getReplacements().size());
    }

    @Test(expected = EntityNotFoundException.class)
    public void deleteReplacement_whenParentItemNotFound_shouldThrowException(){
        when(shipmentService.findByNumber(shipment.getNumber())).thenReturn(shipment);
        when(itemRepository.findByShipmentIdAndReplacedItemIdAndSku(shipment.getId(), null, item1.getSku()))
                .thenReturn(null);

        itemSndService.deleteReplacement(shipment.getNumber(), item1.getSku(), replacement1.getSku());
    }

    @Test(expected = JobNotStartedException.class)
    public void deleteReplacement_whenJobStateInitial_shouldThrowException(){
        shipment.getJobs().stream().filter(j -> j.isShopping() || j.isOnDemandShopping() || j.isRanger() || j.isOnDemandRanger()).findFirst().get().setState(Job.State.INITIAL);

        when(shipmentService.findByNumber(shipment.getNumber())).thenReturn(shipment);
        when(itemRepository.findByShipmentIdAndReplacedItemIdAndSku(shipment.getId(), null, item1.getSku()))
                .thenReturn(item1);

        itemSndService.deleteReplacement(shipment.getNumber(), item1.getSku(), replacement1.getSku());
    }

    @Test(expected = JobAlreadyFinishedException.class)
    public void deleteReplacement_whenJobStateFinished_shouldThrowException(){
        shipment.getJobs().stream().filter(j -> j.isShopping() || j.isOnDemandShopping() || j.isRanger() || j.isOnDemandRanger()).findFirst().get().setState(Job.State.FINISHED);

        when(shipmentService.findByNumber(shipment.getNumber())).thenReturn(shipment);
        when(itemRepository.findByShipmentIdAndReplacedItemIdAndSku(shipment.getId(), null, item1.getSku()))
                .thenReturn(item1);

        itemSndService.deleteReplacement(shipment.getNumber(), item1.getSku(), replacement1.getSku());
    }

    @Test(expected = JobAlreadyFinalizedException.class)
    public void deleteReplacement_whenJobStateFinalizing_shouldThrowException(){
        shipment.getJobs().stream().filter(j -> j.isShopping() || j.isOnDemandShopping() || j.isRanger() || j.isOnDemandRanger()).findFirst().get().setState(Job.State.FINALIZING);

        when(shipmentService.findByNumber(shipment.getNumber())).thenReturn(shipment);
        when(itemRepository.findByShipmentIdAndReplacedItemIdAndSku(shipment.getId(), null, item1.getSku()))
                .thenReturn(item1);

        itemSndService.deleteReplacement(shipment.getNumber(), item1.getSku(), replacement1.getSku());
    }

    @Test(expected = JobNotFoundException.class)
    public void deleteReplacement_whenJobNotFound_shouldThrowException(){
        shipment.setJobs(Collections.emptyList());

        when(shipmentService.findByNumber(shipment.getNumber())).thenReturn(shipment);
        when(itemRepository.findByShipmentIdAndReplacedItemIdAndSku(shipment.getId(), null, item1.getSku()))
                .thenReturn(item1);

        itemSndService.deleteReplacement(shipment.getNumber(), item1.getSku(), replacement1.getSku());
    }

}
