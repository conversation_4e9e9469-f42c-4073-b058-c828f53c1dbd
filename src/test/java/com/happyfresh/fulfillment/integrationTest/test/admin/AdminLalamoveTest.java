package com.happyfresh.fulfillment.integrationTest.test.admin;

import com.happyfresh.fulfillment.common.property.LalamoveProperty;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.common.MockServerHelper;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.lalamove.service.LalamoveDeliveryMessagingService;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.time.LocalDateTime;

import static org.hamcrest.Matchers.equalTo;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.method;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.requestTo;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withSuccess;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

public class AdminLalamoveTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private MockServerHelper mockServerHelper;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private LalamoveDeliveryFactory lalamoveDeliveryFactory;

    @Autowired
    private LalamoveProperty lalamoveProperty;

    @Autowired
    private LalamoveServiceTypeFactory lalamoveServiceTypeFactory;

    @Autowired
    private ItemFactory itemFactory;

    @MockBean
    private LalamoveDeliveryMessagingService lalamoveDeliveryMessagingService;

    private User admin;
    private User shopper;
    private StockLocation stockLocation;
    private Slot slot;
    private Shipment shipment;

    @Override
    protected void setupElasticSearchBeforeEachTests() {
        elasticSearchSetupService.setPreFillSimpleRouteEdges(false); // Prevent prefill ES
    }

    @Before
    public void init() {
        admin = userFactory.createUserData(Role.Name.ADMIN);
        shopper = userFactory.createUserData(Role.Name.SHOPPER, admin.getTenant());
        stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin).get(0);
        slot = slotFactory.createSlot(stockLocation, admin);

        shipment = shipmentFactory.createShipment(slot, admin, "Order-1", "Shipment-1", Shipment.State.READY, 1);
        shipment.getItems().forEach(item -> {
            item.setRequestedQty(1);
            item.setWeight(600.0);
            item.setHeight(100.0);
            item.setWidth(100.0);
            item.setDepth(300.0);
            itemFactory.save(item);
        });
    }

    @Test
    public void updateDeliveryStatus_fromInitialToOrderPlaced_shouldSucceed() throws Exception {
        batchFactory.createBatch(admin, shopper, shipment, slot, Batch.Type.SHOPPING, Job.State.STARTED);
        batchFactory.createTplDeliveryBatch(admin, shipment, slot, Batch.TplType.LALAMOVE, Job.State.INITIAL);

        LalamoveDelivery delivery = new LalamoveDelivery();
        delivery.setTenant(admin.getTenant());
        delivery.setCreatedBy(admin.getId());
        delivery.setShipment(shipment);
        delivery.setStatus(LalamoveDelivery.Status.INITIAL); // should be retried
        delivery.setScheduleAt(LocalDateTime.now().plusMinutes(lalamoveProperty.getRetryTimeoutOffset() + 1));
        lalamoveDeliveryFactory.save(delivery);

        lalamoveServiceTypeFactory.createServiceTypes(admin, stockLocation);
        // Mock - Get Quotation Request
        String uri = lalamoveProperty.getBaseUrl() + "/v2/quotations";
        String responseBody = "{ \"totalFee\": \"35000\", \"totalFeeCurrency\": \"IDR\" }";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer.expect(ExpectedCount.max(2), requestTo(uri))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(responseBody));
        // Mock - Place Order Request
        uri = lalamoveProperty.getBaseUrl() + "/v2/orders";
        responseBody = "{ \"customerOrderId\": \"LALAMOVE-DEPRECATED-ID\", \"orderRef\": \"LALAMOVE-111\" }";
        mockServer.expect(ExpectedCount.max(1), requestTo(uri))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(responseBody));

        mvc.perform(MockMvcRequestBuilders.post("/api/admin/lalamove/update_status")
            .header("X-Fulfillment-User-Token", admin.getToken())
            .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken())
            .contentType(MediaType.APPLICATION_JSON)
            .content("{ \"order_number\":\"Order-1\" }")
        )
        .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
        .andExpect(jsonPath("$.lalamove_delivery.external_order_id", equalTo("LALAMOVE-111")))
        .andExpect(jsonPath("$.lalamove_delivery.status", equalTo("ORDER_PLACED")));
    }

    @Test
    public void updateDeliveryStatus_fromAssigningDriverToOnGoing_shouldSucceed() throws Exception {
        batchFactory.createBatch(admin, shopper, shipment, slot, Batch.Type.SHOPPING, Job.State.STARTED);
        batchFactory.createTplDeliveryBatch(admin, shipment, slot, Batch.TplType.LALAMOVE, Job.State.DELIVERING);

        LalamoveDelivery delivery = new LalamoveDelivery();
        delivery.setTenant(admin.getTenant());
        delivery.setCreatedBy(admin.getId());
        delivery.setShipment(shipment);
        delivery.setStatus(LalamoveDelivery.Status.ASSIGNING_DRIVER);
        delivery.setExternalOrderId("LALAMOVE-111");
        delivery.setScheduleAt(LocalDateTime.of(2020, 4, 1, 10, 0));
        lalamoveDeliveryFactory.save(delivery);

        lalamoveServiceTypeFactory.createServiceTypes(admin, stockLocation);
        // Mock - Get Quotation Request
        String uri = lalamoveProperty.getBaseUrl() + "/v2/orders/LALAMOVE-111";
        String responseBody = "{ \"status\": \"ON_GOING\", \"price\": { \"amount\": \"38000\", \"currency\": \"IDR\" }, \"driverId\": \"123\" }";
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer.expect(ExpectedCount.max(1), requestTo(uri))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(responseBody));
        uri = lalamoveProperty.getBaseUrl() + "/v2/orders/LALAMOVE-111/drivers/123";
        responseBody = "{  \"name\": \"David\",  \"phone\": \"0978787878\",  \"plateNumber\": \"SG-9393\",  \"photo\": \"https://photo-url.web/driver123.jpg\"}";
        mockServer.expect(ExpectedCount.max(1), requestTo(uri))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(responseBody));

        mvc.perform(MockMvcRequestBuilders.post("/api/admin/lalamove/update_status")
                .header("X-Fulfillment-User-Token", admin.getToken())
                .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content("{ \"order_number\":\"Order-1\" }")
        )
        .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
        .andExpect(jsonPath("$.lalamove_delivery.external_order_id", equalTo("LALAMOVE-111")))
        .andExpect(jsonPath("$.lalamove_delivery.status", equalTo("ON_GOING")));
    }

    @Test
    public void updateDeliveryStatus_beforeLalamoveDeliveryCreated() throws Exception {
        batchFactory.createBatch(admin, shopper, shipment, slot, Batch.Type.SHOPPING, Job.State.INITIAL);
        batchFactory.createTplDeliveryBatch(admin, shipment, slot, Batch.TplType.LALAMOVE, Job.State.DELIVERING);

        mvc.perform(MockMvcRequestBuilders.post("/api/admin/lalamove/update_status")
                .header("X-Fulfillment-User-Token", admin.getToken())
                .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content("{ \"order_number\":\"Order-1\" }")
        )
        .andExpect(MockMvcResultMatchers.status().isNotFound());
    }

}
