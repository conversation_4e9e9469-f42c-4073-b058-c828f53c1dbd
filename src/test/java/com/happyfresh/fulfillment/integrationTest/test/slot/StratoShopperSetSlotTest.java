package com.happyfresh.fulfillment.integrationTest.test.slot;

import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.property.StratoProperty;
import com.happyfresh.fulfillment.common.util.DateTimeUtil;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.common.MockServerHelper;
import com.happyfresh.fulfillment.integrationTest.test.common.SetSlotHelper;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.BatchRepository;
import org.elasticsearch.common.geo.GeoPoint;
import org.hamcrest.Matchers;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static org.springframework.test.web.client.match.MockRestRequestMatchers.method;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.requestTo;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withStatus;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withSuccess;

public class StratoShopperSetSlotTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private ShiftFactory shiftFactory;

    @Autowired
    private ClusterFactory clusterFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private ShipmentJsonObjectFactory shipmentJsonObjectFactory;

    @Autowired
    private MockServerHelper mockServerHelper;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private SetSlotHelper helper;

    @Autowired
    private StratoProperty stratoProperty;

    @Autowired
    @Qualifier("AllowGetBodyRestTemplate")
    private RestTemplate customRestTemplate;

    @Autowired
    private BatchRepository batchRepository;

    private User admin;
    private StockLocation stockLocation1;
    private List<Slot> slots;
    private Slot slot1;
    private Slot slot2;

    @Before
    public void setUp() throws InterruptedException, JSONException {
        admin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        // StockLocation should be fulfilled by Strato
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin);
        stockLocation1 = stockLocations.get(0);
        stockLocation1.setEnabler(StockLocation.Enabler.HFC);
        stockLocation1.setEnablerPlatform(StockLocation.EnablerPlatform.STRATO);
        stockLocationFactory.save(stockLocation1);
        // Cluster should be DDS
        Cluster cluster = stockLocation1.getCluster();
        cluster.setSlotType(Slot.Type.LONGER_DELIVERY);
        clusterFactory.save(cluster);
        // No need for Shopper Shift
        LocalDateTime slotStart = LocalDateTime.now().withSecond(0).withNano(0);
        slots = slotFactory.createLongerDeliverySlots(stockLocation1, admin, 2, 2, slotStart);
        slot1 = slots.get(0);
        slot2 = slots.get(1);
        shiftFactory.createShift(stockLocation1, admin, Shift.Type.DRIVER, slots.get(0).getStartTime(), slots.get(slots.size()-1).getEndTime(), 2);
    }

    @Test
    public void shouldSucceed_onStratoBookResponseTrue() throws Exception {
        MockRestServiceServer customMockServer = mockServerHelper.buildMockServer(customRestTemplate);
        mockSuccessGetCapacity(customMockServer, slot1.getId(), slot2.getId());
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockSuccessBookCapacity(mockServer, slot1, true);
        verifySetSlot(slot1, true, "R-1");
        customMockServer.verify();
        mockServer.verify();

        // Should create shopping Batch & Job without shift and vehicle
        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            Assert.assertThat(shoppingBatches, Matchers.hasSize(1));
            Batch shoppingBatch = shoppingBatches.get(0);
            Assert.assertNull(shoppingBatch.getVehicle());
            Assert.assertNull(shoppingBatch.getShift());
            Assert.assertEquals(1, shoppingBatch.getJobs().size());
            LocalDateTime slotStart = slot1.getStartTime().minusMinutes(slot1.durationInMinutes());
            LocalDateTime slotEnd = slot1.getStartTime();
            Assert.assertEquals(slotStart, shoppingBatch.getStartTime());
            Assert.assertEquals(slotEnd, shoppingBatch.getEndTime());
        });
    }

    @Test
    public void shouldNotPoolShoppingBatch_onSuccess() throws Exception {
        MockRestServiceServer customMockServer = mockServerHelper.buildMockServer(customRestTemplate);
        mockSuccessGetCapacity(customMockServer, slot1.getId(), slot2.getId());
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockSuccessBookCapacity(mockServer, slot1, true);
        verifySetSlot(slot1, true, "R-1");
        customMockServer.verify();
        mockServer.verify();

        customMockServer = mockServerHelper.buildMockServer(customRestTemplate);
        mockSuccessGetCapacity(customMockServer, slot1.getId(), slot2.getId());
        mockServer = mockServerHelper.buildMockServer();
        mockSuccessBookCapacity(mockServer, slot1, true);
        verifySetSlot(slot1, true, "R-2");
        customMockServer.verify();
        mockServer.verify();

        // should create 2 shopping batch (not pooled) without shift and vehicle
        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            Assert.assertThat(shoppingBatches, Matchers.hasSize(2));

            Batch shoppingBatch1 = shoppingBatches.get(0);
            Assert.assertNull(shoppingBatch1.getVehicle());
            Assert.assertNull(shoppingBatch1.getShift());
            Assert.assertEquals(1, shoppingBatch1.getJobs().size());
            LocalDateTime slotStart = slot1.getStartTime().minusMinutes(slot1.durationInMinutes());
            LocalDateTime slotEnd = slot1.getStartTime();
            Assert.assertEquals(slotStart, shoppingBatch1.getStartTime());
            Assert.assertEquals(slotEnd, shoppingBatch1.getEndTime());

            Batch shoppingBatch2 = shoppingBatches.get(1);
            Assert.assertNull(shoppingBatch2.getVehicle());
            Assert.assertNull(shoppingBatch2.getShift());
            Assert.assertEquals(1, shoppingBatch2.getJobs().size());
            slotStart = slot1.getStartTime().minusMinutes(slot1.durationInMinutes());
            slotEnd = slot1.getStartTime();
            Assert.assertEquals(slotStart, shoppingBatch2.getStartTime());
            Assert.assertEquals(slotEnd, shoppingBatch2.getEndTime());
        });
    }

    @Test
    public void shouldSucceed_onMoveSlot() throws Exception {
        MockRestServiceServer customMockServer = mockServerHelper.buildMockServer(customRestTemplate);
        mockSuccessGetCapacity(customMockServer, slot1.getId(), slot2.getId());
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockSuccessBookCapacity(mockServer, slot1, true);
        verifySetSlot(slot1, true, "R-1");
        customMockServer.verify();
        mockServer.verify();

        customMockServer = mockServerHelper.buildMockServer(customRestTemplate);
        mockSuccessGetCapacity(customMockServer, slot1.getId(), slot2.getId());
        mockServer = mockServerHelper.buildMockServer();
        mockSuccessBookCapacity(mockServer, slot2, true);
        verifySetSlot(slot2, true, "R-1");
        customMockServer.verify();
        mockServer.verify();

        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            Assert.assertThat(shoppingBatches, Matchers.hasSize(1));
            Batch shoppingBatch1 = shoppingBatches.get(0);
            Assert.assertNull(shoppingBatch1.getVehicle());
            Assert.assertNull(shoppingBatch1.getShift());
            Assert.assertEquals(1, shoppingBatch1.getJobs().size());
            LocalDateTime slotStart = slot2.getStartTime().minusMinutes(slot2.durationInMinutes());
            LocalDateTime slotEnd = slot2.getStartTime();
            Assert.assertEquals(slotStart, shoppingBatch1.getStartTime());
            Assert.assertEquals(slotEnd, shoppingBatch1.getEndTime());
        });
    }

    @Test
    public void shouldUseDefaultDdsChain_onNonStratoStockLocation() throws Exception {
        stockLocation1.setEnabler(null);
        stockLocation1.setEnablerPlatform(null);
        stockLocationFactory.save(stockLocation1);
        Shift shopperShift = shiftFactory.createShift(stockLocation1, admin, Shift.Type.SHOPPER, slots.get(0).getStartTime().minusHours(2), slot2.getEndTime().minusHours(2), 1);
        verifySetSlot(slots.get(0), true, "R-1");
        // verify DDS shopping & delivery batch
        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            Assert.assertThat(shoppingBatches, Matchers.hasSize(1));
            Batch shoppingBatch = shoppingBatches.get(0);
            Assert.assertEquals(1, (int) shoppingBatch.getVehicle());
            Assert.assertEquals(shopperShift.getId(), shoppingBatch.getShift().getId());
            Assert.assertEquals(1, shoppingBatch.getJobs().size());
        });
    }

    @Test
    public void shouldFailed_onStratoBookResponseFalse() throws Exception {
        MockRestServiceServer customMockServer = mockServerHelper.buildMockServer(customRestTemplate);
        mockSuccessGetCapacity(customMockServer, slot1.getId(), slot2.getId());
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockSuccessBookCapacity(mockServer, slot2, false);
        verifySetSlot(slot2, false, "R-1");
        customMockServer.verify();
        mockServer.verify();

        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            Assert.assertEquals(0, shoppingBatches.size());
        });
    }

    @Test
    public void shouldFailed_onStratoBookResponseError() throws Exception {
        MockRestServiceServer customMockServer = mockServerHelper.buildMockServer(customRestTemplate);
        mockSuccessGetCapacity(customMockServer, slots.get(0).getId(), slot2.getId());
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockErrorBookCapacity(mockServer, HttpStatus.INTERNAL_SERVER_ERROR);
        verifySetSlot(slot2, false, "R-1");
        customMockServer.verify();
        mockServer.verify();

        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            Assert.assertEquals(0, shoppingBatches.size());
        });
    }

    @Test
    public void shouldFailed_onStratoGetCapacityResponseError() throws Exception {
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer(customRestTemplate);
        mockErrorGetCapacity(mockServer, HttpStatus.GATEWAY_TIMEOUT);
        verifySetSlot(slot2, false, "R-1");
        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            Assert.assertEquals(0, shoppingBatches.size());
        });
    }

    @Test
    public void shouldUnavailable_withStratoUnexpectedNullResponse() throws Exception {
        MockRestServiceServer customMockServer = mockServerHelper.buildMockServer(customRestTemplate);
        mockSuccessGetCapacity(customMockServer, slots.get(0).getId(), slot2.getId());
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        String uri = stratoProperty.getBaseUrl() + "/store/" + stockLocation1.getExternalId() + "/capacity/book";
        mockServer.expect(ExpectedCount.max(1), requestTo(uri))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body("[]"));

        verifySetSlot(slot2, false, "R-1");
        customMockServer.verify();
        mockServer.verify();
    }

    private void verifySetSlot(Slot slot, Boolean isAvailable, String orderNumber) throws Exception {
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation1, slot.getId(), 3, orderNumber, new GeoPoint(-6.291202, 106.7855), "Lintasarta");
        helper.assertSetRegularSlot(isAvailable, shipmentObj, admin);
    }

    private void mockSuccessGetCapacity(MockRestServiceServer mockServer, Long... availableSlotIds) throws JSONException {
        List<Long> availableSlotIdList = Arrays.stream(availableSlotIds).collect(Collectors.toList());

        String uri = stratoProperty.getBaseUrl() + "/store/" + stockLocation1.getExternalId() + "/capacity";
        JSONArray respArray = new JSONArray();
        for (Slot slot : slots) {
            JSONObject respObj = new JSONObject();
            respObj.put("delivery_slot", DateTimeUtil.localDateTimeToStratoDateTimeString(slot.getStartTime()));
            respObj.put("book_available", availableSlotIdList.contains(slot.getId()));
            respArray.put(respObj);
        }
        mockServer.expect(ExpectedCount.min(1), requestTo(uri))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(respArray.toString()));
    }

    private void mockErrorGetCapacity(MockRestServiceServer mockServer, HttpStatus httpStatus) {
        String uri = stratoProperty.getBaseUrl() + "/store/" + stockLocation1.getExternalId() + "/capacity";
        mockServer.expect(ExpectedCount.max(1), requestTo(uri))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withStatus(httpStatus)
                        .contentType(MediaType.APPLICATION_JSON).body("Strato default error message"));
    }

    private void mockSuccessBookCapacity(MockRestServiceServer mockServer, Slot slot, Boolean available) throws JSONException {
        String uri = stratoProperty.getBaseUrl() + "/store/" + stockLocation1.getExternalId() + "/capacity/book";

        JSONObject respObj = new JSONObject();
        respObj.put("delivery_slot", DateTimeUtil.localDateTimeToStratoDateTimeString(slot.getStartTime()));
        respObj.put("book_available", available);

        mockServer.expect(ExpectedCount.max(1), requestTo(uri))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withSuccess().contentType(MediaType.APPLICATION_JSON).body(respObj.toString()));
    }

    private void mockErrorBookCapacity(MockRestServiceServer mockServer, HttpStatus httpStatus) {
        String uri = stratoProperty.getBaseUrl() + "/store/" + stockLocation1.getExternalId() + "/capacity/book";
        mockServer.expect(ExpectedCount.max(1), requestTo(uri))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withStatus(httpStatus)
                        .contentType(MediaType.APPLICATION_JSON).body("Strato default error message"));
    }
}
