package com.happyfresh.fulfillment.integrationTest.test.slot;

import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.util.DateTimeUtil;
import com.happyfresh.fulfillment.enabler.model.StratoShopperCapacity;
import com.happyfresh.fulfillment.enabler.service.StratoService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.*;
import com.happyfresh.fulfillment.shipment.service.ShipmentService;
import com.happyfresh.fulfillment.slot.service.SlotOptimizationService;
import com.happyfresh.fulfillment.user.service.UserService;
import org.elasticsearch.common.geo.GeoPoint;
import org.hamcrest.Matchers;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class SetSlotLongerDeliveryTest extends BaseTest {

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private ShipmentJsonObjectFactory shipmentJsonObjectFactory;

    @Autowired
    private ShiftFactory shiftFactory;

    @Autowired
    private ClusterFactory clusterFactory;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private LalamoveServiceTypeFactory lalamoveServiceTypeFactory;

    @Autowired
    private JobFactory jobFactory;

    @Autowired
    private ShipmentService shipmentService;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private ItemRepository itemRepository;

    @Autowired
    private ClusterRepository clusterRepository;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private SlotOptimizationService slotOptimizationService;

    @Autowired
    private UserService userService;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private LalamoveDeliveryFactory lalamoveDeliveryFactory;

    @MockBean
    private StratoService stratoService;

    @Before
    public void setUp() throws InterruptedException{
        Thread.sleep(500);
    }

    private MvcResult setSlotRequest(JSONObject shipmentObj, User user) throws Exception {
        return mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andReturn();
    }

    @Test
    public void shouldCreateNonPoolingBatch() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user, Slot.Type.LONGER_DELIVERY).get(0);
        List<GeoPoint> addresses = Lists.newArrayList(new GeoPoint( -6.2915547, 106.7977159), new GeoPoint(-6.291033, 106.817397), new GeoPoint(-6.291202, 106.7855));
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 3, 1);
        Slot slot = slots.get(0);
        int numberOfDriver = 3;
        Shift shopperShift = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime(), slot.getStartTime().plusHours(8), 1);
        Shift driverShift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime(), slot.getStartTime().plusHours(10), numberOfDriver);

        int numberOfShipment = 12;

        for (int i = 0; i < numberOfShipment; i++) {
            Thread.sleep(100);
            JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 1, "Order-" + i, addresses.get(i%3), "Address " + i);
            MvcResult mvcResults = setSlotRequest(shipmentObject, user);
            Assert.assertEquals(200, mvcResults.getResponse().getStatus());
            final int iteration = i;
            printPlan(user, stockLocation, slot.getStartTime());
            transactionHelper.withNewTransaction(() -> {
                List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
                Assert.assertThat(shoppingBatches, Matchers.hasSize(iteration + 1));
                Batch shoppingBatch = shoppingBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
                Assert.assertEquals(1, (int) shoppingBatch.getVehicle());
                Assert.assertFalse(shoppingBatch.isFlagOptimized());
                Assert.assertEquals(shopperShift.getId(), shoppingBatch.getShift().getId());

                List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
                Assert.assertThat(deliveryBatches, Matchers.hasSize(iteration + 1));
                Batch deliveryBatch = deliveryBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
                Assert.assertFalse(deliveryBatch.isFlagOptimized());
                Assert.assertEquals((iteration % numberOfDriver) + 1, (int) deliveryBatch.getVehicle());
                Assert.assertEquals(driverShift.getId(), deliveryBatch.getShift().getId());

                List<Shipment> shipments = shipmentRepository.findAll();
                for(Shipment shipment: shipments)
                    Assert.assertEquals(false, shipment.getIsExpress());
            });
        }

        transactionHelper.withNewTransaction(() -> {
            // This will make pooling maximum 2 order, because of volume constraint
            for (int i = 0; i < numberOfShipment; i++) {
                Shipment shipment = shipmentRepository.findByNumber("Order-" + i);
                shipment.setState(Shipment.State.READY);
                shipmentRepository.save(shipment);
                Item item = shipment.getItems().get(0);
                item.setDepth(200.0);
                item.setHeight(20.0);
                item.setWidth(10.0);
                itemRepository.save(item);
            }
        });
        Thread.sleep(200);
        try {
            AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(user.getToken(), user.getTenant().getToken());
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            slotOptimizationService.shopperSlotOptimization(slots.get(0), shopperShift);
            slotOptimizationService.driverSlotOptimization(slots.get(0));
        } finally {
            SecurityContextHolder.clearContext();
        }
        printPlan(user, stockLocation, slots.get(0).getStartTime());
    }

    @Test
    public void shouldCreateTplBatch() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user, Slot.Type.LONGER_DELIVERY).get(0);
        stockLocation.setTplEnabled(true);
        stockLocation.setShipDistanceThreshold(1.0);
        stockLocationFactory.save(stockLocation);

        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 3, 1);
        Slot slot = slots.get(0);
        Shift shopperShift = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().minusHours(2), slot.getStartTime().plusHours(8), 1);
        Shift driverShift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime(), slot.getStartTime().plusHours(10), 1);

        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 1, "Order-1", new GeoPoint(-6.2877476, 106.7704242), "Bank BRI Pasar Jumat");
        MvcResult mvcResult = setSlotRequest(shipmentObj, user);

        Assert.assertEquals(200, mvcResult.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            Assert.assertThat(deliveryBatches, Matchers.hasSize(1));
            Batch batch = deliveryBatches.get(0);
            Assert.assertEquals(Batch.DeliveryType.TPL, batch.getDeliveryType());
            Assert.assertEquals(slot.getStartTime(), batch.getStartTime());
            Assert.assertEquals(slot.getEndTime(), batch.getEndTime());
        });
    }

    @Test
    public void shouldAssignToEarliestLatestBatchCase1() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);

        /** Cluster 1 */
        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 3, 3);
        Slot slot = slots.get(0);
        Shift shift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime(), slots.get(2).getEndTime(), 1);
        Shift shiftShopper = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().minusHours(2), slot.getStartTime().plusHours(16), 1);
        List<Batch> batches = batchFactory.createBatches(1, user, slot);
        List<Batch> initialShoppingBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).collect(Collectors.toList());
        List<Batch> initialDeliveryBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).collect(Collectors.toList());

        Batch batch1 = initialDeliveryBatches.get(0);
        batch1.setEndTime(batch1.getStartTime().plusMinutes(160));
        batch1.setVehicle(1);
        batch1.setShift(shift);

        Batch shoppingBatch1 = initialShoppingBatches.get(0);
        shoppingBatch1.setStartTime(shiftShopper.getStartTime());
        shoppingBatch1.setEndTime(shoppingBatch1.getStartTime().plusMinutes(160));
        shoppingBatch1.setVehicle(1);
        shoppingBatch1.setShift(shiftShopper);

        StockLocation stockLocation2 = stockLocations.get(1);
        List<Slot> slots2 = slotFactory.createLongerDeliverySlots(stockLocation2, user, 3, 3);
        Slot slot2 = slots2.get(0);
        final Shift shift2 = shiftFactory.createShift(stockLocation2, user, Shift.Type.DRIVER, slot2.getStartTime(), slots2.get(2).getEndTime(), 2);
        Shift shiftShopper2 = shiftFactory.createShift(stockLocation2, user, Shift.Type.SHOPPER, slot2.getStartTime().minusHours(2), slot2.getStartTime().plusHours(16), 3);
        List<Batch> batches2 = batchFactory.createBatches(2, user, slot2);
        List<Batch> deliveryBatches2 = batches2.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).collect(Collectors.toList());

        Batch batch2 = deliveryBatches2.get(0);
        batch2.setEndTime(batch2.getStartTime().plusMinutes(80));
        batch2.setVehicle(2);
        batch2.setShift(shift2);

        Batch batch3 = deliveryBatches2.get(1);
        batch3.setEndTime(batch3.getStartTime().plusMinutes(90));
        batch3.setVehicle(3);
        batch3.setShift(shift2);

        Batch shoppingBatch2 = initialShoppingBatches.get(0);
        shoppingBatch1.setStartTime(shiftShopper.getStartTime());
        shoppingBatch1.setEndTime(shoppingBatch1.getStartTime().plusMinutes(160));
        shoppingBatch1.setVehicle(1);
        shoppingBatch1.setShift(shiftShopper);

        Batch shoppingBatch3 = initialShoppingBatches.get(0);
        shoppingBatch1.setStartTime(shiftShopper.getStartTime());
        shoppingBatch1.setEndTime(shoppingBatch1.getStartTime().plusMinutes(160));
        shoppingBatch1.setVehicle(1);
        shoppingBatch1.setShift(shiftShopper);

        batchFactory.saveAll(Lists.newArrayList(batch1, shoppingBatch1, batch2, batch3));

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 1, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            List<Batch> deliveryBatchesFinal = batchRepository.findByType(Batch.Type.DELIVERY);
            Assert.assertThat(deliveryBatchesFinal, Matchers.hasSize(4));
            Batch batch = deliveryBatchesFinal.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
            Assert.assertEquals(2, (int) batch.getVehicle());
            Assert.assertEquals(shift2.getId(), batch.getShift().getId());
        });
        printPlan(user, stockLocation, slot.getStartTime());
    }

    @Test
    public void shouldAssignToEarliestLatestBatchCase2() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);

        /** Cluster 1 */
        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 3, 3);
        Slot slot = slots.get(0);
        int numberOfDriver = 2;
        Shift shiftDriver = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime(), slots.get(2).getEndTime(), numberOfDriver);
        Shift shiftShopper = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().minusHours(2), slot.getStartTime().plusHours(16), 1);


        List<Batch> batches = batchFactory.createBatches(3, user, slot);
        List<Batch> initialShoppingBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).collect(Collectors.toList());
        List<Batch> initialDeliveryBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).collect(Collectors.toList());

        Batch batch1 = initialDeliveryBatches.get(0);
        batch1.setEndTime(batch1.getStartTime().plusMinutes(120));
        batch1.setVehicle(1);
        batch1.setShift(shiftDriver);

        Batch shoppingBatch1 = initialShoppingBatches.get(0);
        shoppingBatch1.setStartTime(shiftShopper.getStartTime());
        shoppingBatch1.setEndTime(shiftShopper.getStartTime().plusMinutes(80));
        shoppingBatch1.setVehicle(1);
        shoppingBatch1.setShift(shiftShopper);

        Batch batch2 = initialDeliveryBatches.get(1);
        batch2.setStartTime(batch1.getEndTime());
        batch2.setEndTime(batch2.getStartTime().plusMinutes(20));
        batch2.setVehicle(1);
        batch2.setShift(shiftDriver);

        Batch shoppingBatch2 = initialShoppingBatches.get(0);
        shoppingBatch2.setStartTime(shoppingBatch1.getStartTime());
        shoppingBatch2.setEndTime(shoppingBatch2.getStartTime().plusMinutes(30));
        shoppingBatch2.setVehicle(1);
        shoppingBatch2.setShift(shiftShopper);

        Batch batch3 = initialDeliveryBatches.get(2);
        batch3.setEndTime(batch3.getStartTime().plusMinutes(90));
        batch3.setVehicle(2);
        batch3.setShift(shiftDriver);

        Batch shoppingBatch3 = initialShoppingBatches.get(0);
        shoppingBatch3.setStartTime(shoppingBatch2.getStartTime());
        shoppingBatch3.setEndTime(shoppingBatch3.getStartTime().plusMinutes(20));
        shoppingBatch3.setVehicle(1);
        shoppingBatch3.setShift(shiftShopper);

        batchFactory.saveAll(Lists.newArrayList(batch1, batch2, batch3, shoppingBatch1, shoppingBatch2, shoppingBatch3));

        int numberOfShipment = 4;

        for (int i = 0; i < numberOfShipment; i++) {
            JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 1, "Order-" + i, new GeoPoint(-6.2925049, 106.821986), "Address " + i);
            MvcResult mvcResults = setSlotRequest(shipmentObject, user);
            Assert.assertEquals(200, mvcResults.getResponse().getStatus());
            final int iteration = i;
            transactionHelper.withNewTransaction(() -> {
                List<Batch> deliveryBatchesFinal = batchRepository.findByType(Batch.Type.DELIVERY);
                Assert.assertThat(deliveryBatchesFinal, Matchers.hasSize(iteration + 4));
                Batch batch = deliveryBatchesFinal.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
                Assert.assertEquals(iteration < 3 ? 2 : 1, (int) batch.getVehicle());
                Assert.assertEquals(shiftDriver.getId(), batch.getShift().getId());
            });
        }
    }

    @Ignore("Need to be fixed and re-tested later")
    @Test
    public void shouldAssignToEarliestLatestBatchCase3() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, user.getTenant());
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user, Slot.Type.LONGER_DELIVERY);

        /** Cluster 1 */
        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 3, 3);
        Slot slot1 = slots.get(0);
        Slot slot2 = slots.get(1);
        Slot slot3 = slots.get(2);
        Shift shift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot1.getStartTime(), slot3.getEndTime(), 1);
        Shift shiftShopper = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot1.getStartTime().minusHours(2), slot3.getEndTime().minusHours(2), 1);
        List<Batch> batches = batchFactory.createBatches(1, user, slot1);
        List<Batch> initialShoppingBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).collect(Collectors.toList());
        List<Batch> initialDeliveryBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).collect(Collectors.toList());

        Batch batch1 = initialDeliveryBatches.get(0);
        batch1.setEndTime(batch1.getStartTime().plusMinutes(160));
        batch1.setVehicle(1);
        batch1.setShift(shift);
        batchRepository.save(batch1);

        Batch shoppingBatch1 = initialShoppingBatches.get(0);
        shoppingBatch1.setStartTime(shiftShopper.getStartTime());
        shoppingBatch1.setEndTime(shiftShopper.getStartTime().plusMinutes(160));
        shoppingBatch1.setVehicle(1);
        shoppingBatch1.setShift(shiftShopper);
        shoppingBatch1.setUser(user);
        batchRepository.save(shoppingBatch1);

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slot1.getId(), 1, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatchesFinal = batchRepository.findByType(Batch.Type.SHOPPING);
            Assert.assertThat(shoppingBatchesFinal, Matchers.hasSize(2));

            Shipment shipment = shipmentRepository.findByOrderNumber("Order-1");
            Batch batch = shipment.getShoppingJob().get().getBatch();
            Assert.assertNotEquals(shiftShopper.getStartTime(), batch.getStartTime());
        });
    }

    @Test
    public void shouldAssignToEarliestBatchWithShiftWithinSlotAndDeliveryTimeCase1() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);

        /** Cluster 1 */
        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 3, 3);
        Slot slot = slots.get(1);
        int numberOfDriver = 3;
        Shift shiftDriver1 = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime().minusHours(2), slot.getStartTime().plusMinutes(10), numberOfDriver);
        Shift shiftShopper1 = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().minusHours(3), slot.getStartTime().plusHours(1), 1);

        final Shift shiftDriver2 = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime().plusMinutes(10), slot.getStartTime().plusHours(4), numberOfDriver);
        Shift shiftShopper2 = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().plusHours(1), slot.getStartTime().plusHours(5), 3);

        int numberOfShipment = 3;

        for (int i = 0; i < numberOfShipment; i++) {
            Thread.sleep(100);
            JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(1).getId(), 1, "Order-" + i, new GeoPoint(-6.2925049, 106.821986), "Address " + i);
            MvcResult mvcResults = setSlotRequest(shipmentObject, user);
            Assert.assertEquals(200, mvcResults.getResponse().getStatus());
            final int iteration = i;
            transactionHelper.withNewTransaction(() -> {
                List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
                Assert.assertThat(deliveryBatches, Matchers.hasSize(iteration + 1));
                Batch batch = deliveryBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
                Assert.assertEquals((iteration % numberOfDriver) + 1, (int) batch.getVehicle());
                Assert.assertEquals(shiftDriver2.getId(), batch.getShift().getId());
            });
        }
    }

    @Test
    public void shouldAssignToEarliestBatchWithShiftWithinSlotAndDeliveryTimeCase2() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);

        /** Cluster 1 */
        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 3, 3);
        Slot slot = slots.get(1);
        int numberOfDriver = 3;
        Shift shiftDriver1 = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime().minusHours(2), slot.getStartTime().plusMinutes(10), numberOfDriver);
        Shift shiftShopper1 = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().minusHours(3), slot.getStartTime().plusHours(1), 1);

        final Shift shiftDriver2 = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime().plusMinutes(10), slot.getStartTime().plusHours(4), numberOfDriver);
        Shift shiftShopper2 = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().plusHours(1), slot.getStartTime().plusHours(5), 3);

        List<Batch> batches = batchFactory.createBatches(3, user, slot);
        List<Batch> initialShoppingBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).collect(Collectors.toList());
        List<Batch> initialDeliveryBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).collect(Collectors.toList());

        Batch batch1 = initialDeliveryBatches.get(0);
        batch1.setEndTime(batch1.getStartTime().plusMinutes(80));
        batch1.setVehicle(1);
        batch1.setShift(shiftDriver2);

        Batch shoppingBatch1 = initialShoppingBatches.get(0);
        shoppingBatch1.setStartTime(shiftShopper1.getStartTime());
        shoppingBatch1.setEndTime(shiftShopper1.getStartTime().plusMinutes(80));
        shoppingBatch1.setVehicle(1);
        shoppingBatch1.setShift(shiftShopper1);

        Batch batch2 = initialDeliveryBatches.get(1);
        batch2.setEndTime(batch2.getStartTime().plusMinutes(70));
        batch2.setVehicle(2);
        batch2.setShift(shiftDriver2);

        Batch shoppingBatch2 = initialShoppingBatches.get(1);
        shoppingBatch2.setStartTime(shoppingBatch1.getStartTime());
        shoppingBatch2.setEndTime(shoppingBatch2.getStartTime().plusMinutes(80));
        shoppingBatch2.setVehicle(1);
        shoppingBatch2.setShift(shiftShopper1);

        Batch batch3 = initialDeliveryBatches.get(2);
        batch3.setEndTime(batch3.getStartTime().plusMinutes(90));
        batch3.setVehicle(3);
        batch3.setShift(shiftDriver2);

        Batch shoppingBatch3 = initialShoppingBatches.get(2);
        shoppingBatch3.setStartTime(shoppingBatch2.getStartTime());
        shoppingBatch3.setEndTime(shoppingBatch3.getStartTime().plusMinutes(80));
        shoppingBatch3.setVehicle(1);
        shoppingBatch3.setShift(shiftShopper1);

        batchFactory.saveAll(Lists.newArrayList(batch1, batch2, batch3, shoppingBatch1, shoppingBatch2, shoppingBatch3));

        Thread.sleep(500);
        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(1).getId(), 1, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            List<Batch> deliveryBatchesFinal = batchRepository.findByType(Batch.Type.DELIVERY);
            Assert.assertThat(deliveryBatchesFinal, Matchers.hasSize(4));
            Batch batch = deliveryBatchesFinal.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
            Assert.assertEquals(2, (int) batch.getVehicle());
            Assert.assertEquals(shiftDriver2.getId(), batch.getShift().getId());
        });
    }

    @Test
    public void shouldAssignToEarliestBatchWithShiftWithinSlotAndDeliveryTimeCase3() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);

        /** Cluster 1 */
        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 3, 3);
        Slot slot = slots.get(1);
        int numberOfDriver = 3;
        Shift shiftDriver1 = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime(), slot.getStartTime().plusMinutes(50), numberOfDriver);
        Shift shiftShopper1 = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().minusHours(3), slot.getStartTime().plusHours(1), 1);

        final Shift shiftDriver2 = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime().plusMinutes(10), slot.getStartTime().plusHours(4), numberOfDriver);
        Shift shiftShopper2 = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().plusHours(1), slot.getStartTime().plusHours(5), 3);

        int numberOfShipment = 3;

        for (int i = 0; i < numberOfShipment; i++) {
            JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(1).getId(), 1, "Order-" + i, new GeoPoint(-6.2925049, 106.821986), "Address " + i);
            MvcResult mvcResults = setSlotRequest(shipmentObject, user);
            Assert.assertEquals(200, mvcResults.getResponse().getStatus());
            final int iteration = i;
            transactionHelper.withNewTransaction(() -> {
                List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
                Assert.assertThat(deliveryBatches, Matchers.hasSize(iteration + 1));
                Batch batch = deliveryBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
                Assert.assertEquals((iteration % numberOfDriver) + 1, (int) batch.getVehicle());
                Assert.assertEquals(shiftDriver1.getId(), batch.getShift().getId());
            });
        }
    }

    @Test
    public void shouldAssignToEarliestBatchWithShiftWithinSlotAndDeliveryTimeCase4() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);

        /** Cluster 1 */
        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 3, 3);
        Slot slot = slots.get(1);
        int numberOfDriver = 3;
        Shift shiftDriver1 = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime().minusHours(2), slot.getStartTime().plusMinutes(50), numberOfDriver);
        Shift shiftShopper1 = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().minusHours(3), slot.getStartTime().plusHours(1), 1);

        final Shift shiftDriver2 = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime().plusMinutes(10), slot.getStartTime().plusHours(4), numberOfDriver);
        Shift shiftShopper2 = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().plusHours(1), slot.getStartTime().plusHours(5), 3);

        List<Batch> batches = batchFactory.createBatches(3, user, slot);
        List<Batch> initialShoppingBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).collect(Collectors.toList());
        List<Batch> initialDeliveryBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).collect(Collectors.toList());

        Batch batch1 = initialDeliveryBatches.get(0);
        batch1.setEndTime(batch1.getStartTime().plusMinutes(80));
        batch1.setVehicle(1);
        batch1.setShift(shiftDriver1);

        Batch shoppingBatch1 = initialShoppingBatches.get(0);
        shoppingBatch1.setStartTime(shiftShopper1.getStartTime());
        shoppingBatch1.setEndTime(shiftShopper1.getStartTime().plusMinutes(80));
        shoppingBatch1.setVehicle(1);
        shoppingBatch1.setShift(shiftShopper1);

        Batch batch2 = initialDeliveryBatches.get(1);
        batch2.setEndTime(batch2.getStartTime().plusMinutes(70));
        batch2.setVehicle(2);
        batch2.setShift(shiftDriver1);

        Batch shoppingBatch2 = initialShoppingBatches.get(1);
        shoppingBatch2.setStartTime(shoppingBatch1.getStartTime());
        shoppingBatch2.setEndTime(shoppingBatch2.getStartTime().plusMinutes(80));
        shoppingBatch2.setVehicle(1);
        shoppingBatch2.setShift(shiftShopper1);

        Batch batch3 = initialDeliveryBatches.get(2);
        batch3.setEndTime(batch3.getStartTime().plusMinutes(90));
        batch3.setVehicle(3);
        batch3.setShift(shiftDriver1);

        Batch shoppingBatch3 = initialShoppingBatches.get(2);
        shoppingBatch3.setStartTime(shoppingBatch2.getStartTime());
        shoppingBatch3.setEndTime(shoppingBatch3.getStartTime().plusMinutes(80));
        shoppingBatch3.setVehicle(1);
        shoppingBatch3.setShift(shiftShopper1);

        batchFactory.saveAll(Lists.newArrayList(batch1, batch2, batch3, shoppingBatch1, shoppingBatch2, shoppingBatch3));

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(1).getId(), 1, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            List<Batch> deliveryBatchesFinal = batchRepository.findByType(Batch.Type.DELIVERY);
            Assert.assertThat(deliveryBatchesFinal, Matchers.hasSize(4));
            Batch batch = deliveryBatchesFinal.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
            Assert.assertEquals(1, (int) batch.getVehicle());
            Assert.assertEquals(shiftDriver2.getId(), batch.getShift().getId());
        });
    }

    @Test
    public void shouldSetShoppingBatchToCurrentTimeAndDeliveryBatchAfterShoppingBatch() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user, Slot.Type.LONGER_DELIVERY);

        /** Cluster 1 */
        StockLocation stockLocation = stockLocations.get(0);
        Slot slot1 = slotFactory.createLongerDeliverySlot(stockLocation, user, 0, -2, 4);
        Slot slot2 = slotFactory.createLongerDeliverySlot(stockLocation, user, 0, 3, 4);
        Shift shiftDriver1 = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot1.getStartTime(), slot2.getEndTime(), 1);
        Shift shiftShopper1 = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot1.getStartTime().minusHours(2), slot2.getStartTime().minusHours(1), 1);

        int numberOfShipment = 2;
        for (int i = 0; i < numberOfShipment; i++) {
            JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slot1.getId(), 1, "Order-" + i, new GeoPoint(-6.2925049, 106.821986), "Address " + i);
            MvcResult mvcResults = setSlotRequest(shipmentObject, user);
            Assert.assertEquals(200, mvcResults.getResponse().getStatus());
            final int iteration = i;
            transactionHelper.withNewTransaction(() -> {
                for (int j = 0; j <= iteration; j++) {
                    Shipment shipment = shipmentRepository.findByOrderNumber("Order-"+j);
                    Batch shoppingBatch = shipment.getShoppingJob().get().getBatch();
                    Batch deliveryBatch = shipment.getDeliveryJob().get().getBatch();
                    if (j == 0)
                        Assert.assertTrue(shoppingBatch.getEndTime().isEqual(deliveryBatch.getStartTime()));
                    else
                        Assert.assertTrue(shoppingBatch.getEndTime().isBefore(deliveryBatch.getStartTime()));
                }
            });
        }
    }

    @Test
    public void shouldCreateNonPoolingBatchWhenDeliveryVolumeIsGreaterThanThreshold() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user, Slot.Type.LONGER_DELIVERY).get(0);
        stockLocation.setMaxDeliveryVolume(1);
        stockLocationFactory.save(stockLocation);

        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 3, 1);
        Slot slot = slots.get(0);
        Shift shift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime(), slot.getStartTime().plusHours(10), 1);
        Shift shiftShopper = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().minusHours(2), slot.getStartTime().plusHours(16), 1);

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(shipmentJsonObjectFactory.createItemObject(0, "Barang-01-ID", 1, 1, 100, 100, 100));
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), "Order-1", new GeoPoint(-6.2877476, 106.7704242), "Bank BRI Pasar Jumat", itemsArray);
        MvcResult mvcResult = setSlotRequest(shipmentObj, user);

        Assert.assertEquals(200, mvcResult.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            List<Batch> deliveryBatchesFinal = batchRepository.findByType(Batch.Type.DELIVERY);
            Assert.assertThat(deliveryBatchesFinal, Matchers.hasSize(1));
            Batch batch = deliveryBatchesFinal.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
            Assert.assertEquals(1, (int) batch.getVehicle());
            Assert.assertEquals(shift.getId(), batch.getShift().getId());
        });
    }

    @Test
    public void changeSlotTest() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, user.getTenant());
        User driver = userFactory.createUserData(Role.Name.DRIVER, user.getTenant());
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);

        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 3, 3);
        Slot slot = slots.get(1);
        int numberOfDriver = 3;
        final Shift shiftDriver1 = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime().minusHours(2), slot.getStartTime().plusMinutes(50), numberOfDriver);
        Shift shiftShopper1 = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().minusHours(3), slot.getStartTime().plusHours(1), 1);

        final Shift shiftDriver2 = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime().plusMinutes(10), slot.getStartTime().plusHours(4), numberOfDriver);
        Shift shiftShopper2 = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().plusHours(1), slot.getStartTime().plusHours(5), 3);

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(1).getId(), 1, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            List<Batch> deliveryBatchesFinal = batchRepository.findByType(Batch.Type.DELIVERY);
            Assert.assertThat(deliveryBatchesFinal, Matchers.hasSize(1));
            Batch batch = deliveryBatchesFinal.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
            Assert.assertEquals(1, (int) batch.getVehicle());
            Assert.assertEquals(shiftDriver1.getId(), batch.getShift().getId());
        });

        // Lalamove
        Shipment s2 = shipmentFactory.createShipment(slot, user, "Order-22222", "Shipment-222222");
        Batch batch2 = batchFactory.createBatch(user, shopper, s2, slot, Batch.Type.SHOPPING, Job.State.FINISHED);
        Batch deliveryBatch2 = batchFactory.createTplDeliveryBatch(user, s2, slot, Batch.TplType.LALAMOVE, Job.State.DELIVERING);
        deliveryBatch2.setUser(driver);
        batchFactory.save(deliveryBatch2);

        LalamoveDelivery delivery2 = new LalamoveDelivery();
        delivery2.setTenant(user.getTenant());
        delivery2.setCreatedBy(user.getId());
        delivery2.setShipment(s2);
        delivery2.setStatus(LalamoveDelivery.Status.PICKED_UP);
        delivery2.setExternalOrderId("LALAMOVE-111");
        delivery2.setDriverName("John");
        delivery2.setDriverPhone("08192823928");
        delivery2.setDriverPhoto("https://google.com");
        delivery2.setDriverPlateNumber("B 1234 CD");
        delivery2.setServiceType("MOTORCYCLE");
        delivery2.setServiceDescription("Motor");
        delivery2.setScheduleAt(LocalDateTime.of(2020, 4, 30, 10, 0));
        delivery2.setTryCount(0);
        lalamoveDeliveryFactory.save(delivery2);

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + "Order-1" + "/change_slot?slot_id=" + slots.get(2).getId())
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        transactionHelper.withNewTransaction(() -> {
            List<Batch> deliveryBatchesFinal = batchRepository.findByType(Batch.Type.DELIVERY);
            Assert.assertThat(deliveryBatchesFinal, Matchers.hasSize(1));
            Batch batch = deliveryBatchesFinal.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
            Assert.assertEquals(1, (int) batch.getVehicle());
            Assert.assertEquals(shiftDriver2.getId(), batch.getShift().getId());
        });
    }

    @Test
    public void setSlotWithShipmentExceedHfDimensionAndLalamoveEnabled() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user, Slot.Type.LONGER_DELIVERY).get(0);
        stockLocation.setMaxDeliveryVolume(1);
        stockLocationFactory.save(stockLocation);

        /** Enable lalamove */
        stockLocation.setEnableLalamove(true);
        stockLocation.setPreferences(new HashMap<String, String>() {{
            put("enable_lalamove_delivery_fee", "6000");
            put("lalamove_flat_service_fee", "1000");
        }});
        stockLocationFactory.save(stockLocation);
        lalamoveServiceTypeFactory.createServiceTypes(user, stockLocation);

        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 3, 1);
        Slot slot = slots.get(0);
        Shift driverShift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime(), slot.getStartTime().plusHours(10), 1);
        Shift shopperShift = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().minusHours(2), slot.getStartTime().plusHours(16), 1);

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(shipmentJsonObjectFactory.createItemObject(0, "Barang-01-ID", 1, 1, 100, 100, 100));
        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), "Order-1", new GeoPoint(-6.2877476, 106.7704242), "Bank BRI Pasar Jumat", itemsArray);
        MvcResult mvcResult = setSlotRequest(shipmentObj, user);

        Assert.assertEquals(200, mvcResult.getResponse().getStatus());

        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            Assert.assertThat(shoppingBatches, Matchers.hasSize(1));
            Batch shoppingBatch = shoppingBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
            Assert.assertEquals(1, (int) shoppingBatch.getVehicle());
            Assert.assertEquals(shopperShift.getId(), shoppingBatch.getShift().getId());

            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            Assert.assertThat(deliveryBatches, Matchers.hasSize(1));
            Batch deliveryBatch = deliveryBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
            Assert.assertEquals(null, deliveryBatch.getVehicle());
            Assert.assertEquals(null, deliveryBatch.getShift());
            Assert.assertEquals(slot.getStartTime(), deliveryBatch.getStartTime());
            Assert.assertEquals(Batch.TplType.LALAMOVE, deliveryBatch.getTplType());
        });
    }

    @Ignore
    @Test
    public void shouldPreventDeliveryStartTimeBeforeShoppingFinished() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user, Slot.Type.LONGER_DELIVERY).get(0);
        stockLocation.setShopperAveragePickingTimePerUniqItem(2);
        stockLocation.setShopperQueueReplacementTime(0);
        stockLocationFactory.save(stockLocation);

        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 3, 1);
        Slot slot = slots.get(0);
        Shift shopperShift = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().minusMinutes(30), slot.getStartTime().plusHours(8), 1);
        Shift driverShift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime(), slot.getStartTime().plusHours(10), 1);

        List<Batch> batches = batchFactory.createBatches(1, user, slot);
        List<Batch> initialShoppingBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).collect(Collectors.toList());
        List<Batch> initialDeliveryBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).collect(Collectors.toList());

        Batch batch1 = initialShoppingBatches.get(0);
        batch1.setStartTime(shopperShift.getStartTime());
        batch1.setEndTime(shopperShift.getStartTime().plusMinutes(20));
        batch1.setVehicle(1);
        batch1.setShift(shopperShift);
        batchFactory.save(batch1);

        Batch batch2 = initialDeliveryBatches.get(0);
        batch2.setStartTime(slot.getStartTime());
        batch2.setEndTime(batch2.getStartTime().plusMinutes(20));
        batch2.setVehicle(1);
        batch2.setShift(driverShift);
        batchFactory.save(batch2);

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 20, "Order-45", new GeoPoint(-6.2925049, 106.821986), "Address 4");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());

        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            Assert.assertThat(shoppingBatches, Matchers.hasSize(2));
            Batch shoppingBatch = shoppingBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
            Assert.assertEquals(1, (int) shoppingBatch.getVehicle());
            Assert.assertEquals(shopperShift.getId(), shoppingBatch.getShift().getId());
            Assert.assertEquals(batch1.getEndTime(), shoppingBatch.getStartTime());
            Assert.assertEquals(batch1.getEndTime().plusMinutes(40), shoppingBatch.getEndTime());

            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            Assert.assertThat(deliveryBatches, Matchers.hasSize(2));
            Batch deliveryBatch = deliveryBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
            Assert.assertEquals(1, (int) deliveryBatch.getVehicle());
            Assert.assertEquals(driverShift.getId(), deliveryBatch.getShift().getId());
            Assert.assertEquals(shoppingBatch.getEndTime(), deliveryBatch.getStartTime());
            Assert.assertEquals(deliveryBatch.getStartTime().plusMinutes(stockLocation.getCluster().deliveryTimePerShipment()), deliveryBatch.getEndTime());
            Assert.assertTrue(deliveryBatch.getStartTime().equals(shoppingBatch.getEndTime()) || deliveryBatch.getStartTime().isAfter(shoppingBatch.getEndTime()));
        });
    }

    /**
     * Slot    00:00-03:00, 03:00-06:00, 06:00-09:00
     * Shift shopping 1 23:30-08:00, count 1
     * Shift delivery 1 00:00-10:00, count 1
     * Batch 1, shopping 23:30-23:55, delivery 00:00-00:24
     * Batch 2, shopping 23:55-00:15, delivery 03:00-03:20
     * Set slot on 00:00-03:00, 40 minutes of shopping, should make shopping 00:15-00:55 and delivery 00:24-00:48
     * Optimization should make shopping 23:55-00:35 and delivery 00:35-01:02 (27 minutes)
     **/

    @Test
    public void shouldCheckOtherSlotInOneShift() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, user.getTenant());
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user, Slot.Type.LONGER_DELIVERY).get(0);
        stockLocation.setShopperAveragePickingTimePerUniqItem(2);
        stockLocation.setShopperQueueReplacementTime(0);
        stockLocationFactory.save(stockLocation);

        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 3, 3);
        Slot slot = slots.get(0);
        Shift shopperShift = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().minusMinutes(30), slot.getStartTime().plusHours(8), 1);
        Shift driverShift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime(), slot.getStartTime().plusHours(10), 1);

        List<Batch> batches = batchFactory.createBatches(1, user, slot);
        List<Batch> initialShoppingBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).collect(Collectors.toList());
        List<Batch> initialDeliveryBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).collect(Collectors.toList());

        Batch batch1 = initialShoppingBatches.get(0);
        batch1.setStartTime(shopperShift.getStartTime());
        batch1.setEndTime(shopperShift.getStartTime().plusMinutes(25));
        batch1.setVehicle(1);
        batch1.setShift(shopperShift);
        batchFactory.save(batch1);

        Batch batch2 = initialDeliveryBatches.get(0);
        batch2.setStartTime(slot.getStartTime());
        batch2.setEndTime(batch2.getStartTime().plusMinutes(24));
        batch2.setVehicle(1);
        batch2.setShift(driverShift);
        batchFactory.save(batch2);
        startBatches(Lists.newArrayList(batch1,batch2), shopper);
        List<Batch> batches1 = batchFactory.createBatches(1, user, slots.get(1));
        List<Batch> initialShoppingBatches1 = batches1.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).collect(Collectors.toList());
        List<Batch> initialDeliveryBatches1 = batches1.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).collect(Collectors.toList());

        Batch batch3 = initialShoppingBatches1.get(0);
        batch3.setStartTime(batch1.getEndTime());
        batch3.setEndTime(batch3.getStartTime().plusMinutes(20));
        batch3.setVehicle(1);
        batch3.setShift(shopperShift);
        batchFactory.save(batch3);

        Batch batch4 = initialDeliveryBatches1.get(0);
        batch4.setStartTime(slots.get(1).getStartTime());
        batch4.setEndTime(batch4.getStartTime().plusMinutes(20));
        batch4.setVehicle(1);
        batch4.setShift(driverShift);
        batchFactory.save(batch4);

        printPlan(user, stockLocation, slots.get(0).getStartTime());
        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 20, "Order-45", new GeoPoint(-6.2925049, 106.821986), "Address 4");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());

        printPlan(user, stockLocation, slots.get(0).getStartTime());
        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            Assert.assertThat(shoppingBatches, Matchers.hasSize(3));
            Shipment shipment1 = shipmentRepository.findByNumber("Order-45");
            Batch shoppingBatchShipment1 = shipment1.getShoppingJob().get().getBatch();
            Batch deliveryBatchShipment1 = shipment1.getDeliveryJob().get().getBatch();

            Assert.assertEquals(shopperShift.getId(), shoppingBatchShipment1.getShift().getId());
            // uncomment after new implementation
            //Assert.assertEquals(batch3.getEndTime(), shoppingBatchShipment1.getStartTime());
            //Assert.assertEquals(batch3.getEndTime().plusMinutes(40), shoppingBatchShipment1.getEndTime());

            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            Assert.assertThat(deliveryBatches, Matchers.hasSize(3));

            Assert.assertEquals(driverShift.getId(), deliveryBatchShipment1.getShift().getId());
            Assert.assertEquals(DateTimeUtil.getMaxDatesBetween(batch2.getEndTime(), shoppingBatchShipment1.getEndTime()), deliveryBatchShipment1.getStartTime());
            Assert.assertEquals(DateTimeUtil.getMaxDatesBetween(batch2.getEndTime(), shoppingBatchShipment1.getEndTime()).plusMinutes(24), deliveryBatchShipment1.getEndTime());
        });

        try {
            Shipment shipment = shipmentRepository.findByNumber("Order-45");
            shipment.setState(Shipment.State.READY);
            shipmentRepository.save(shipment);
            Thread.sleep(100);

            AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(user.getToken(), user.getTenant().getToken());
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            slotOptimizationService.shopperSlotOptimization(slots.get(0), shopperShift);
            slotOptimizationService.driverSlotOptimization(slots.get(0));
        } finally {
            SecurityContextHolder.clearContext();
        }

        printPlan(user, stockLocation, slots.get(0).getStartTime());
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByNumber("Order-45");
            Batch shoppingBatchShipment1 = shipment1.getShoppingJob().get().getBatch();
            Batch deliveryBatchShipment1 = shipment1.getDeliveryJob().get().getBatch();

            Assert.assertEquals(batch1.getEndTime(), shoppingBatchShipment1.getStartTime());
            Assert.assertEquals(batch1.getShift().getId(), shoppingBatchShipment1.getShift().getId());
            Assert.assertEquals(shoppingBatchShipment1.getEndTime(), deliveryBatchShipment1.getStartTime());
        });
    }

    /**
     * Slot    00:00-03:00, 03:00-06:00, 06:00-09:00
     * Shift shopping 1 21:00-08:00, count 1
     * Shift delivery 1 00:00-10:00, count 1
     * Batch 1, shopping 21:00-21:25, delivery 03:00-03:24
     * Set slot on 00:00-03:00, 40 minutes of shopping, should make shopping 21:25-22:05 and delivery 00:00-00:24
     * Optimization should make shopping 21:00-21:40 and delivery 00:00-00:27 (27 minutes)
     **/
    @Test
    public void shouldCheckOtherSlotInOneShiftCase2() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user, Slot.Type.LONGER_DELIVERY).get(0);
        stockLocation.setShopperAveragePickingTimePerUniqItem(2);
        stockLocation.setShopperQueueReplacementTime(0);
        stockLocationFactory.save(stockLocation);

        LocalDateTime now = LocalDateTime.now();
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 3, 3);
        Slot slot = slots.get(0);
        Shift shopperShift = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().minusHours(3), slot.getStartTime().plusHours(8), 1);
        Shift driverShift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime(), slot.getStartTime().plusHours(10), 1);

        List<Batch> batches = batchFactory.createBatches(1, user, slots.get(1));
        List<Batch> initialShoppingBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).collect(Collectors.toList());
        List<Batch> initialDeliveryBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).collect(Collectors.toList());

        Batch batch1 = initialShoppingBatches.get(0);
        batch1.setStartTime(shopperShift.getStartTime());
        batch1.setEndTime(batch1.getStartTime().plusMinutes(25));
        batch1.setVehicle(1);
        batch1.setShift(shopperShift);
        batchFactory.save(batch1);

        Batch batch2 = initialDeliveryBatches.get(0);
        batch2.setStartTime(slots.get(1).getStartTime());
        batch2.setEndTime(batch2.getStartTime().plusMinutes(24));
        batch2.setVehicle(1);
        batch2.setShift(driverShift);
        batchFactory.save(batch2);

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 20, "Order-45", new GeoPoint(-6.2925049, 106.821986), "Address 4");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());

        printPlan(user, stockLocation, slots.get(0).getStartTime());
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByNumber("Order-45");
            Batch shoppingBatchShipment1 = shipment1.getShoppingJob().get().getBatch();
            Batch deliveryBatchShipment1 = shipment1.getDeliveryJob().get().getBatch();

            //uncomment after new implementation for shopper availability
            //Assert.assertEquals(batch1.getEndTime(), shoppingBatchShipment1.getStartTime());
            Assert.assertEquals(shopperShift.getId(), shoppingBatchShipment1.getShift().getId());
            Assert.assertEquals(slots.get(0).getStartTime(), deliveryBatchShipment1.getStartTime());
        });

        try {
            Shipment shipment = shipmentRepository.findByNumber("Order-45");
            shipment.setState(Shipment.State.READY);
            shipmentRepository.save(shipment);
            Thread.sleep(100);

            AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(user.getToken(), user.getTenant().getToken());
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            slotOptimizationService.shopperSlotOptimization(slots.get(0), shopperShift);
            slotOptimizationService.driverSlotOptimization(slots.get(0));
        } finally {
            SecurityContextHolder.clearContext();
        }

        printPlan(user, stockLocation, slots.get(0).getStartTime());
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByNumber("Order-45");
            Batch shoppingBatchShipment1 = shipment1.getShoppingJob().get().getBatch();
            Batch deliveryBatchShipment1 = shipment1.getDeliveryJob().get().getBatch();

            Assert.assertEquals(shopperShift.getStartTime(), shoppingBatchShipment1.getStartTime());
            Assert.assertEquals(shopperShift.getId(), shoppingBatchShipment1.getShift().getId());
            Assert.assertEquals(slots.get(0).getStartTime(), deliveryBatchShipment1.getStartTime());
        });
    }

    /**
     * Slot    00:00-09:00
     * Shift shopping 1 22:00-02:00, count 0
     * Shift shopping 2 02:00-05:00, count 1
     * Shift shopping 3 06:00-08:00, count 1
     * Shift delivery 1 00:00-10:00, count 1
     * Batch 1, shopping 02:00-04:45, delivery 04:45-05:09
     * Set slot on 00:00-09:00, 40 minutes of shopping, should make shopping 06:00-06:40 and delivery 06:40-07:09
     * (Delivery start time before shopping end time on set slot, will be move after shopping upon optimization)
     * Optimization should make shopping 06:00-06:40 and delivery 06:40-07:07 (27 minutes)
     **/
    @Test
    public void shouldCheckMultiShiftInOneSlot() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, user.getTenant());
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user, Slot.Type.LONGER_DELIVERY).get(0);
        stockLocation.setShopperAveragePickingTimePerUniqItem(2);
        stockLocation.setShopperQueueReplacementTime(0);
        stockLocationFactory.save(stockLocation);

        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 9, 1);
        Slot slot = slots.get(0);
        Shift shopperShift1 = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().minusHours(2), slot.getStartTime().plusHours(2), 0);
        Shift shopperShift2 = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().plusHours(2), slot.getStartTime().plusHours(5), 1);
        Shift shopperShift3 = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().plusHours(6), slot.getStartTime().plusHours(8), 1);
        Shift driverShift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime(), slot.getStartTime().plusHours(10), 1);

        List<Batch> batches = batchFactory.createBatches(1, user, slot);
        List<Batch> initialShoppingBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).collect(Collectors.toList());
        List<Batch> initialDeliveryBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).collect(Collectors.toList());

        Batch batch1 = initialShoppingBatches.get(0);
        batch1.setStartTime(shopperShift2.getStartTime());
        batch1.setEndTime(batch1.getStartTime().plusMinutes(165));
        batch1.setVehicle(1);
        batch1.setShift(shopperShift2);
        batchFactory.save(batch1);

        Batch batch2 = initialDeliveryBatches.get(0);
        batch2.setStartTime(batch1.getEndTime());
        batch2.setEndTime(batch2.getStartTime().plusMinutes(24));
        batch2.setVehicle(1);
        batch2.setShift(driverShift);
        batchFactory.save(batch2);

        startBatches(Lists.newArrayList(batch1,batch2), shopper);
        printPlan(user, stockLocation, slot.getStartTime());
        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 20, "Order-45", new GeoPoint(-6.2925049, 106.821986), "Address 4");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slot.getStartTime());

        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByNumber("Order-45");
            Batch shoppingBatchShipment1 = shipment1.getShoppingJob().get().getBatch();
            Batch deliveryBatchShipment1 = shipment1.getDeliveryJob().get().getBatch();

            Assert.assertEquals(shopperShift3.getStartTime(), shoppingBatchShipment1.getStartTime());
            Assert.assertEquals(shopperShift3.getId(), shoppingBatchShipment1.getShift().getId());
            Assert.assertEquals(DateTimeUtil.getMaxDatesBetween(batch2.getEndTime(), shoppingBatchShipment1.getEndTime()), deliveryBatchShipment1.getStartTime());
        });

        try {
            Shipment shipment = shipmentRepository.findByNumber("Order-45");
            shipment.setState(Shipment.State.READY);
            shipmentRepository.save(shipment);
            Thread.sleep(100);

            AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(user.getToken(), user.getTenant().getToken());
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            slotOptimizationService.shopperSlotOptimization(slots.get(0), shopperShift3);
            slotOptimizationService.driverSlotOptimization(slots.get(0));
        } finally {
            SecurityContextHolder.clearContext();
        }

        printPlan(user, stockLocation, slots.get(0).getStartTime());
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByNumber("Order-45");
            Batch shoppingBatchShipment1 = shipment1.getShoppingJob().get().getBatch();
            Batch deliveryBatchShipment1 = shipment1.getDeliveryJob().get().getBatch();

            Assert.assertEquals(shopperShift3.getStartTime(), shoppingBatchShipment1.getStartTime());
            Assert.assertEquals(shopperShift3.getId(), shoppingBatchShipment1.getShift().getId());
            Assert.assertEquals(shoppingBatchShipment1.getEndTime(), deliveryBatchShipment1.getStartTime());
        });
    }

    @Test
    public void shouldCheckMultiShiftInOneSlotSameCluster() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);

        List<Slot> slots1 = slotFactory.createLongerDeliverySlots(stockLocations.get(0), user, 9, 1);
        List<Slot> slots2 = slotFactory.createLongerDeliverySlots(stockLocations.get(1), user, 9, 1);
        Slot slot = slots1.get(0);


        Shift shopperShift1 = shiftFactory.createShift(stockLocations.get(0), user, Shift.Type.SHOPPER, slots1.get(0).getStartTime().minusHours(2), slots1.get(0).getEndTime().minusHours(2), 1);
        Shift shopperShift2 = shiftFactory.createShift(stockLocations.get(1), user, Shift.Type.SHOPPER, slots2.get(0).getStartTime().minusHours(2), slots2.get(0).getEndTime().minusHours(2), 1);

        Shift driverShift1 = shiftFactory.createShift(stockLocations.get(0), user, Shift.Type.DRIVER, slots1.get(0).getStartTime(), slots1.get(0).getEndTime().plusMinutes(30), 0);
        Shift driverShift2 = shiftFactory.createShift(stockLocations.get(1), user, Shift.Type.DRIVER, slots2.get(0).getStartTime(), slots2.get(0).getEndTime().plusMinutes(30), 1);


        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocations.get(0), slots1.get(0).getId(), 5, "Order-45", new GeoPoint(-6.2925049, 106.821986), "Address 4");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());

        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            List<Batch> sortedShoppingBatches = shoppingBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch shoppingBatch = sortedShoppingBatches.get(0);
            Assert.assertEquals(shopperShift1.getStartTime(), shoppingBatch.getStartTime());
            Assert.assertEquals(shopperShift1.getId(), shoppingBatch.getShift().getId());

            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            List<Batch> sortedDeliveryBatches = deliveryBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch deliveryBatch = sortedDeliveryBatches.get(0);
            Assert.assertEquals(slots1.get(0).getStartTime(), deliveryBatch.getStartTime());
            Assert.assertEquals(driverShift2.getId(), deliveryBatch.getShift().getId());
        });
    }

    @Test
    public void shouldCheckMultiShiftStartAndSlotForDeliveryTimeCase1() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);

        List<Slot> slots1 = slotFactory.createLongerDeliverySlots(stockLocations.get(0), user, 9, 1);
        List<Slot> slots2 = slotFactory.createLongerDeliverySlots(stockLocations.get(1), user, 9, 1);
        Slot slot = slots1.get(0);


        Shift shopperShift1 = shiftFactory.createShift(stockLocations.get(0), user, Shift.Type.SHOPPER, slots1.get(0).getStartTime().minusHours(2), slots1.get(0).getEndTime().minusHours(2), 1);

        Shift driverShift1 = shiftFactory.createShift(stockLocations.get(0), user, Shift.Type.DRIVER, slots1.get(0).getStartTime().plusMinutes(30), slots1.get(0).getEndTime().minusHours(4), 1);
        Shift driverShift2 = shiftFactory.createShift(stockLocations.get(0), user, Shift.Type.DRIVER, slots1.get(0).getStartTime().plusHours(5), slots1.get(0).getEndTime().plusMinutes(30), 1);


        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocations.get(0), slots1.get(0).getId(), 5, "Order-45", new GeoPoint(-6.2925049, 106.821986), "Address 4");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());

        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            List<Batch> sortedShoppingBatches = shoppingBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch shoppingBatch = sortedShoppingBatches.get(0);
            Assert.assertEquals(shopperShift1.getStartTime(), shoppingBatch.getStartTime());
            Assert.assertEquals(shopperShift1.getId(), shoppingBatch.getShift().getId());

            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            List<Batch> sortedDeliveryBatches = deliveryBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch deliveryBatch = sortedDeliveryBatches.get(0);
            Assert.assertEquals(DateTimeUtil.getMaxDatesBetween(slot.getStartTime(), driverShift1.getStartTime()), deliveryBatch.getStartTime());
            Assert.assertEquals(driverShift1.getId(), deliveryBatch.getShift().getId());
        });
    }

    @Test
    public void shouldCheckMultiShiftStartAndSlotForDeliveryTimeCase2() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);

        List<Slot> slots1 = slotFactory.createLongerDeliverySlots(stockLocations.get(0), user, 9, 1);
        List<Slot> slots2 = slotFactory.createLongerDeliverySlots(stockLocations.get(1), user, 9, 1);
        Slot slot = slots1.get(0);


        Shift shopperShift1 = shiftFactory.createShift(stockLocations.get(0), user, Shift.Type.SHOPPER, slots1.get(0).getStartTime().minusHours(2), slots1.get(0).getEndTime().minusHours(2), 1);

        Shift driverShift1 = shiftFactory.createShift(stockLocations.get(0), user, Shift.Type.DRIVER, slots1.get(0).getStartTime().plusMinutes(30), slots1.get(0).getEndTime().minusHours(4), 0);
        Shift driverShift2 = shiftFactory.createShift(stockLocations.get(0), user, Shift.Type.DRIVER, slots1.get(0).getStartTime().plusHours(5), slots1.get(0).getEndTime().plusMinutes(30), 1);


        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocations.get(0), slots1.get(0).getId(), 5, "Order-45", new GeoPoint(-6.2925049, 106.821986), "Address 4");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());

        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            List<Batch> sortedShoppingBatches = shoppingBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch shoppingBatch = sortedShoppingBatches.get(0);
            Assert.assertEquals(shopperShift1.getStartTime(), shoppingBatch.getStartTime());
            Assert.assertEquals(shopperShift1.getId(), shoppingBatch.getShift().getId());

            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            List<Batch> sortedDeliveryBatches = deliveryBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch deliveryBatch = sortedDeliveryBatches.get(0);
            Assert.assertEquals(DateTimeUtil.getMaxDatesBetween(slot.getStartTime(), driverShift2.getStartTime()), deliveryBatch.getStartTime());
            Assert.assertEquals(driverShift2.getId(), deliveryBatch.getShift().getId());
        });
    }


    @Test
    public void shouldCheckMultiShiftStartAndSlotForDeliveryTimeCase3() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(3, user, Slot.Type.LONGER_DELIVERY);

        List<Slot> slots1 = slotFactory.createLongerDeliverySlots(stockLocations.get(0), user, 2, 4);
        List<Slot> slots2 = slotFactory.createLongerDeliverySlots(stockLocations.get(1), user, 2, 4);
        List<Slot> slots3 = slotFactory.createLongerDeliverySlots(stockLocations.get(2), user, 2, 4);
        Slot slot = slots1.get(0);


        Shift shopperShift1 = shiftFactory.createShift(stockLocations.get(0), user, Shift.Type.SHOPPER, slots1.get(0).getStartTime().minusHours(2), slots1.get(0).getStartTime().plusHours(6), 1);

        Shift driverStore1Shift1 = shiftFactory.createShift(stockLocations.get(0), user, Shift.Type.DRIVER, slots1.get(0).getStartTime(), slots1.get(0).getStartTime().plusHours(10), 0);
        Shift driverStore2Shift1 = shiftFactory.createShift(stockLocations.get(1), user, Shift.Type.DRIVER, slots2.get(0).getStartTime(), slots2.get(0).getStartTime().plusHours(10), 0);
        Shift driverStore3Shift1 = shiftFactory.createShift(stockLocations.get(2), user, Shift.Type.DRIVER, slots3.get(0).getStartTime(), slots3.get(0).getStartTime().plusHours(4), 0);
        Shift driverStore3Shift2 = shiftFactory.createShift(stockLocations.get(2), user, Shift.Type.DRIVER, slots3.get(0).getStartTime().plusHours(1), slots3.get(0).getStartTime().plusHours(8), 1);


        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocations.get(0), slots1.get(0).getId(), 5, "Order-45", new GeoPoint(-6.2925049, 106.821986), "Address 4");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());

        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            List<Batch> sortedShoppingBatches = shoppingBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch shoppingBatch = sortedShoppingBatches.get(0);
            Assert.assertEquals(shopperShift1.getStartTime(), shoppingBatch.getStartTime());
            Assert.assertEquals(shopperShift1.getId(), shoppingBatch.getShift().getId());

            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            List<Batch> sortedDeliveryBatches = deliveryBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch deliveryBatch = sortedDeliveryBatches.get(0);
            Assert.assertEquals(DateTimeUtil.getMaxDatesBetween(slot.getStartTime(), driverStore3Shift2.getStartTime()), deliveryBatch.getStartTime());
            Assert.assertEquals(driverStore3Shift2.getId(), deliveryBatch.getShift().getId());
        });
    }

    @Test
    public void shouldCheckMultiShiftStartAndSlotForDeliveryTimeCase4() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(3, user, Slot.Type.LONGER_DELIVERY);
        StockLocation stockLocation = stockLocations.get(0);
        stockLocation.setEnableGrabExpress(false);
        stockLocation.setEnableGrabExpressCod(false);
        stockLocationFactory.save(stockLocation);

        List<Slot> slots1 = slotFactory.createLongerDeliverySlots(stockLocations.get(0), user, 2, 4);
        List<Slot> slots2 = slotFactory.createLongerDeliverySlots(stockLocations.get(1), user, 2, 4);
        List<Slot> slots3 = slotFactory.createLongerDeliverySlots(stockLocations.get(2), user, 2, 4);
        Slot slot = slots1.get(0);


        Shift shopperShift1 = shiftFactory.createShift(stockLocations.get(0), user, Shift.Type.SHOPPER, slots1.get(0).getStartTime().minusHours(2), slots1.get(0).getStartTime().plusHours(6), 1);

        Shift driverStore1Shift1 = shiftFactory.createShift(stockLocations.get(0), user, Shift.Type.DRIVER, slots1.get(0).getStartTime(), slots1.get(0).getStartTime().plusHours(10), 0);
        Shift driverStore2Shift1 = shiftFactory.createShift(stockLocations.get(1), user, Shift.Type.DRIVER, slots2.get(0).getStartTime(), slots2.get(0).getStartTime().plusHours(10), 0);
        Shift driverStore3Shift1 = shiftFactory.createShift(stockLocations.get(2), user, Shift.Type.DRIVER, slots3.get(0).getStartTime(), slots3.get(0).getStartTime().plusHours(4), 0);
        Shift driverStore3Shift2 = shiftFactory.createShift(stockLocations.get(2), user, Shift.Type.DRIVER, slots3.get(0).getStartTime().plusHours(1), slots3.get(0).getStartTime().plusHours(8), 0);


        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocations.get(0), slots1.get(0).getId(), 5, "Order-45", new GeoPoint(-6.2925049, 106.821986), "Address 4");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(422, mvcResults.getResponse().getStatus());
    }

    @Test
    public void shouldAvailableOnBigBasketSizeToAllVolume() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);
        StockLocation stockLocation = stockLocations.get(0);
        stockLocation.setMaxDeliveryVolume(1);
        stockLocationFactory.save(stockLocation);

        List<Slot> slots1 = slotFactory.createLongerDeliverySlots(stockLocations.get(0), user, 9, 1);
        List<Slot> slots2 = slotFactory.createLongerDeliverySlots(stockLocations.get(1), user, 9, 1);
        Slot slot = slots1.get(0);


        Shift shopperShift1 = shiftFactory.createShift(stockLocations.get(0), user, Shift.Type.SHOPPER, slots1.get(0).getStartTime().minusHours(2), slots1.get(0).getEndTime().minusHours(2), 1);

        Shift driverShift1 = shiftFactory.createShift(stockLocations.get(0), user, Shift.Type.DRIVER, slots1.get(0).getStartTime().plusMinutes(30), slots1.get(0).getEndTime().minusHours(4), 0);
        Shift driverShift2 = shiftFactory.createShift(stockLocations.get(0), user, Shift.Type.DRIVER, slots1.get(0).getStartTime().plusHours(5), slots1.get(0).getEndTime().plusMinutes(30), 1);


        JSONArray itemsArray = new JSONArray();
        itemsArray.put(shipmentJsonObjectFactory.createItemObject(0, "Barang-01-ID", 1, 1, 100, 100, 100));
        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots1.get(0).getId(), "Order-1", new GeoPoint(-6.2925049, 106.821986), "Bank BRI Pasar Jumat", itemsArray);
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());

        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            List<Batch> sortedShoppingBatches = shoppingBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch shoppingBatch = sortedShoppingBatches.get(0);
            Assert.assertEquals(shopperShift1.getStartTime(), shoppingBatch.getStartTime());
            Assert.assertEquals(shopperShift1.getId(), shoppingBatch.getShift().getId());

            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            List<Batch> sortedDeliveryBatches = deliveryBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch deliveryBatch = sortedDeliveryBatches.get(0);
            Assert.assertEquals(DateTimeUtil.getMaxDatesBetween(slot.getStartTime(), driverShift2.getStartTime()), deliveryBatch.getStartTime());
            Assert.assertEquals(driverShift2.getId(), deliveryBatch.getShift().getId());
        });
    }

    /**
     * Slot    00:00-03:00, 03:00-06:00, 06:00-09:00
     * Shift shopping 1 23:00-08:00, count 1
     * Shift delivery 1 00:00-09:00, count 1
     * Batch 1, shopping 23:00-00:20, delivery 03:00-04:20
     * Set slot on 00:00-03:00, should make shopping start 00:20 and delivery start after shopping finish
     **/
    @Test
    public void shouldAssignToEarliestLatestBatchCase4() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);

        /** Cluster 1 */
        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 3, 3);
        Slot slot = slots.get(1);
        Shift shift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slots.get(0).getStartTime(), slots.get(2).getEndTime(), 1);
        Shift shiftShopper = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slots.get(0).getStartTime().minusHours(1), slots.get(2).getEndTime().minusHours(1), 1);
        List<Batch> batches = batchFactory.createBatches(1, user, slot);
        List<Batch> initialShoppingBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).collect(Collectors.toList());
        List<Batch> initialDeliveryBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).collect(Collectors.toList());

        Batch batch1 = initialDeliveryBatches.get(0);
        batch1.setEndTime(batch1.getStartTime().plusMinutes(80));
        batch1.setVehicle(1);
        batch1.setShift(shift);

        Batch shoppingBatch1 = initialShoppingBatches.get(0);
        shoppingBatch1.setStartTime(shiftShopper.getStartTime());
        shoppingBatch1.setEndTime(shoppingBatch1.getStartTime().plusMinutes(80));
        shoppingBatch1.setVehicle(1);
        shoppingBatch1.setShift(shiftShopper);

        batchFactory.saveAll(Lists.newArrayList(batch1, shoppingBatch1));

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 1, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            List<Batch> sortedShoppingBatches = shoppingBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch shoppingBatch = sortedShoppingBatches.get(0);
            Assert.assertEquals(shoppingBatch1.getEndTime(), shoppingBatch.getStartTime());
            Assert.assertEquals(shoppingBatch1.getShift().getId(), shoppingBatch.getShift().getId());

            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            List<Batch> sortedDeliveryBatches = deliveryBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch deliveryBatch = sortedDeliveryBatches.get(0);
            Assert.assertEquals(DateTimeUtil.getMaxDatesBetween(slots.get(0).getStartTime(), shift.getStartTime(), shoppingBatch.getEndTime()), deliveryBatch.getStartTime());
            Assert.assertEquals(shift.getId(), deliveryBatch.getShift().getId());
        });
    }

    /**
     * Slot    00:00-02:00, 02:00-04:00, 05:00-07:00
     * Shift shopping 1 23:00-06:00, count 1
     * Shift delivery 1 00:00-07:00, count 1
     * Batch 1, shopping 23:00-00:20, delivery 05:00-06:20
     * Set slot on 00:00-02:00, should make shopping start 00:20 and delivery start after shopping finished
     **/
    @Test
    public void shouldAssignToEarliestLatestBatchCase5() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);

        /** Cluster 1 */
        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 2, 3);
        Slot lastSlot = slots.get(2);
        lastSlot.setStartTime(lastSlot.getStartTime().plusHours(1));
        lastSlot.setEndTime(lastSlot.getStartTime().plusHours(2));
        slotFactory.save(lastSlot);
        Shift shift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slots.get(0).getStartTime(), slots.get(2).getEndTime(), 1);
        Shift shiftShopper = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slots.get(0).getStartTime().minusHours(1), slots.get(2).getEndTime().minusHours(1), 1);
        List<Batch> batches = batchFactory.createBatches(1, user, lastSlot);
        List<Batch> initialShoppingBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).collect(Collectors.toList());
        List<Batch> initialDeliveryBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).collect(Collectors.toList());

        Batch batch1 = initialDeliveryBatches.get(0);
        batch1.setEndTime(batch1.getStartTime().plusMinutes(80));
        batch1.setVehicle(1);
        batch1.setShift(shift);

        Batch shoppingBatch1 = initialShoppingBatches.get(0);
        shoppingBatch1.setStartTime(shiftShopper.getStartTime());
        shoppingBatch1.setEndTime(shoppingBatch1.getStartTime().plusMinutes(80));
        shoppingBatch1.setVehicle(1);
        shoppingBatch1.setShift(shiftShopper);

        batchFactory.saveAll(Lists.newArrayList(batch1, shoppingBatch1));

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 1, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            List<Batch> sortedShoppingBatches = shoppingBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch shoppingBatch = sortedShoppingBatches.get(0);
            Assert.assertEquals(shoppingBatch1.getEndTime(), shoppingBatch.getStartTime());
            Assert.assertEquals(shoppingBatch1.getShift().getId(), shoppingBatch.getShift().getId());

            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            List<Batch> sortedDeliveryBatches = deliveryBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch deliveryBatch = sortedDeliveryBatches.get(0);
            Assert.assertEquals(DateTimeUtil.getMaxDatesBetween(slots.get(0).getStartTime(), shift.getStartTime(), shoppingBatch.getEndTime()), deliveryBatch.getStartTime());
            Assert.assertEquals(shift.getId(), deliveryBatch.getShift().getId());
        });
    }

    @Test
    public void shouldAssignToEarliestLatestBatchCase6() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);

        /** Cluster 1 */
        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 4, 2);
        Slot lastSlot = slots.get(1);
        lastSlot.setStartTime(lastSlot.getStartTime().plusHours(2));
        lastSlot.setEndTime(lastSlot.getStartTime().plusHours(4));
        slotFactory.save(lastSlot);

        Shift shift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slots.get(0).getStartTime(), slots.get(1).getEndTime(), 1);
        Shift shiftShopper = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slots.get(0).getStartTime().minusHours(1), slots.get(1).getEndTime().minusHours(1), 1);
        List<Batch> batches = batchFactory.createBatches(1, user, slots.get(0));
        List<Batch> initialShoppingBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).collect(Collectors.toList());
        List<Batch> initialDeliveryBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).collect(Collectors.toList());

        Batch batch1 = initialDeliveryBatches.get(0);
        batch1.setEndTime(batch1.getStartTime().plusMinutes(80));
        batch1.setVehicle(1);
        batch1.setShift(shift);

        Batch shoppingBatch1 = initialShoppingBatches.get(0);
        shoppingBatch1.setStartTime(shiftShopper.getStartTime());
        shoppingBatch1.setEndTime(shoppingBatch1.getStartTime().plusMinutes(80));
        shoppingBatch1.setVehicle(1);
        shoppingBatch1.setShift(shiftShopper);

        batchFactory.saveAll(Lists.newArrayList(batch1, shoppingBatch1));

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, lastSlot.getId(), 1, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            List<Batch> sortedShoppingBatches = shoppingBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch shoppingBatch = sortedShoppingBatches.get(0);
            Assert.assertEquals(shoppingBatch1.getEndTime(), shoppingBatch.getStartTime());
            Assert.assertEquals(shiftShopper.getId(), shoppingBatch.getShift().getId());

            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            List<Batch> sortedDeliveryBatches = deliveryBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch deliveryBatch = sortedDeliveryBatches.get(0);
            Assert.assertEquals(lastSlot.getStartTime(), deliveryBatch.getStartTime());
            Assert.assertEquals(shift.getId(), deliveryBatch.getShift().getId());
        });
    }

    /**
     * Slot    00:00-03:00, 03:00-06:00, 06:00-09:00
     * Shift shopping 1 23:00-08:00, count 2
     * Shift delivery 1 00:00-09:00, count 2
     * Batch 1, slot 00:00-03:00, shopping 23:00-23:50, delivery 00:00-02:50, ssv1 sdv1
     * Batch 2, slot 00:00-03:00, shopping 23:00-00:20, delivery 00:20-03:10, ssv2 sdv2
     * Batch 3, slot 03:00-06:00, shopping 23:50-00:50, delivery 03:00-05:50, ssv1 sdv1
     * Set slot 1 on 03:00-06:00, should make shopping 00:20-00:30 and delivery 00:00 - 00:24, ssv2 sdv2
     * Set slot 2 on 03:00-06:00, should make shopping 00:30-00:40 and delivery 00:24 - 00:48, ssv2 sdv2
     **/
    @Test
    public void shouldAssignToEarliestLatestBatchCase7() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);

        /** Cluster 1 */
        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 3, 3);
        Slot slot = slots.get(1);
        Shift shift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slots.get(0).getStartTime(), slots.get(2).getEndTime(), 2);
        Shift shiftShopper = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slots.get(0).getStartTime().minusHours(1), slots.get(2).getEndTime().minusHours(1), 2);

        /** Slot 00:00-03:00 */
        List<Batch> batches = batchFactory.createBatches(2, user, slots.get(0));
        List<Batch> initialShoppingBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());
        List<Batch> initialDeliveryBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());

        Batch shoppingBatch1 = initialShoppingBatches.get(0);
        shoppingBatch1.setStartTime(shiftShopper.getStartTime());
        shoppingBatch1.setEndTime(shoppingBatch1.getStartTime().plusMinutes(50));
        shoppingBatch1.setVehicle(1);
        shoppingBatch1.setShift(shiftShopper);

        Batch deliveryBatch1 = initialDeliveryBatches.get(0);
        deliveryBatch1.setEndTime(deliveryBatch1.getStartTime().plusMinutes(170));
        deliveryBatch1.setVehicle(1);
        deliveryBatch1.setShift(shift);

        Batch shoppingBatch2 = initialShoppingBatches.get(1);
        shoppingBatch2.setStartTime(shiftShopper.getStartTime());
        shoppingBatch2.setEndTime(shoppingBatch2.getStartTime().plusMinutes(80));
        shoppingBatch2.setVehicle(2);
        shoppingBatch2.setShift(shiftShopper);

        Batch deliveryBatch2 = initialDeliveryBatches.get(1);
        deliveryBatch2.setStartTime(shoppingBatch2.getEndTime());
        deliveryBatch2.setEndTime(deliveryBatch2.getStartTime().plusMinutes(170));
        deliveryBatch2.setVehicle(2);
        deliveryBatch2.setShift(shift);

        /** Slot 03:00-06:00 */
        List<Batch> batchesSlot2 = batchFactory.createBatches(1, user, slots.get(1));
        List<Batch> initialShoppingBatchesSlot2 = batchesSlot2.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());
        List<Batch> initialDeliveryBatchesSlot2 = batchesSlot2.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());

        Batch shoppingBatch3 = initialShoppingBatchesSlot2.get(0);
        shoppingBatch3.setStartTime(shoppingBatch1.getEndTime());
        shoppingBatch3.setEndTime(shoppingBatch3.getStartTime().plusMinutes(60));
        shoppingBatch3.setVehicle(1);
        shoppingBatch3.setShift(shiftShopper);

        Batch deliveryBatch3 = initialDeliveryBatchesSlot2.get(0);
        deliveryBatch3.setEndTime(deliveryBatch3.getStartTime().plusMinutes(170));
        deliveryBatch3.setVehicle(1);
        deliveryBatch3.setShift(shift);

        batchFactory.saveAll(Lists.newArrayList(shoppingBatch1, deliveryBatch1, shoppingBatch2, deliveryBatch2, shoppingBatch3, deliveryBatch3));

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(1).getId(), 1, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            List<Batch> sortedShoppingBatches = shoppingBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch shoppingBatch = sortedShoppingBatches.get(0);
            Assert.assertEquals(shoppingBatch2.getEndTime(), shoppingBatch.getStartTime());
            Assert.assertEquals(shoppingBatch2.getVehicle(), shoppingBatch.getVehicle());
            Assert.assertEquals(shiftShopper.getId(), shoppingBatch.getShift().getId());

            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            List<Batch> sortedDeliveryBatches = deliveryBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch deliveryBatch = sortedDeliveryBatches.get(0);
            Assert.assertEquals(slots.get(1).getStartTime(), deliveryBatch.getStartTime());
            Assert.assertEquals(deliveryBatch2.getVehicle(), deliveryBatch.getVehicle());
            Assert.assertEquals(shift.getId(), deliveryBatch.getShift().getId());
        });

        shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(1).getId(), 1, "Order-44", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            List<Batch> sortedShoppingBatches = shoppingBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch shoppingBatch = sortedShoppingBatches.get(0);
            Assert.assertEquals(shoppingBatch2.getEndTime().plusMinutes(10), shoppingBatch.getStartTime());
            Assert.assertEquals(shoppingBatch2.getVehicle(), shoppingBatch.getVehicle());
            Assert.assertEquals(shiftShopper.getId(), shoppingBatch.getShift().getId());

            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            List<Batch> sortedDeliveryBatches = deliveryBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch deliveryBatch = sortedDeliveryBatches.get(0);
            Assert.assertEquals(slots.get(1).getStartTime().plusMinutes(24), deliveryBatch.getStartTime());
            Assert.assertEquals(deliveryBatch2.getVehicle(), deliveryBatch.getVehicle());
            Assert.assertEquals(shift.getId(), deliveryBatch.getShift().getId());
        });
    }

    /**
     * Slot    00:00-03:00, 03:00-06:00, 06:00-09:00
     * Shift shopping 1 23:00-08:00, count 1
     * Shift delivery 1 00:00-09:00, count 1
     * Set slot 1 on 00:00-03:00, should make shopping 23:00-23:10 and delivery 00:00 - 00:24
     * Set slot 2 on 03:00-06:00, should make shopping 23:10-23:20 and delivery 03:00 - 03:24
     **/
    @Test
    public void shouldAssignToEarliestLatestBatchCase8() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);

        /** Cluster 1 */
        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 3, 3);
        Shift shift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slots.get(0).getStartTime(), slots.get(2).getEndTime(), 1);
        Shift shiftShopper = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slots.get(0).getStartTime().minusHours(1), slots.get(2).getEndTime().minusHours(1), 1);

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 1, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());

        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            List<Batch> sortedShoppingBatches = shoppingBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch shoppingBatch = sortedShoppingBatches.get(0);
            Assert.assertEquals(shiftShopper.getStartTime(), shoppingBatch.getStartTime());
            Assert.assertEquals(1, (int) shoppingBatch.getVehicle());
            Assert.assertEquals(shiftShopper.getId(), shoppingBatch.getShift().getId());

            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            List<Batch> sortedDeliveryBatches = deliveryBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch deliveryBatch = sortedDeliveryBatches.get(0);
            Assert.assertEquals(slots.get(0).getStartTime(), deliveryBatch.getStartTime());
            Assert.assertEquals(1, (int) deliveryBatch.getVehicle());
            Assert.assertEquals(shift.getId(), deliveryBatch.getShift().getId());
        });

        adjustShipment(user, shipmentObject, "Order-1");

        shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(1).getId(), 1, "Order-44", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());

        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            List<Batch> sortedShoppingBatches = shoppingBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch shoppingBatch = sortedShoppingBatches.get(0);
            Assert.assertEquals(shiftShopper.getStartTime().plusMinutes(10), shoppingBatch.getStartTime());
            Assert.assertEquals(1, (int) shoppingBatch.getVehicle());
            Assert.assertEquals(shiftShopper.getId(), shoppingBatch.getShift().getId());

            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            List<Batch> sortedDeliveryBatches = deliveryBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch deliveryBatch = sortedDeliveryBatches.get(0);
            Assert.assertEquals(slots.get(1).getStartTime(), deliveryBatch.getStartTime());
            Assert.assertEquals(1, (int) deliveryBatch.getVehicle());
            Assert.assertEquals(shift.getId(), deliveryBatch.getShift().getId());
        });
    }

    /**
     * Slot    00:00-03:00, 03:00-06:00, 06:00-09:00
     * Shift shopping 1 23:00-08:00, count 3
     * Shift delivery 1 00:00-09:00, count 3
     * Batch 1, slot 00:00-03:00, shopping 23:00-23:50, delivery 00:00-02:50, ssv1 sdv1
     * Batch 2, slot 00:00-03:00, shopping 23:00-23:40, delivery 00:00-01:10, ssv2 sdv2
     * Batch 3, slot 00:00-03:00, shopping 23:00-23:35, delivery 00:00-01:20, ssv3 sdv3
     * Batch 4, slot 03:00-06:00, shopping 23:40-00:50, delivery 03:00-04:40, ssv2 sdv1
     * Batch 5, slot 06:00-09:00, shopping 23:35-00:35, delivery 06:00-07:10, ssv3 sdv1
     * Set slot 1 on 00:00-03:00, should make shopping 00:35-00:55 and delivery 01:10 - 01:34, ssv3 sdv2
     * Set slot 2 on 03:00-06:00, should make shopping 23:50-00:00 and delivery 03:00 - 03:24, ssv1 sdv2
     **/
    @Test
    public void shouldAssignToEarliestLatestBatchCase9() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);

        /** Cluster 1 */
        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 3, 3);
        Shift shift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slots.get(0).getStartTime(), slots.get(2).getEndTime(), 3);
        Shift shiftShopper = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slots.get(0).getStartTime().minusHours(1), slots.get(2).getEndTime().minusHours(1), 3);

        /** Slot 00:00-03:00 */
        List<Batch> batches = batchFactory.createBatches(3, user, slots.get(0));
        List<Batch> initialShoppingBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());
        List<Batch> initialDeliveryBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());

        Batch shoppingBatch1 = initialShoppingBatches.get(0);
        shoppingBatch1.setStartTime(shiftShopper.getStartTime());
        shoppingBatch1.setEndTime(shoppingBatch1.getStartTime().plusMinutes(50));
        shoppingBatch1.setVehicle(1);
        shoppingBatch1.setShift(shiftShopper);

        Batch deliveryBatch1 = initialDeliveryBatches.get(0);
        deliveryBatch1.setEndTime(deliveryBatch1.getStartTime().plusMinutes(170));
        deliveryBatch1.setVehicle(1);
        deliveryBatch1.setShift(shift);

        Batch shoppingBatch2 = initialShoppingBatches.get(1);
        shoppingBatch2.setStartTime(shiftShopper.getStartTime());
        shoppingBatch2.setEndTime(shoppingBatch2.getStartTime().plusMinutes(40));
        shoppingBatch2.setVehicle(2);
        shoppingBatch2.setShift(shiftShopper);

        Batch deliveryBatch2 = initialDeliveryBatches.get(1);
        deliveryBatch2.setEndTime(deliveryBatch2.getStartTime().plusMinutes(70));
        deliveryBatch2.setVehicle(2);
        deliveryBatch2.setShift(shift);

        Batch shoppingBatch3 = initialShoppingBatches.get(2);
        shoppingBatch3.setStartTime(shiftShopper.getStartTime());
        shoppingBatch3.setEndTime(shoppingBatch3.getStartTime().plusMinutes(35));
        shoppingBatch3.setVehicle(3);
        shoppingBatch3.setShift(shiftShopper);

        Batch deliveryBatch3 = initialDeliveryBatches.get(2);
        deliveryBatch3.setEndTime(deliveryBatch3.getStartTime().plusMinutes(80));
        deliveryBatch3.setVehicle(3);
        deliveryBatch3.setShift(shift);

        /** Slot 03:00-06:00 */
        List<Batch> batchesSlot2 = batchFactory.createBatches(1, user, slots.get(1));
        List<Batch> initialShoppingBatchesSlot2 = batchesSlot2.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());
        List<Batch> initialDeliveryBatchesSlot2 = batchesSlot2.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());

        Batch shoppingBatch4 = initialShoppingBatchesSlot2.get(0);
        shoppingBatch4.setStartTime(shoppingBatch2.getEndTime());
        shoppingBatch4.setEndTime(shoppingBatch4.getStartTime().plusMinutes(70));
        shoppingBatch4.setVehicle(shoppingBatch2.getVehicle());
        shoppingBatch4.setShift(shiftShopper);

        Batch deliveryBatch4 = initialDeliveryBatchesSlot2.get(0);
        deliveryBatch4.setEndTime(deliveryBatch4.getStartTime().plusMinutes(100));
        deliveryBatch4.setVehicle(1);
        deliveryBatch4.setShift(shift);

        /** Slot 06:00-09:00 */
        List<Batch> batchesSlot3 = batchFactory.createBatches(1, user, slots.get(2));
        List<Batch> initialShoppingBatchesSlot3 = batchesSlot3.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());
        List<Batch> initialDeliveryBatchesSlot3 = batchesSlot3.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());

        Batch shoppingBatch5 = initialShoppingBatchesSlot3.get(0);
        shoppingBatch5.setStartTime(shoppingBatch3.getEndTime());
        shoppingBatch5.setEndTime(shoppingBatch5.getStartTime().plusMinutes(60));
        shoppingBatch5.setVehicle(shoppingBatch3.getVehicle());
        shoppingBatch5.setShift(shiftShopper);

        Batch deliveryBatch5 = initialDeliveryBatchesSlot3.get(0);
        deliveryBatch5.setEndTime(deliveryBatch5.getStartTime().plusMinutes(170));
        deliveryBatch5.setVehicle(1);
        deliveryBatch5.setShift(shift);

        batchFactory.saveAll(Lists.newArrayList(shoppingBatch1, deliveryBatch1, shoppingBatch2, deliveryBatch2, shoppingBatch3, deliveryBatch3, shoppingBatch4, deliveryBatch4, shoppingBatch5, deliveryBatch5));
        printPlan(user, stockLocation, slots.get(0).getStartTime());

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 2, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slots.get(0).getStartTime());
        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            List<Batch> sortedShoppingBatches = shoppingBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch shoppingBatch = sortedShoppingBatches.get(0);
            Assert.assertEquals(shoppingBatch5.getEndTime(), shoppingBatch.getStartTime());
            Assert.assertEquals(shoppingBatch5.getVehicle(), shoppingBatch.getVehicle());
            Assert.assertEquals(shoppingBatch5.getShift().getId(), shoppingBatch.getShift().getId());

            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            List<Batch> sortedDeliveryBatches = deliveryBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch deliveryBatch = sortedDeliveryBatches.get(0);
            Assert.assertEquals(deliveryBatch2.getEndTime(), deliveryBatch.getStartTime());
            Assert.assertEquals(deliveryBatch2.getVehicle(), deliveryBatch.getVehicle());
            Assert.assertEquals(shift.getId(), deliveryBatch.getShift().getId());
        });

        shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(1).getId(), 1, "Order-44", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slots.get(0).getStartTime());
        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            List<Batch> sortedShoppingBatches = shoppingBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch shoppingBatch = sortedShoppingBatches.get(0);
            Assert.assertEquals(shoppingBatch1.getEndTime(), shoppingBatch.getStartTime());
            Assert.assertEquals(shoppingBatch1.getVehicle(), shoppingBatch.getVehicle());
            Assert.assertEquals(shiftShopper.getId(), shoppingBatch.getShift().getId());

            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            List<Batch> sortedDeliveryBatches = deliveryBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch deliveryBatch = sortedDeliveryBatches.get(0);
            Assert.assertEquals(slots.get(1).getStartTime(), deliveryBatch.getStartTime());
            Assert.assertEquals(2, (int) deliveryBatch.getVehicle());
            Assert.assertEquals(shift.getId(), deliveryBatch.getShift().getId());
        });
    }

    /**
     * Slot    00:00-02:00, 02:00-04:00, 05:00-07:00, 07:00-09:00
     * Shift shopping 1 00:00-08:00, count 3
     * Shift delivery 1 01:00-09:00, count 3
     **/
    @Test
    public void shouldAssignToEarliestLatestBatchCase10() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);

        /** Slot configuration */
        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 2, 4);
        Slot slot3 = slots.get(2);
        slot3.setStartTime(slot3.getStartTime().plusHours(1));
        slot3.setEndTime(slot3.getEndTime().plusHours(1));
        slotFactory.save(slot3);
        Slot slot4 = slots.get(3);
        slot4.setStartTime(slot4.getStartTime().plusHours(1));
        slot4.setEndTime(slot4.getEndTime().plusHours(1));
        slotFactory.save(slot4);

        /** Shift configuration */
        Shift shopperShift = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slots.get(0).getStartTime(), slot4.getEndTime().minusHours(1), 3);
        Shift driverShift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slots.get(0).getStartTime(), slot4.getEndTime(), 3);

        /** Slot 00:00-02:00 */
        List<Batch> batches = batchFactory.createBatches(3, user, slots.get(0));
        List<Batch> initialShoppingBatches1 = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());
        List<Batch> initialDeliveryBatches1 = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());

        /**  Batch 1,  slot 00:00-02:00, shopping 00:00-00:50, delivery 00:50-01:20, ssv1 sdv1 */
        Batch shoppingBatch1 = setBatchDetails(initialShoppingBatches1.get(0), shopperShift.getStartTime(), shopperShift.getStartTime().plusMinutes(50), 1, shopperShift);
        Batch deliveryBatch1 = setBatchDetails(initialDeliveryBatches1.get(0), shoppingBatch1.getEndTime(), shoppingBatch1.getEndTime().plusMinutes(30), 1, driverShift);

        /**  Batch 2,  slot 00:00-02:00, shopping 00:00-00:40, delivery 00:40-01:10, ssv2 sdv2 */
        Batch shoppingBatch2 = setBatchDetails(initialShoppingBatches1.get(1), shopperShift.getStartTime(), shopperShift.getStartTime().plusMinutes(40), 2, shopperShift);
        Batch deliveryBatch2 = setBatchDetails(initialDeliveryBatches1.get(1), shoppingBatch2.getEndTime(), shoppingBatch1.getEndTime().plusMinutes(30), 2, driverShift);

        /**  Batch 3,  slot 00:00-02:00, shopping 00:00-00:30, delivery 00:30-01:00, ssv3 sdv3 */
        Batch shoppingBatch3 = setBatchDetails(initialShoppingBatches1.get(2), shopperShift.getStartTime(), shopperShift.getStartTime().plusMinutes(30), 3, shopperShift);
        Batch deliveryBatch3 = setBatchDetails(initialDeliveryBatches1.get(2), shoppingBatch3.getEndTime(), shoppingBatch1.getEndTime().plusMinutes(30), 3, driverShift);

        /** Slot 02:00-04:00 */
        List<Batch> batchesSlot2 = batchFactory.createBatches(4, user, slots.get(1));
        List<Batch> initialShoppingBatchesSlot2 = batchesSlot2.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());
        List<Batch> initialDeliveryBatchesSlot2 = batchesSlot2.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());

        /**  Batch 4,  slot 02:00-04:00, shopping 00:50-01:40, delivery 02:00-02:50, ssv1 sdv1 */
        Batch shoppingBatch4 = setBatchDetails(initialShoppingBatchesSlot2.get(0), shoppingBatch1.getEndTime(), shoppingBatch1.getEndTime().plusMinutes(50), shoppingBatch1.getVehicle(), shopperShift);
        Batch deliveryBatch4 = setBatchDetails(initialDeliveryBatchesSlot2.get(0), slots.get(1).getStartTime(), slots.get(1).getStartTime().plusMinutes(50), 1, driverShift);

        /**  Batch 5,  slot 02:00-04:00, shopping 00:40-01:20, delivery 02:00-02:40, ssv2 sdv2 */
        Batch shoppingBatch5 = setBatchDetails(initialShoppingBatchesSlot2.get(1), shoppingBatch2.getEndTime(), shoppingBatch2.getEndTime().plusMinutes(40), shoppingBatch2.getVehicle(), shopperShift);
        Batch deliveryBatch5 = setBatchDetails(initialDeliveryBatchesSlot2.get(1), slots.get(1).getStartTime(), slots.get(1).getStartTime().plusMinutes(40), 2, driverShift);

        /**  Batch 6,  slot 02:00-04:00, shopping 00:30-01:30, delivery 02:00-02:45, ssv3 sdv3 */
        Batch shoppingBatch6 = setBatchDetails(initialShoppingBatchesSlot2.get(2), shoppingBatch3.getEndTime(), shoppingBatch3.getEndTime().plusMinutes(60), shoppingBatch3.getVehicle(), shopperShift);
        Batch deliveryBatch6 = setBatchDetails(initialDeliveryBatchesSlot2.get(2), slots.get(1).getStartTime(), slots.get(1).getStartTime().plusMinutes(45), 3, driverShift);

        /**  Batch 7,  slot 02:00-04:00, shopping 01:20-02:00, delivery 02:40-03:10, ssv2 sdv2 */
        Batch shoppingBatch7 = setBatchDetails(initialShoppingBatchesSlot2.get(3), shoppingBatch5.getEndTime(), shoppingBatch5.getEndTime().plusMinutes(40), shoppingBatch5.getVehicle(), shopperShift);
        Batch deliveryBatch7 = setBatchDetails(initialDeliveryBatchesSlot2.get(3), deliveryBatch5.getEndTime(), deliveryBatch5.getEndTime().plusMinutes(30), deliveryBatch5.getVehicle(), driverShift);

        /** Slot 05:00-07:00 */
        List<Batch> batchesSlot3 = batchFactory.createBatches(3, user, slots.get(2));
        List<Batch> initialShoppingBatchesSlot3 = batchesSlot3.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());
        List<Batch> initialDeliveryBatchesSlot3 = batchesSlot3.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());

        /**  Batch 8,  slot 05:00-07:00, shopping 01:30-02:10, delivery 05:00-05:30, ssv3 sdv1 */
        Batch shoppingBatch8 = setBatchDetails(initialShoppingBatchesSlot3.get(0), shoppingBatch6.getEndTime(), shoppingBatch6.getEndTime().plusMinutes(40), shoppingBatch6.getVehicle(), shopperShift);
        Batch deliveryBatch8 = setBatchDetails(initialDeliveryBatchesSlot3.get(0), slots.get(2).getStartTime(), slots.get(2).getStartTime().plusMinutes(30), 1, driverShift);

        /**  Batch 9,  slot 05:00-07:00, shopping 01:40-02:20, delivery 05:00-05:40, ssv1 sdv2 */
        Batch shoppingBatch9 = setBatchDetails(initialShoppingBatchesSlot3.get(1), shoppingBatch4.getEndTime(), shoppingBatch4.getEndTime().plusMinutes(40), shoppingBatch4.getVehicle(), shopperShift);
        Batch deliveryBatch9 = setBatchDetails(initialDeliveryBatchesSlot3.get(1), slots.get(2).getStartTime(), slots.get(2).getStartTime().plusMinutes(40), 2, driverShift);

        /**  Batch 10, slot 05:00-07:00, shopping 02:00-02:50, delivery 05:00-05:50, ssv2 sdv3 */
        Batch shoppingBatch10 = setBatchDetails(initialShoppingBatchesSlot3.get(2), shoppingBatch7.getEndTime(), shoppingBatch7.getEndTime().plusMinutes(50), shoppingBatch7.getVehicle(), shopperShift);
        Batch deliveryBatch10 = setBatchDetails(initialDeliveryBatchesSlot3.get(2), slots.get(2).getStartTime(), slots.get(2).getStartTime().plusMinutes(50), 3, driverShift);

        /** Slot 07:00-09:00 */
        List<Batch> batchesSlot4 = batchFactory.createBatches(3, user, slots.get(3));
        List<Batch> initialShoppingBatchesSlot4 = batchesSlot4.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());
        List<Batch> initialDeliveryBatchesSlot4 = batchesSlot4.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());

        /**  Batch 11, slot 07:00-09:00, shopping 02:10-03:30, delivery 07:00-07:30, ssv3 sdv1 */
        Batch shoppingBatch11 = setBatchDetails(initialShoppingBatchesSlot4.get(0), shoppingBatch8.getEndTime(), shoppingBatch8.getEndTime().plusMinutes(80), shoppingBatch8.getVehicle(), shopperShift);
        Batch deliveryBatch11 = setBatchDetails(initialDeliveryBatchesSlot4.get(0), slots.get(3).getStartTime(), slots.get(3).getStartTime().plusMinutes(30), 1, driverShift);

        /**  Batch 12, slot 07:00-09:00, shopping 02:20-03:20, delivery 07:00-07:40, ssv1 sdv2 */
        Batch shoppingBatch12 = setBatchDetails(initialShoppingBatchesSlot4.get(1), shoppingBatch9.getEndTime(), shoppingBatch9.getEndTime().plusMinutes(60), shoppingBatch9.getVehicle(), shopperShift);
        Batch deliveryBatch12 = setBatchDetails(initialDeliveryBatchesSlot4.get(1), slots.get(3).getStartTime(), slots.get(3).getStartTime().plusMinutes(40), 2, driverShift);

        /**  Batch 13, slot 07:00-09:00, shopping 02:50-03:10, delivery 07:00-07:50, ssv2 sdv3 */
        Batch shoppingBatch13 = setBatchDetails(initialShoppingBatchesSlot4.get(2), shoppingBatch10.getEndTime(), shoppingBatch10.getEndTime().plusMinutes(20), shoppingBatch10.getVehicle(), shopperShift);
        Batch deliveryBatch13 = setBatchDetails(initialDeliveryBatchesSlot4.get(2), slots.get(3).getStartTime(), slots.get(3).getStartTime().plusMinutes(50), 3, driverShift);

        /**  Set slot 1 on 05:00-07:00, should make shopping 03:20-03:40 and delivery 05:40 - 06:04, ssv1 sdv1 */
        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(2).getId(), 2, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slots.get(0).getStartTime());

        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            List<Batch> sortedShoppingBatches = shoppingBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch shoppingBatch = sortedShoppingBatches.get(0);
            Assert.assertEquals(shoppingBatch12.getEndTime(), shoppingBatch.getStartTime());
            Assert.assertEquals(shoppingBatch12.getVehicle(), shoppingBatch.getVehicle());
            Assert.assertEquals(shopperShift.getId(), shoppingBatch.getShift().getId());
            List<Batch> nextFollowingBatches = batchRepository.findAllBatchesByTypeAndStartTimeAndVehicleAndShift(Batch.Type.SHOPPING, shoppingBatch12.getEndTime(), shoppingBatch12.getVehicle(), shoppingBatch12.getShift().getId(), shoppingBatch.getJobs().get(0).getShipment().getId());
            Assert.assertEquals(0, nextFollowingBatches.size());

            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            List<Batch> sortedDeliveryBatches = deliveryBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch deliveryBatch = sortedDeliveryBatches.get(0);
            Assert.assertEquals(deliveryBatch8.getEndTime(), deliveryBatch.getStartTime());
            Assert.assertEquals(deliveryBatch8.getVehicle(), deliveryBatch.getVehicle());
            Assert.assertEquals(driverShift.getId(), deliveryBatch.getShift().getId());
        });

        /**  Set slot 2 on 05:00-07:00, should make shopping 03:30-03:50 and delivery 05:40 - 06:04, ssv1 sdv2 */
        shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(2).getId(), 2, "Order-2", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slots.get(0).getStartTime());

        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            List<Batch> sortedShoppingBatches = shoppingBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch shoppingBatch = sortedShoppingBatches.get(0);
            Assert.assertEquals(shoppingBatch11.getEndTime(), shoppingBatch.getStartTime());
            Assert.assertEquals(shoppingBatch11.getVehicle(), shoppingBatch.getVehicle());
            Assert.assertEquals(shopperShift.getId(), shoppingBatch.getShift().getId());
            List<Batch> nextFollowingBatches = batchRepository.findAllBatchesByTypeAndStartTimeAndVehicleAndShift(Batch.Type.SHOPPING, shoppingBatch11.getEndTime(), shoppingBatch11.getVehicle(), shoppingBatch11.getShift().getId(), shoppingBatch.getJobs().get(0).getShipment().getId());
            Assert.assertEquals(0, nextFollowingBatches.size());

            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            List<Batch> sortedDeliveryBatches = deliveryBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch deliveryBatch = sortedDeliveryBatches.get(0);
            Assert.assertEquals(deliveryBatch9.getEndTime(), deliveryBatch.getStartTime());
            Assert.assertEquals(deliveryBatch9.getVehicle(), deliveryBatch.getVehicle());
            Assert.assertEquals(driverShift.getId(), deliveryBatch.getShift().getId());
        });

        /**  Set slot 3 on 07:00-09:00, should make shopping 03:10-03:30 and delivery 07:30 - 07:54, ssv2 sdv1 */
        shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(3).getId(), 2, "Order-3", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slots.get(0).getStartTime());

        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            List<Batch> sortedShoppingBatches = shoppingBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch shoppingBatch = sortedShoppingBatches.get(0);
            Assert.assertEquals(shoppingBatch13.getEndTime(), shoppingBatch.getStartTime());
            Assert.assertEquals(shoppingBatch13.getVehicle(), shoppingBatch.getVehicle());
            Assert.assertEquals(shopperShift.getId(), shoppingBatch.getShift().getId());
            List<Batch> nextFollowingBatches = batchRepository.findAllBatchesByTypeAndStartTimeAndVehicleAndShift(Batch.Type.SHOPPING, shoppingBatch13.getEndTime(), shoppingBatch13.getVehicle(), shoppingBatch13.getShift().getId(), shoppingBatch.getJobs().get(0).getShipment().getId());
            Assert.assertEquals(0, nextFollowingBatches.size());

            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            List<Batch> sortedDeliveryBatches = deliveryBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch deliveryBatch = sortedDeliveryBatches.get(0);
            Assert.assertEquals(deliveryBatch11.getEndTime(), deliveryBatch.getStartTime());
            Assert.assertEquals(deliveryBatch11.getVehicle(), deliveryBatch.getVehicle());
            Assert.assertEquals(driverShift.getId(), deliveryBatch.getShift().getId());
        });

        /**  Set slot 4 on 07:00-09:00, should make shopping 03:30-03:50 and delivery 07:40 - 08:04, ssv2 sdv2 */
        shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(3).getId(), 2, "Order-4", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slots.get(0).getStartTime());

        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            List<Batch> sortedShoppingBatches = shoppingBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch shoppingBatch = sortedShoppingBatches.get(0);
            Assert.assertEquals(shoppingBatch13.getEndTime().plusMinutes(20), shoppingBatch.getStartTime());
            Assert.assertEquals(shoppingBatch13.getVehicle(), shoppingBatch.getVehicle());
            Assert.assertEquals(shopperShift.getId(), shoppingBatch.getShift().getId());
            List<Batch> nextFollowingBatches = batchRepository.findAllBatchesByTypeAndStartTimeAndVehicleAndShift(Batch.Type.SHOPPING, shoppingBatch13.getEndTime().plusMinutes(20), shoppingBatch13.getVehicle(), shoppingBatch13.getShift().getId(), shoppingBatch.getJobs().get(0).getShipment().getId());
            Assert.assertEquals(0, nextFollowingBatches.size());

            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            List<Batch> sortedDeliveryBatches = deliveryBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch deliveryBatch = sortedDeliveryBatches.get(0);
            Assert.assertEquals(deliveryBatch12.getEndTime(), deliveryBatch.getStartTime());
            Assert.assertEquals(deliveryBatch12.getVehicle(), deliveryBatch.getVehicle());
            Assert.assertEquals(driverShift.getId(), deliveryBatch.getShift().getId());
        });

        /**  Set slot 5 on 07:00-09:00, should make shopping 03:40-04:00 and delivery 07:50 - 08:14, ssv1 sdv3 */
        shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(3).getId(), 2, "Order-5", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slots.get(0).getStartTime());

        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            List<Batch> sortedShoppingBatches = shoppingBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch shoppingBatch = sortedShoppingBatches.get(0);
            Assert.assertEquals(shoppingBatch12.getEndTime().plusMinutes(20), shoppingBatch.getStartTime());
            Assert.assertEquals(shoppingBatch12.getVehicle(), shoppingBatch.getVehicle());
            Assert.assertEquals(shopperShift.getId(), shoppingBatch.getShift().getId());
            List<Batch> nextFollowingBatches = batchRepository.findAllBatchesByTypeAndStartTimeAndVehicleAndShift(Batch.Type.SHOPPING, shoppingBatch12.getEndTime().plusMinutes(20), shoppingBatch12.getVehicle(), shoppingBatch12.getShift().getId(), shoppingBatch.getJobs().get(0).getShipment().getId());
            Assert.assertEquals(0, nextFollowingBatches.size());

            List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
            List<Batch> sortedDeliveryBatches = deliveryBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList());
            Batch deliveryBatch = sortedDeliveryBatches.get(0);
            Assert.assertEquals(deliveryBatch13.getEndTime(), deliveryBatch.getStartTime());
            Assert.assertEquals(deliveryBatch13.getVehicle(), deliveryBatch.getVehicle());
            Assert.assertEquals(driverShift.getId(), deliveryBatch.getShift().getId());
        });
    }

    @Test
    public void testMultipleSetSlotCase1() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);

        /** Slot configuration */
        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 2, 4);
        Slot slot3 = slots.get(2);
        slot3.setStartTime(slot3.getStartTime().plusHours(1));
        slot3.setEndTime(slot3.getEndTime().plusHours(1));
        slotFactory.save(slot3);
        Slot slot4 = slots.get(3);
        slot4.setStartTime(slot4.getStartTime().plusHours(1));
        slot4.setEndTime(slot4.getEndTime().plusHours(1));
        slotFactory.save(slot4);

        /** Shift configuration */
        Shift shopperShift = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slots.get(0).getStartTime(), slot4.getEndTime().minusHours(1), 3);
        Shift driverShift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slots.get(0).getStartTime(), slot4.getEndTime(), 3);

        int numberOfShipment = 40;
        for (int i = 0; i < numberOfShipment; i++) {
            Thread.sleep(100);
            String shipmentNumber = "Order-" + i;
            int slotNumber = i / 10;
            int numberOfItem = generateNumber(1, 3);
            JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(slotNumber).getId(), numberOfItem, shipmentNumber, new GeoPoint(-6.2925049, 106.821986), "Address " + i);
            MvcResult mvcResults = setSlotRequest(shipmentObject, user);
            Assert.assertEquals(200, mvcResults.getResponse().getStatus());
            printPlan(user, stockLocation, slots.get(0).getStartTime());
            System.out.println("Iteration" + i + ": " + shipmentNumber + ", slot " + slotNumber + ", number of item " + numberOfItem);
        }
    }

    @Test
    public void testMultipleSetSlotCase2() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);

        /** Slot configuration */
        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 2, 4);
        Slot slot3 = slots.get(2);
        slot3.setStartTime(slot3.getStartTime().plusHours(1));
        slot3.setEndTime(slot3.getEndTime().plusHours(1));
        slotFactory.save(slot3);
        Slot slot4 = slots.get(3);
        slot4.setStartTime(slot4.getStartTime().plusHours(1));
        slot4.setEndTime(slot4.getEndTime().plusHours(1));
        slotFactory.save(slot4);

        /** Shift configuration */
        Shift shopperShift = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slots.get(0).getStartTime(), slot4.getEndTime().minusHours(1), 3);
        Shift driverShift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slots.get(0).getStartTime(), slot4.getEndTime(), 3);

        int numberOfShipment = 40;

        for (int i = 0; i < numberOfShipment; i++) {
            Thread.sleep(100);
            String shipmentNumber = "Order-" + i;
            int slotNumber = i % 4;
            int numberOfItem = generateNumber(1, 2);
            JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(slotNumber).getId(), numberOfItem, shipmentNumber, new GeoPoint(-6.2925049, 106.821986), "Address " + i);
            MvcResult mvcResults = setSlotRequest(shipmentObject, user);
            Assert.assertEquals(200, mvcResults.getResponse().getStatus());
            printPlan(user, stockLocation, slots.get(0).getStartTime());
            System.out.println("Iteration" + i + ": " + shipmentNumber + ", slot " + slotNumber + ", number of item " + numberOfItem);
        }

        transactionHelper.withNewTransaction(() -> {
            // This will make pooling maximum 2 order, because of volume constraint
            for (int i = 0; i < numberOfShipment; i++) {
                Shipment shipment = shipmentRepository.findByNumber("Order-" + i);
                shipment.setState(Shipment.State.READY);
                shipmentRepository.save(shipment);
            }
        });
        Thread.sleep(200);
        try {
            AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(user.getToken(), user.getTenant().getToken());
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            slotOptimizationService.shopperSlotOptimization(slots.get(0), shopperShift);
            Thread.sleep(200);
            slotOptimizationService.driverSlotOptimization(slots.get(0));
            slotOptimizationService.driverSlotOptimization(slots.get(1));
            slotOptimizationService.driverSlotOptimization(slots.get(2));
            slotOptimizationService.driverSlotOptimization(slots.get(3));
        } finally {
            SecurityContextHolder.clearContext();
        }
        printPlan(user, stockLocation, slots.get(0).getStartTime());
    }

    private Batch setBatchDetails(Batch batch, LocalDateTime startTime, LocalDateTime endTime, int vehicle, Shift shift) {
        batch.setStartTime(startTime);
        batch.setEndTime(endTime);
        batch.setVehicle(vehicle);
        batch.setShift(shift);
        batchFactory.save(batch);
        return batch;
    }

    private void printPlan(User user, StockLocation stockLocation, LocalDateTime checkDate) throws Exception {
        User admin = userFactory.createUserData(Role.Name.ADMIN, user.getTenant());
        String url = String.format("/api/admin/batches/by_cluster?cluster_id=%d&date=%s", stockLocation.getCluster().getId(), checkDate.format(DateTimeFormatter.ISO_DATE));

        String payload = mvc.perform(MockMvcRequestBuilders.get(url)
                .header("X-Fulfillment-User-Token", admin.getToken())
                .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(status().is2xxSuccessful())
                .andReturn().getResponse().getContentAsString();

        String data = "window.dataString = '" + payload + "'; window.clusterId = " + stockLocation.getCluster().getId() + "; window.clusterDate = '" + checkDate.format(DateTimeFormatter.ISO_DATE) + "';";
        System.out.println(data);
    }

    private void adjustShipment(User user, JSONObject shipmentObject, String shipmentNumber) throws Exception {
        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + shipmentNumber + "/adjust")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObject.toString()));
    }

    private int generateNumber(int min, int max) {
        return min + (int) (Math.random() * ((max - min) + 1));
    }

    @Test
    public void shouldCheckGrabExpressIfNoDriverAvailable() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(3, user, Slot.Type.LONGER_DELIVERY);
        StockLocation stockLocation = stockLocations.get(0);
        stockLocation.setEnableGrabExpress(true);
        stockLocation.setEnableGrabExpressCod(false);
        stockLocation.setEnableLalamove(false);
        stockLocationFactory.save(stockLocation);

        List<Slot> slots1 = slotFactory.createLongerDeliverySlots(stockLocations.get(0), user, 2, 4);
        List<Slot> slots2 = slotFactory.createLongerDeliverySlots(stockLocations.get(1), user, 2, 4);
        List<Slot> slots3 = slotFactory.createLongerDeliverySlots(stockLocations.get(2), user, 2, 4);
        Slot slot = slots1.get(0);


        Shift shopperShift1 = shiftFactory.createShift(stockLocations.get(0), user, Shift.Type.SHOPPER, slots1.get(0).getStartTime().minusHours(2), slots1.get(0).getStartTime().plusHours(6), 1);

        Shift driverStore1Shift1 = shiftFactory.createShift(stockLocations.get(0), user, Shift.Type.DRIVER, slots1.get(0).getStartTime(), slots1.get(0).getStartTime().plusHours(10), 0);
        Shift driverStore2Shift1 = shiftFactory.createShift(stockLocations.get(1), user, Shift.Type.DRIVER, slots2.get(0).getStartTime(), slots2.get(0).getStartTime().plusHours(10), 0);
        Shift driverStore3Shift1 = shiftFactory.createShift(stockLocations.get(2), user, Shift.Type.DRIVER, slots3.get(0).getStartTime(), slots3.get(0).getStartTime().plusHours(4), 0);
        Shift driverStore3Shift2 = shiftFactory.createShift(stockLocations.get(2), user, Shift.Type.DRIVER, slots3.get(0).getStartTime().plusHours(1), slots3.get(0).getStartTime().plusHours(8), 0);


        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocations.get(0), slots1.get(0).getId(), 5, "Order-45", new GeoPoint(-6.2925049, 106.821986), "Address 4");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        Batch deliveryBatch = batchRepository.findByType(Batch.Type.DELIVERY).get(0);
        Assert.assertNull(deliveryBatch.getShift());
        Assert.assertEquals(Batch.DeliveryType.TPL, deliveryBatch.getDeliveryType());
        Assert.assertEquals(Batch.TplType.GRAB_EXPRESS, deliveryBatch.getTplType());
    }

    @Test
    public void shouldCheckLalamoveIfNoDriverAvailable() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(3, user, Slot.Type.LONGER_DELIVERY);
        StockLocation stockLocation = stockLocations.get(0);
        stockLocation.setEnableGrabExpress(false);
        stockLocation.setEnableGrabExpressCod(false);
        stockLocation.setEnableLalamove(true);
        stockLocationFactory.save(stockLocation);
        lalamoveServiceTypeFactory.createServiceTypes(user, stockLocation);

        List<Slot> slots1 = slotFactory.createLongerDeliverySlots(stockLocations.get(0), user, 2, 4);
        List<Slot> slots2 = slotFactory.createLongerDeliverySlots(stockLocations.get(1), user, 2, 4);
        List<Slot> slots3 = slotFactory.createLongerDeliverySlots(stockLocations.get(2), user, 2, 4);
        Slot slot = slots1.get(0);


        Shift shopperShift1 = shiftFactory.createShift(stockLocations.get(0), user, Shift.Type.SHOPPER, slots1.get(0).getStartTime().minusHours(2), slots1.get(0).getStartTime().plusHours(6), 1);

        Shift driverStore1Shift1 = shiftFactory.createShift(stockLocations.get(0), user, Shift.Type.DRIVER, slots1.get(0).getStartTime(), slots1.get(0).getStartTime().plusHours(10), 0);
        Shift driverStore2Shift1 = shiftFactory.createShift(stockLocations.get(1), user, Shift.Type.DRIVER, slots2.get(0).getStartTime(), slots2.get(0).getStartTime().plusHours(10), 0);
        Shift driverStore3Shift1 = shiftFactory.createShift(stockLocations.get(2), user, Shift.Type.DRIVER, slots3.get(0).getStartTime(), slots3.get(0).getStartTime().plusHours(4), 0);
        Shift driverStore3Shift2 = shiftFactory.createShift(stockLocations.get(2), user, Shift.Type.DRIVER, slots3.get(0).getStartTime().plusHours(1), slots3.get(0).getStartTime().plusHours(8), 0);


        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocations.get(0), slots1.get(0).getId(), 5, "Order-45", new GeoPoint(-6.2925049, 106.821986), "Address 4");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        Batch deliveryBatch = batchRepository.findByType(Batch.Type.DELIVERY).get(0);
        Assert.assertNull(deliveryBatch.getShift());
        Assert.assertEquals(Batch.DeliveryType.TPL, deliveryBatch.getDeliveryType());
        Assert.assertEquals(Batch.TplType.LALAMOVE, deliveryBatch.getTplType());
    }

    /**
     * Slot    00:00-02:00, 02:00-04:00, 05:00-07:00, 07:00-09:00
     * Shift shopping 1 00:00-08:00, count 3
     * Shift delivery 1 01:00-09:00, count 3
     **/
    @Test
    public void setSlotAfterBatchesStartedCase1() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, user.getTenant());
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);

        /** Slot configuration */
        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 2, 4);
        Slot slot3 = slots.get(2);
        slot3.setStartTime(slot3.getStartTime().plusHours(1));
        slot3.setEndTime(slot3.getEndTime().plusHours(1));
        slotFactory.save(slot3);
        Slot slot4 = slots.get(3);
        slot4.setStartTime(slot4.getStartTime().plusHours(1));
        slot4.setEndTime(slot4.getEndTime().plusHours(1));
        slotFactory.save(slot4);

        /** Shift configuration */
        Shift shopperShift = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slots.get(0).getStartTime(), slot4.getEndTime().minusHours(1), 3);
        Shift driverShift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slots.get(0).getStartTime(), slot4.getEndTime(), 3);

        /** Slot 00:00-02:00 */
        List<Batch> batches = batchFactory.createBatches(3, user, slots.get(0));
        List<Batch> initialShoppingBatches1 = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());
        List<Batch> initialDeliveryBatches1 = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());

        /**  Batch 1,  slot 00:00-02:00, shopping 00:00-00:50, delivery 00:50-01:20, ssv1 sdv1 */
        Batch shoppingBatch1 = setBatchDetails(initialShoppingBatches1.get(0), shopperShift.getStartTime(), shopperShift.getStartTime().plusMinutes(50), 1, shopperShift);
        Batch deliveryBatch1 = setBatchDetails(initialDeliveryBatches1.get(0), shoppingBatch1.getEndTime(), shoppingBatch1.getEndTime().plusMinutes(30), 1, driverShift);

        /**  Batch 2,  slot 00:00-02:00, shopping 00:00-00:40, delivery 00:40-01:10, ssv2 sdv2 */
        Batch shoppingBatch2 = setBatchDetails(initialShoppingBatches1.get(1), shopperShift.getStartTime(), shopperShift.getStartTime().plusMinutes(40), 2, shopperShift);
        Batch deliveryBatch2 = setBatchDetails(initialDeliveryBatches1.get(1), shoppingBatch2.getEndTime(), shoppingBatch1.getEndTime().plusMinutes(30), 2, driverShift);

        /**  Batch 3,  slot 00:00-02:00, shopping 00:00-00:30, delivery 00:30-01:00, ssv3 sdv3 */
        Batch shoppingBatch3 = setBatchDetails(initialShoppingBatches1.get(2), shopperShift.getStartTime(), shopperShift.getStartTime().plusMinutes(30), 3, shopperShift);
        Batch deliveryBatch3 = setBatchDetails(initialDeliveryBatches1.get(2), shoppingBatch3.getEndTime(), shoppingBatch1.getEndTime().plusMinutes(30), 3, driverShift);

        /** Slot 02:00-04:00 */
        List<Batch> batchesSlot2 = batchFactory.createBatches(3, user, slots.get(1));
        List<Batch> initialShoppingBatchesSlot2 = batchesSlot2.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());
        List<Batch> initialDeliveryBatchesSlot2 = batchesSlot2.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());

        /**  Batch 4,  slot 02:00-04:00, shopping 00:50-01:40, delivery 02:00-02:50, ssv1 sdv1 */
        Batch shoppingBatch4 = setBatchDetails(initialShoppingBatchesSlot2.get(0), shoppingBatch1.getEndTime(), shoppingBatch1.getEndTime().plusMinutes(50), shoppingBatch1.getVehicle(), shopperShift);
        Batch deliveryBatch4 = setBatchDetails(initialDeliveryBatchesSlot2.get(0), slots.get(1).getStartTime(), slots.get(1).getStartTime().plusMinutes(50), 1, driverShift);

        /**  Batch 5,  slot 02:00-04:00, shopping 00:40-01:20, delivery 02:00-02:40, ssv2 sdv2 */
        Batch shoppingBatch5 = setBatchDetails(initialShoppingBatchesSlot2.get(1), shoppingBatch2.getEndTime(), shoppingBatch2.getEndTime().plusMinutes(40), shoppingBatch2.getVehicle(), shopperShift);
        Batch deliveryBatch5 = setBatchDetails(initialDeliveryBatchesSlot2.get(1), slots.get(1).getStartTime(), slots.get(1).getStartTime().plusMinutes(40), 2, driverShift);

        /**  Batch 6,  slot 02:00-04:00, shopping 00:30-01:30, delivery 02:00-02:45, ssv3 sdv3 */
        Batch shoppingBatch6 = setBatchDetails(initialShoppingBatchesSlot2.get(2), shoppingBatch3.getEndTime(), shoppingBatch3.getEndTime().plusMinutes(60), shoppingBatch3.getVehicle(), shopperShift);
        Batch deliveryBatch6 = setBatchDetails(initialDeliveryBatchesSlot2.get(2), slots.get(1).getStartTime(), slots.get(1).getStartTime().plusMinutes(45), 3, driverShift);

        startBatches(Lists.newArrayList(shoppingBatch1, shoppingBatch2, shoppingBatch3, shoppingBatch4, shoppingBatch5, shoppingBatch6), shopper);
        printPlan(user, stockLocation, slots.get(0).getStartTime());

        /**  Set slot 1 on 00:00-02:00, should make shopping 01:20-01:30 and delivery 01:00 - 01:24, ssv2 sdv3 */
        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 1, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slots.get(0).getStartTime());
        try {
            Shipment shipment = shipmentRepository.findByNumber("Order-1");
            shipment.setState(Shipment.State.READY);
            shipmentRepository.save(shipment);
            Thread.sleep(100);
            AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(user.getToken(), user.getTenant().getToken());
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            slotOptimizationService.shopperSlotOptimization(slots.get(0), shopperShift);
        } finally {
            SecurityContextHolder.clearContext();
        }
        printPlan(user, stockLocation, slots.get(0).getStartTime());
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByNumber("Order-1");
            Batch shoppingBatchShipment1 = shipment1.getShoppingJob().get().getBatch();
            startBatches(Lists.newArrayList(shoppingBatchShipment1), shopper);
        });
        /**  Set slot 1 on 00:00-02:00 */
        shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 2, "Order-2", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(422, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slots.get(0).getStartTime());
    }

    /**
     * Slot    00:00-02:00, 02:00-04:00, 05:00-07:00, 07:00-09:00
     * Shift shopping 1 00:00-08:00, count 3
     * Shift delivery 1 01:00-09:00, count 3
     **/
    @Test
    public void setSlotAfterBatchesStartedCase2() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, user.getTenant());
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);

        /** Slot configuration */
        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 2, 4);
        Slot slot3 = slots.get(2);
        slot3.setStartTime(slot3.getStartTime().plusHours(1));
        slot3.setEndTime(slot3.getEndTime().plusHours(1));
        slotFactory.save(slot3);
        Slot slot4 = slots.get(3);
        slot4.setStartTime(slot4.getStartTime().plusHours(1));
        slot4.setEndTime(slot4.getEndTime().plusHours(1));
        slotFactory.save(slot4);

        /** Shift configuration */
        Shift shopperShift = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slots.get(0).getStartTime(), slot4.getEndTime().minusHours(1), 3);
        Shift driverShift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slots.get(0).getStartTime(), slot4.getEndTime(), 3);

        /** Slot 00:00-02:00 */
        List<Batch> batches = batchFactory.createBatches(1, user, slots.get(0));
        List<Batch> initialShoppingBatches1 = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());
        List<Batch> initialDeliveryBatches1 = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());

        /**  Batch 1,  slot 00:00-02:00, shopping 00:00-00:50, delivery 00:50-01:20, ssv1 sdv1 */
        Batch shoppingBatch1 = setBatchDetails(initialShoppingBatches1.get(0), shopperShift.getStartTime(), shopperShift.getStartTime().plusMinutes(50), 1, shopperShift);
        Batch deliveryBatch1 = setBatchDetails(initialDeliveryBatches1.get(0), shoppingBatch1.getEndTime(), shoppingBatch1.getEndTime().plusMinutes(30), 1, driverShift);

        /** Slot 02:00-04:00 */
        List<Batch> batchesSlot2 = batchFactory.createBatches(1, user, slots.get(1));
        List<Batch> initialShoppingBatchesSlot2 = batchesSlot2.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());
        List<Batch> initialDeliveryBatchesSlot2 = batchesSlot2.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());

        /**  Batch 2,  slot 02:00-04:00, shopping 00:00-00:40, delivery 02:00-02:50, ssv1 sdv1 */
        Batch shoppingBatch2 = setBatchDetails(initialShoppingBatchesSlot2.get(0), shopperShift.getStartTime(), shopperShift.getStartTime().plusMinutes(40), 2, shopperShift);
        Batch deliveryBatch2 = setBatchDetails(initialDeliveryBatchesSlot2.get(0), slots.get(1).getStartTime(), slots.get(1).getStartTime().plusMinutes(50), 1, driverShift);

        /** Slot 05:00-07:00 */
        List<Batch> batchesSlot3 = batchFactory.createBatches(1, user, slots.get(2));
        List<Batch> initialShoppingBatchesSlot3 = batchesSlot3.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());
        List<Batch> initialDeliveryBatchesSlot3 = batchesSlot3.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).sorted(Comparator.comparing(Batch::getId)).collect(Collectors.toList());

        /**  Batch 3,  slot 05:00-07:00, shopping 00:00-00:45, delivery 05:00-05:30, ssv3 sdv1 */
        Batch shoppingBatch3 = setBatchDetails(initialShoppingBatchesSlot3.get(0), shopperShift.getStartTime(), shopperShift.getStartTime().plusMinutes(45), 3, shopperShift);
        Batch deliveryBatch3 = setBatchDetails(initialDeliveryBatchesSlot3.get(0), slots.get(2).getStartTime(), slots.get(2).getStartTime().plusMinutes(30), 1, driverShift);

        startBatches(Lists.newArrayList(shoppingBatch1, shoppingBatch2, shoppingBatch3), shopper);
        printPlan(user, stockLocation, slots.get(0).getStartTime());

        /**  Set slot 1 on 00:00-02:00, should make shopping 00:00-00:20 and delivery 00:20 - 00:24 */
        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 2, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slots.get(0).getStartTime());

        /** chose another slot on 05:00-07:00 */
        shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(2).getId(), 2, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slots.get(0).getStartTime());

        try {
            Shipment shipment = shipmentRepository.findByNumber("Order-1");
            shipment.setState(Shipment.State.READY);
            shipmentRepository.save(shipment);
            Thread.sleep(100);

            AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(user.getToken(), user.getTenant().getToken());
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            slotOptimizationService.shopperSlotOptimization(slots.get(0), shopperShift);
        } finally {
            SecurityContextHolder.clearContext();
        }
        printPlan(user, stockLocation, slots.get(0).getStartTime());
    }

    /**
     * Slot    00:00-02:00, 02:00-04:00, 05:00-07:00, 07:00-09:00
     * Shift shopping 1 00:00-08:00, count 3
     * Shift delivery 1 01:00-09:00, count 3
     **/
    @Test
    public void changeSlotBeforeCompleteShouldAssignToShiftStart() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);

        /** Slot configuration */
        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 2, 4);
        Slot slot3 = slots.get(2);
        slot3.setStartTime(slot3.getStartTime().plusHours(1));
        slot3.setEndTime(slot3.getEndTime().plusHours(1));
        slotFactory.save(slot3);
        Slot slot4 = slots.get(3);
        slot4.setStartTime(slot4.getStartTime().plusHours(1));
        slot4.setEndTime(slot4.getEndTime().plusHours(1));
        slotFactory.save(slot4);

        /** Shift configuration */
        Shift shopperShift = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slots.get(0).getStartTime(), slot4.getEndTime().minusHours(1), 3);
        Shift driverShift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slots.get(0).getStartTime(), slot4.getEndTime(), 3);

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(1).getId(), 2, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slots.get(0).getStartTime());

        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByNumber("Order-1");
            Batch shoppingBatchShipment1 = shipment1.getShoppingJob().get().getBatch();
            Batch deliveryBatchShipment1 = shipment1.getDeliveryJob().get().getBatch();
            Assert.assertEquals(shopperShift.getStartTime(), shoppingBatchShipment1.getStartTime());
            Assert.assertEquals(DateTimeUtil.getMaxDatesBetween(slots.get(1).getStartTime(), shoppingBatchShipment1.getEndTime()), deliveryBatchShipment1.getStartTime());
        });

        shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(3).getId(), 2, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slots.get(0).getStartTime());

        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByNumber("Order-1");
            Batch shoppingBatchShipment1 = shipment1.getShoppingJob().get().getBatch();
            Batch deliveryBatchShipment1 = shipment1.getDeliveryJob().get().getBatch();
            Assert.assertEquals(shopperShift.getStartTime(), shoppingBatchShipment1.getStartTime());
            Assert.assertEquals(DateTimeUtil.getMaxDatesBetween(slots.get(3).getStartTime(), shoppingBatchShipment1.getEndTime()), deliveryBatchShipment1.getStartTime());
        });

        shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(2).getId(), 2, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slots.get(0).getStartTime());

        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByNumber("Order-1");
            Batch shoppingBatchShipment1 = shipment1.getShoppingJob().get().getBatch();
            Batch deliveryBatchShipment1 = shipment1.getDeliveryJob().get().getBatch();
            Assert.assertEquals(shopperShift.getStartTime(), shoppingBatchShipment1.getStartTime());
            Assert.assertEquals(DateTimeUtil.getMaxDatesBetween(slots.get(2).getStartTime(), shoppingBatchShipment1.getEndTime()), deliveryBatchShipment1.getStartTime());
        });

        shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 2, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slots.get(0).getStartTime());

        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByNumber("Order-1");
            Batch shoppingBatchShipment1 = shipment1.getShoppingJob().get().getBatch();
            Batch deliveryBatchShipment1 = shipment1.getDeliveryJob().get().getBatch();
            Assert.assertEquals(shopperShift.getStartTime(), shoppingBatchShipment1.getStartTime());
            Assert.assertEquals(DateTimeUtil.getMaxDatesBetween(slots.get(0).getStartTime(), shoppingBatchShipment1.getEndTime()), deliveryBatchShipment1.getStartTime());
        });
    }

    @Test
    public void setSlot_multipleTimes_withDifferentStore_shouldCorrectlySetBatchStockLocation() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        Slot.Type dds = Slot.Type.LONGER_DELIVERY;
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, dds);
        StockLocation sl1 = stockLocations.get(0);
        StockLocation sl2 = stockLocations.get(1);
        Cluster cluster2 = new Cluster();
        cluster2.setSlotType(dds);
        cluster2.setName("CL-02");
        cluster2.setTenant(user.getTenant());
        cluster2.setCreatedBy(user.getId());
        clusterRepository.save(cluster2);
        sl2.setCluster(cluster2);
        stockLocationFactory.save(sl2);

        Slot slotSl1 = slotFactory.createLongerDeliverySlots(sl1, user, 1, 1).get(0);
        Slot slotSl2 = slotFactory.createLongerDeliverySlots(sl2, user, 1, 1).get(0);

        Shift sShiftSl1 = shiftFactory.createShopperShifts(sl1, slotSl1.getStartTime().minusHours(1), 1, 2, 1, user).get(0);
        Shift dShiftSl1 = shiftFactory.createDriverShifts(sl1, slotSl1.getStartTime(), 1, 2, 1, user).get(0);
        Shift sShiftSl2 = shiftFactory.createShopperShifts(sl2, slotSl2.getStartTime().minusHours(1), 1, 2, 1, user).get(0);
        Shift dShiftSl2 = shiftFactory.createDriverShifts(sl2, slotSl2.getStartTime(), 1, 2, 1, user).get(0);

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(sl1, slotSl1.getId(), 2, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());

        // 2nd set slot, different store
        shipmentObject.put("stock_location_id", sl2.getExternalId());
        shipmentObject.put("slot_id", slotSl2.getId());
        shipmentObject.put("number", "H2");
        mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());

        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByOrderNumber("Order-1");
            Batch shoppingBatch = shipment1.getShoppingJob().get().getBatch();
            Batch deliveryBatch = shipment1.getDeliveryJob().get().getBatch();
            Assert.assertEquals(slotSl2.getId(), shoppingBatch.getStockLocation().getId());
        });
    }

    @Test
    public void setStratoSlot_multipleTimes_withDifferentStore_shouldCorrectlySetBatchStockLocation() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        Slot.Type dds = Slot.Type.LONGER_DELIVERY;
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, dds);
        StockLocation sl1 = stockLocations.get(0);
        sl1.setEnabler(StockLocation.Enabler.HFC);
        sl1.setEnablerPlatform(StockLocation.EnablerPlatform.STRATO);
        stockLocationFactory.save(sl1);

        StockLocation sl2 = stockLocations.get(1);
        Cluster cluster2 = new Cluster();
        cluster2.setSlotType(dds);
        cluster2.setName("CL-02");
        cluster2.setTenant(user.getTenant());
        cluster2.setCreatedBy(user.getId());
        clusterRepository.save(cluster2);
        sl2.setCluster(cluster2);
        sl2.setEnabler(StockLocation.Enabler.HFC);
        sl2.setEnablerPlatform(StockLocation.EnablerPlatform.STRATO);
        stockLocationFactory.save(sl2);

        List<Slot> slotList1 = slotFactory.createLongerDeliverySlots(sl1, user, 1, 1);
        Slot slotSl1 = slotList1.get(0);
        List<Slot> slotList2 = slotFactory.createLongerDeliverySlots(sl2, user, 1, 1);
        Slot slotSl2 = slotList2.get(0);

        Shift sShiftSl1 = shiftFactory.createShopperShifts(sl1, slotSl1.getStartTime().minusHours(1), 1, 2, 1, user).get(0);
        Shift dShiftSl1 = shiftFactory.createDriverShifts(sl1, slotSl1.getStartTime(), 1, 2, 1, user).get(0);
        Shift sShiftSl2 = shiftFactory.createShopperShifts(sl2, slotSl2.getStartTime().minusHours(1), 1, 2, 1, user).get(0);
        Shift dShiftSl2 = shiftFactory.createDriverShifts(sl2, slotSl2.getStartTime(), 1, 2, 1, user).get(0);

        List<StratoShopperCapacity> toBeReturned = slotList1.stream()
                .map(_slot -> new StratoShopperCapacity(_slot.getId(), _slot.getStartTime(), true))
                .collect(Collectors.toList());
        Mockito.doReturn(toBeReturned)
                .when(stratoService).getShopperCapacity(any(String.class), anyLong(), anyInt(), anyList());
        Mockito.doReturn(true).when(stratoService).bookShopperCapacity(anyString(), anyLong(), anyInt(), any(Slot.class));

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(sl1, slotSl1.getId(), 2, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByOrderNumber("Order-1");
            Batch shoppingBatch = shipment1.getShoppingJob().get().getBatch();
            Batch deliveryBatch = shipment1.getDeliveryJob().get().getBatch();
            Assert.assertEquals(slotSl1.getId(), shoppingBatch.getStockLocation().getId());
        });

        // 2nd set slot, different store
        shipmentObject.put("stock_location_id", sl2.getExternalId());
        shipmentObject.put("slot_id", slotSl2.getId());
        shipmentObject.put("number", "H2");

        List<StratoShopperCapacity> toBeReturned2 = slotList2.stream()
                .map(_slot -> new StratoShopperCapacity(_slot.getId(), _slot.getStartTime(), true))
                .collect(Collectors.toList());
        Mockito.doReturn(toBeReturned2)
                .when(stratoService).getShopperCapacity(any(String.class), anyLong(), anyInt(), anyList());

        mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());

        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByOrderNumber("Order-1");
            Batch shoppingBatch = shipment1.getShoppingJob().get().getBatch();
            Batch deliveryBatch = shipment1.getDeliveryJob().get().getBatch();
            Assert.assertEquals(slotSl2.getId(), shoppingBatch.getStockLocation().getId());
        });
    }

    @Test
    public void setStratoSlot_multipleTimes_withDifferentStore_usingShopperCapacityEndpointV2_shouldCorrectlySetBatchStockLocation() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        Slot.Type dds = Slot.Type.LONGER_DELIVERY;
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, dds);
        StockLocation sl1 = stockLocations.get(0);
        sl1.setEnabler(StockLocation.Enabler.HFC);
        sl1.setEnablerPlatform(StockLocation.EnablerPlatform.STRATO);
        sl1.setPreferences(new HashMap<String, String>() {{
            put("use_strato_shopper_capacity_v2", "true");
        }});
        stockLocationFactory.save(sl1);

        StockLocation sl2 = stockLocations.get(1);
        Cluster cluster2 = new Cluster();
        cluster2.setSlotType(dds);
        cluster2.setName("CL-02");
        cluster2.setTenant(user.getTenant());
        cluster2.setCreatedBy(user.getId());
        clusterRepository.save(cluster2);
        sl2.setCluster(cluster2);
        sl2.setEnabler(StockLocation.Enabler.HFC);
        sl2.setEnablerPlatform(StockLocation.EnablerPlatform.STRATO);
        sl2.setPreferences(new HashMap<String, String>() {{
            put("use_strato_shopper_capacity_v2", "true");
        }});
        stockLocationFactory.save(sl2);

        List<Slot> slotList1 = slotFactory.createLongerDeliverySlots(sl1, user, 1, 1);
        Slot slotSl1 = slotList1.get(0);
        List<Slot> slotList2 = slotFactory.createLongerDeliverySlots(sl2, user, 1, 1);
        Slot slotSl2 = slotList2.get(0);

        Shift sShiftSl1 = shiftFactory.createShopperShifts(sl1, slotSl1.getStartTime().minusHours(1), 1, 2, 1, user).get(0);
        Shift dShiftSl1 = shiftFactory.createDriverShifts(sl1, slotSl1.getStartTime(), 1, 2, 1, user).get(0);
        Shift sShiftSl2 = shiftFactory.createShopperShifts(sl2, slotSl2.getStartTime().minusHours(1), 1, 2, 1, user).get(0);
        Shift dShiftSl2 = shiftFactory.createDriverShifts(sl2, slotSl2.getStartTime(), 1, 2, 1, user).get(0);

        List<StratoShopperCapacity> toBeReturned = slotList1.stream()
                .map(_slot -> new StratoShopperCapacity(_slot.getId(), _slot.getStartTime(), true))
                .collect(Collectors.toList());
        Mockito.doReturn(toBeReturned)
                .when(stratoService).getShopperCapacityV2(any(String.class), anyLong(), anyList(), anyList());
        Mockito.doReturn(true).when(stratoService).bookShopperCapacityV2(anyString(), anyLong(), anyList(), any(Slot.class));

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(sl1, slotSl1.getId(), 2, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByOrderNumber("Order-1");
            Batch shoppingBatch = shipment1.getShoppingJob().get().getBatch();
            Batch deliveryBatch = shipment1.getDeliveryJob().get().getBatch();
            Assert.assertEquals(slotSl1.getId(), shoppingBatch.getStockLocation().getId());
        });

        // 2nd set slot, different store
        shipmentObject.put("stock_location_id", sl2.getExternalId());
        shipmentObject.put("slot_id", slotSl2.getId());
        shipmentObject.put("number", "H2");

        List<StratoShopperCapacity> toBeReturned2 = slotList2.stream()
                .map(_slot -> new StratoShopperCapacity(_slot.getId(), _slot.getStartTime(), true))
                .collect(Collectors.toList());
        Mockito.doReturn(toBeReturned2)
                .when(stratoService).getShopperCapacityV2(any(String.class), anyLong(), anyList(), anyList());

        mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());

        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByOrderNumber("Order-1");
            Batch shoppingBatch = shipment1.getShoppingJob().get().getBatch();
            Batch deliveryBatch = shipment1.getDeliveryJob().get().getBatch();
            Assert.assertEquals(slotSl2.getId(), shoppingBatch.getStockLocation().getId());
        });
    }

    @Test
    public void setRangerSlot_multipleTimes_withDifferentStore_shouldCorrectlySetBatchStockLocation() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        Slot.Type dds = Slot.Type.LONGER_DELIVERY;
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, dds);
        StockLocation sl1 = stockLocations.get(0);
        sl1.setType(StockLocation.Type.SPECIAL);
        stockLocationFactory.save(sl1);

        StockLocation sl2 = stockLocations.get(1);
        Cluster cluster2 = new Cluster();
        cluster2.setSlotType(dds);
        cluster2.setName("CL-02");
        cluster2.setTenant(user.getTenant());
        cluster2.setCreatedBy(user.getId());
        clusterRepository.save(cluster2);
        sl2.setCluster(cluster2);
        sl2.setType(StockLocation.Type.SPECIAL);
        stockLocationFactory.save(sl2);

        List<Slot> slotList1 = slotFactory.createLongerDeliverySlots(sl1, user, 1, 1);
        Slot slotSl1 = slotList1.get(0);
        List<Slot> slotList2 = slotFactory.createLongerDeliverySlots(sl2, user, 1, 1);
        Slot slotSl2 = slotList2.get(0);

        Shift sShiftSl1 = shiftFactory.createShopperShifts(sl1, slotSl1.getStartTime().minusHours(1), 1, 2, 1, user).get(0);
        Shift dShiftSl1 = shiftFactory.createDriverShifts(sl1, slotSl1.getStartTime(), 1, 2, 1, user).get(0);
        Shift sShiftSl2 = shiftFactory.createShopperShifts(sl2, slotSl2.getStartTime().minusHours(1), 1, 2, 1, user).get(0);
        Shift dShiftSl2 = shiftFactory.createDriverShifts(sl2, slotSl2.getStartTime(), 1, 2, 1, user).get(0);

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(sl1, slotSl1.getId(), 2, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByOrderNumber("Order-1");
            Batch rangerBatch = shipment1.getRangerJob().get().getBatch();
            Assert.assertEquals(slotSl1.getId(), rangerBatch.getStockLocation().getId());
        });

        // 2nd set slot, different store
        shipmentObject.put("stock_location_id", sl2.getExternalId());
        shipmentObject.put("slot_id", slotSl2.getId());
        shipmentObject.put("number", "H2");


        mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());

        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByOrderNumber("Order-1");
            Batch rangerBatch = shipment1.getRangerJob().get().getBatch();
            Assert.assertEquals(slotSl2.getId(), rangerBatch.getStockLocation().getId());
        });
    }

    @Test
    public void setSlot_multipleTimes_fromRangerDdsToSndDds_shouldSuccessSetSlot() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        Slot.Type dds = Slot.Type.LONGER_DELIVERY;
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, dds);
        StockLocation sl1 = stockLocations.get(0);
        sl1.setType(StockLocation.Type.SPECIAL);
        stockLocationFactory.save(sl1);

        StockLocation sl2 = stockLocations.get(1);
        Cluster cluster2 = new Cluster();
        cluster2.setSlotType(Slot.Type.LONGER_DELIVERY);
        cluster2.setName("CL-02");
        cluster2.setTenant(user.getTenant());
        cluster2.setCreatedBy(user.getId());
        clusterRepository.save(cluster2);
        sl2.setCluster(cluster2);
        sl2.setType(StockLocation.Type.ORIGINAL);
        stockLocationFactory.save(sl2);

        List<Slot> slotList1 = slotFactory.createLongerDeliverySlots(sl1, user, 1, 1);
        Slot slotSl1 = slotList1.get(0);
        List<Slot> slotList2 = slotFactory.createLongerDeliverySlots(sl2, user, 1, 1);
        Slot slotSl2 = slotList2.get(0);

        Shift sShiftSl1 = shiftFactory.createShopperShifts(sl1, slotSl1.getStartTime().minusHours(1), 1, 2, 1, user).get(0);
        Shift dShiftSl1 = shiftFactory.createDriverShifts(sl1, slotSl1.getStartTime(), 1, 2, 1, user).get(0);
        Shift sShiftSl2 = shiftFactory.createShopperShifts(sl2, slotSl2.getStartTime().minusHours(1), 1, 2, 1, user).get(0);
        Shift dShiftSl2 = shiftFactory.createDriverShifts(sl2, slotSl2.getStartTime(), 1, 2, 1, user).get(0);

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(sl1, slotSl1.getId(), 2, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByOrderNumber("Order-1");
            Batch rangerBatch = shipment1.getRangerJob().get().getBatch();
            Assert.assertEquals(sl1.getId(), rangerBatch.getStockLocation().getId());
        });

        // 2nd set slot, different store
        shipmentObject.put("stock_location_id", sl2.getExternalId());
        shipmentObject.put("slot_id", slotSl2.getId());
        shipmentObject.put("number", "H2");

        mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByOrderNumber("Order-1");
            Batch shoppingBatch = shipment1.getShoppingJob().get().getBatch();
            Assert.assertEquals(sl2.getId(), shoppingBatch.getStockLocation().getId());
        });
    }

    @Test
    public void setSlot_multipleTimes_fromSndDdsToRangerDds_shouldSuccessSetSlot() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        Slot.Type dds = Slot.Type.LONGER_DELIVERY;
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, dds);
        StockLocation sl1 = stockLocations.get(0);
        sl1.setType(StockLocation.Type.ORIGINAL);
        stockLocationFactory.save(sl1);

        StockLocation sl2 = stockLocations.get(1);
        Cluster cluster2 = new Cluster();
        cluster2.setSlotType(Slot.Type.LONGER_DELIVERY);
        cluster2.setName("CL-02");
        cluster2.setTenant(user.getTenant());
        cluster2.setCreatedBy(user.getId());
        clusterRepository.save(cluster2);
        sl2.setCluster(cluster2);
        sl2.setType(StockLocation.Type.SPECIAL);
        stockLocationFactory.save(sl2);

        List<Slot> slotList1 = slotFactory.createLongerDeliverySlots(sl1, user, 1, 1);
        Slot slotSl1 = slotList1.get(0);
        List<Slot> slotList2 = slotFactory.createLongerDeliverySlots(sl2, user, 1, 1);
        Slot slotSl2 = slotList2.get(0);

        Shift sShiftSl1 = shiftFactory.createShopperShifts(sl1, slotSl1.getStartTime().minusHours(1), 1, 2, 1, user).get(0);
        Shift dShiftSl1 = shiftFactory.createDriverShifts(sl1, slotSl1.getStartTime(), 1, 2, 1, user).get(0);
        Shift sShiftSl2 = shiftFactory.createShopperShifts(sl2, slotSl2.getStartTime().minusHours(1), 1, 2, 1, user).get(0);
        Shift dShiftSl2 = shiftFactory.createDriverShifts(sl2, slotSl2.getStartTime(), 1, 2, 1, user).get(0);

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(sl1, slotSl1.getId(), 2, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByOrderNumber("Order-1");
            Batch shoppingBatch = shipment1.getShoppingJob().get().getBatch();
            Assert.assertEquals(sl1.getId(), shoppingBatch.getStockLocation().getId());
        });

        // 2nd set slot, different store
        shipmentObject.put("stock_location_id", sl2.getExternalId());
        shipmentObject.put("slot_id", slotSl2.getId());
        shipmentObject.put("number", "H2");

        mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByOrderNumber("Order-1");
            Batch rangerBatch = shipment1.getRangerJob().get().getBatch();
            Assert.assertEquals(sl2.getId(), rangerBatch.getStockLocation().getId());
        });
    }

    private void startBatches(List<Batch> batches, User user) {
        for (Batch batch : batches) {
            Job shoppingJob = batch.getJobs().stream().filter(Job::isShopping).findFirst().orElse(null);
            if (shoppingJob != null) {
                shoppingJob.setState(Job.State.STARTED);
                jobFactory.save(shoppingJob);
            }
            batch.setUser(user);
            batchFactory.save(batch);
        }
    }

    /**
     *  Slot    00:00-03:00, 03:00-06:00, 06:00-09:00
     *  Shift 1 00:00-09:00, store 1, count 1
     *  Shift 2 01:00-08:00, store 2, count 1
     *  Shift 3 00:30-08:00, store 3, count 1
     *  Set slot on store 3, slot 00:00-03:00 should be assigned to shift 3
     *  */
    @Test
    public void sndStoreShouldAssignToStoreShift() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(3, user, Slot.Type.LONGER_DELIVERY);

        List<Slot> slotsStore2 = slotFactory.createLongerDeliverySlots(stockLocations.get(1), user, 3, 3);
        List<Slot> slotsStore3 = slotFactory.createLongerDeliverySlots(stockLocations.get(2), user, 3, 3);
        Shift shiftStore1 = shiftFactory.createShift(stockLocations.get(0), user, Shift.Type.DRIVER, slotsStore2.get(0).getStartTime(), slotsStore2.get(2).getEndTime(), 1);
        Shift shopperShift1 = shiftFactory.createShift(stockLocations.get(1), user, Shift.Type.SHOPPER, slotsStore2.get(0).getStartTime(), slotsStore2.get(2).getEndTime().minusMinutes(60), 1);
        Shift shiftStore2 = shiftFactory.createShift(stockLocations.get(1), user, Shift.Type.DRIVER, slotsStore2.get(0).getStartTime().plusMinutes(60), slotsStore2.get(2).getEndTime().minusMinutes(60), 1);
        Shift shopperShift2 = shiftFactory.createShift(stockLocations.get(2), user, Shift.Type.SHOPPER, slotsStore2.get(0).getStartTime(), slotsStore2.get(2).getEndTime().minusMinutes(60), 1);
        Shift shiftStore3 = shiftFactory.createShift(stockLocations.get(2), user, Shift.Type.DRIVER, slotsStore2.get(0).getStartTime().plusMinutes(30), slotsStore2.get(2).getEndTime().minusMinutes(60), 1);

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocations.get(2), slotsStore3.get(0).getId(), 1, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            List<Batch> deliveryBatchesFinal = batchRepository.findByType(Batch.Type.DELIVERY);
            Assert.assertThat(deliveryBatchesFinal, Matchers.hasSize(1));
            Batch batch = deliveryBatchesFinal.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
            Assert.assertEquals(1, (int) batch.getVehicle());
            Assert.assertEquals(shiftStore3.getId(), batch.getShift().getId());
        });

        shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocations.get(1), slotsStore2.get(0).getId(), 1, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            List<Batch> deliveryBatchesFinal = batchRepository.findByType(Batch.Type.DELIVERY);
            Assert.assertThat(deliveryBatchesFinal, Matchers.hasSize(1));
            Batch batch = deliveryBatchesFinal.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
            Assert.assertEquals(1, (int) batch.getVehicle());
            Assert.assertEquals(shiftStore2.getId(), batch.getShift().getId());
        });

        shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocations.get(1), slotsStore2.get(0).getId(), 1, "Order-45", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            List<Batch> deliveryBatchesFinal = batchRepository.findByType(Batch.Type.DELIVERY);
            Assert.assertThat(deliveryBatchesFinal, Matchers.hasSize(2));
            Batch batch = deliveryBatchesFinal.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
            Assert.assertEquals(1, (int) batch.getVehicle());
            Assert.assertEquals(shiftStore2.getId(), batch.getShift().getId());
        });
    }

    @Test
    public void rangerStoreShouldCreateNonPoolingBatch() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user, Slot.Type.LONGER_DELIVERY).get(0);
        stockLocation.setShopperAveragePickingTimePerUniqItem(5);
        stockLocation.setShopperQueueReplacementTime(1);
        stockLocation.setType(StockLocation.Type.SPECIAL);
        stockLocationFactory.save(stockLocation);

        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 3, 1);
        Slot slot = slots.get(0);
        int numberOfDriver = 3;
        Shift driverShift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime(), slot.getStartTime().plusHours(10), numberOfDriver);

        int numberOfShipment = 12;

        for (int i = 0; i < numberOfShipment; i++) {
            Thread.sleep(100);
            JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 1, "Order-" + i, new GeoPoint(-6.2925049, 106.821986), "Address " + i);
            MvcResult mvcResults = setSlotRequest(shipmentObject, user);
            Assert.assertEquals(200, mvcResults.getResponse().getStatus());
            final int iteration = i;
            transactionHelper.withNewTransaction(() -> {
                List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.RANGER);
                Assert.assertThat(deliveryBatches, Matchers.hasSize(iteration + 1));
                Batch deliveryBatch = deliveryBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
                Assert.assertEquals((iteration % numberOfDriver) + 1, (int) deliveryBatch.getVehicle());
                Assert.assertEquals(driverShift.getId(), deliveryBatch.getShift().getId());
            });
        }
    }

    /**
     *  Slot    00:00-03:00, 03:00-06:00, 06:00-09:00
     *  Shift 1 00:00-09:00, store 1, count 3
     *  Vehicle 1 latest end time 02:40 - store 1 - shift 1
     *  Vehicle 2 latest end time 01:20 - store 2 - shift 1
     *  Vehicle 3 no batch
     *  */
    @Test
    public void rangerStoreShouldAssignToEarliestLatestBatchCase1() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);
        for (StockLocation stockLocation : stockLocations) {
            stockLocation.setShopperAveragePickingTimePerUniqItem(5);
            stockLocation.setShopperQueueReplacementTime(1);
            stockLocation.setType(StockLocation.Type.SPECIAL);
            stockLocationFactory.save(stockLocation);
        }

        /** Cluster 1 */
        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 3, 3);
        Slot slot = slots.get(0);
        Shift shift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime(), slots.get(2).getEndTime(), 3);
        List<Batch> batches = batchFactory.createBatches(1, user, slot);

        Batch batch1 = batches.get(0);
        batch1.setEndTime(batch1.getStartTime().plusMinutes(160));
        batch1.setVehicle(1);
        batch1.setShift(shift);

        StockLocation stockLocation2 = stockLocations.get(1);
        List<Slot> slots2 = slotFactory.createLongerDeliverySlots(stockLocation2, user, 3, 3);
        Slot slot2 = slots2.get(0);
        List<Batch> batches2 = batchFactory.createBatches(1, user, slot2);

        Batch batch2 = batches2.get(0);
        batch2.setEndTime(batch2.getStartTime().plusMinutes(80));
        batch2.setVehicle(2);
        batch2.setShift(shift);

        batchFactory.saveAll(Lists.newArrayList(batch1, batch2));

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 1, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            List<Batch> deliveryBatchesFinal = batchRepository.findByType(Batch.Type.RANGER);
            Assert.assertThat(deliveryBatchesFinal, Matchers.hasSize(3));
            Batch batch = deliveryBatchesFinal.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
            Assert.assertEquals(3, (int) batch.getVehicle());
            Assert.assertEquals(shift.getId(), batch.getShift().getId());
        });
    }

    /**
     *  Slot    00:00-03:00, 03:00-06:00, 06:00-09:00
     *  Shift 1 00:00-09:00, store 1, count 3
     *  Vehicle 1 latest end time 02:40 - store 1 - shift 1
     *  Vehicle 2 latest end time 01:20 - store 2 - shift 1
     *  Vehicle 3 latest end time 01:30 - store 2 - shift 1
     *  */
    @Test
    public void rangerStoreShouldAssignToEarliestLatestBatchCase2() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);
        for (StockLocation stockLocation : stockLocations) {
            stockLocation.setShopperAveragePickingTimePerUniqItem(5);
            stockLocation.setShopperQueueReplacementTime(1);
            stockLocation.setType(StockLocation.Type.SPECIAL);
            stockLocationFactory.save(stockLocation);
        }

        /** Cluster 1 */
        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 3, 3);
        Slot slot = slots.get(0);
        Shift shift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime(), slots.get(2).getEndTime(), 3);
        List<Batch> batches = batchFactory.createBatches(1, user, slot);

        Batch batch1 = batches.get(0);
        batch1.setEndTime(batch1.getStartTime().plusMinutes(160));
        batch1.setVehicle(1);
        batch1.setShift(shift);

        StockLocation stockLocation2 = stockLocations.get(1);
        List<Slot> slots2 = slotFactory.createLongerDeliverySlots(stockLocation2, user, 3, 3);
        Slot slot2 = slots2.get(0);
        List<Batch> batches2 = batchFactory.createBatches(2, user, slot2);

        Batch batch2 = batches2.get(0);
        batch2.setEndTime(batch2.getStartTime().plusMinutes(80));
        batch2.setVehicle(2);
        batch2.setShift(shift);

        Batch batch3 = batches2.get(1);
        batch3.setEndTime(batch3.getStartTime().plusMinutes(90));
        batch3.setVehicle(3);
        batch3.setShift(shift);

        batchFactory.saveAll(Lists.newArrayList(batch1, batch2, batch3));

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 1, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            List<Batch> deliveryBatchesFinal = batchRepository.findByType(Batch.Type.RANGER);
            Assert.assertThat(deliveryBatchesFinal, Matchers.hasSize(4));
            Batch batch = deliveryBatchesFinal.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
            Assert.assertEquals(2, (int) batch.getVehicle());
            Assert.assertEquals(shift.getId(), batch.getShift().getId());
        });
    }

    /**
     *  Slot    00:00-03:00, 03:00-06:00, 06:00-09:00
     *  Shift 1 00:00-09:00, store 1, count 1
     *  Shift 2 00:00-09:00, store 2, count 2
     *  Vehicle 1 latest end time 02:40 - store 1 - shift 1
     *  Vehicle 2 latest end time 01:20 - store 2 - shift 2
     *  Vehicle 3 no batch
     *  */
    @Test
    public void rangerStoreShouldAssignToEarliestLatestBatchCase3() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);
        for (StockLocation stockLocation : stockLocations) {
            stockLocation.setShopperAveragePickingTimePerUniqItem(5);
            stockLocation.setShopperQueueReplacementTime(1);
            stockLocation.setType(StockLocation.Type.SPECIAL);
            stockLocationFactory.save(stockLocation);
        }

        /** Cluster 1 */
        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 3, 3);
        Slot slot = slots.get(0);
        Shift shift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime(), slots.get(2).getEndTime(), 1);
        List<Batch> batches = batchFactory.createBatches(1, user, slot);

        Batch batch1 = batches.get(0);
        batch1.setEndTime(batch1.getStartTime().plusMinutes(160));
        batch1.setVehicle(1);
        batch1.setShift(shift);

        StockLocation stockLocation2 = stockLocations.get(1);
        List<Slot> slots2 = slotFactory.createLongerDeliverySlots(stockLocation2, user, 3, 3);
        Slot slot2 = slots2.get(0);
        final Shift shift2 = shiftFactory.createShift(stockLocation2, user, Shift.Type.DRIVER, slot2.getStartTime(), slots2.get(2).getEndTime(), 2);
        List<Batch> batches2 = batchFactory.createBatches(1, user, slot2);

        Batch batch2 = batches2.get(0);
        batch2.setEndTime(batch2.getStartTime().plusMinutes(80));
        batch2.setVehicle(1);
        batch2.setShift(shift2);

        batchFactory.saveAll(Lists.newArrayList(batch1, batch2));

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 1, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            List<Batch> deliveryBatchesFinal = batchRepository.findByType(Batch.Type.RANGER);
            Assert.assertThat(deliveryBatchesFinal, Matchers.hasSize(3));
            Batch batch = deliveryBatchesFinal.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
            Assert.assertEquals(2, (int) batch.getVehicle());
            Assert.assertEquals(shift2.getId(), batch.getShift().getId());
        });
    }

    /**
     *  Slot    00:00-03:00, 03:00-06:00, 06:00-09:00
     *  Shift 1 00:00-09:00, store 1, count 1
     *  Shift 2 00:00-09:00, store 2, count 2
     *  Vehicle 1 latest end time 02:40 - store 1 - shift 1
     *  Vehicle 2 latest end time 01:20 - store 2 - shift 2
     *  Vehicle 3 latest end time 01:30 - store 2 - shift 2
     *  */
    @Test
    public void rangerStoreShouldAssignToEarliestLatestBatchCase4() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);
        for (StockLocation stockLocation : stockLocations) {
            stockLocation.setShopperAveragePickingTimePerUniqItem(5);
            stockLocation.setShopperQueueReplacementTime(1);
            stockLocation.setType(StockLocation.Type.SPECIAL);
            stockLocationFactory.save(stockLocation);
        }

        /** Cluster 1 */
        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 3, 3);
        Slot slot = slots.get(0);
        Shift shift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime(), slots.get(2).getEndTime(), 1);
        List<Batch> batches = batchFactory.createBatches(1, user, slot);

        Batch batch1 = batches.get(0);
        batch1.setEndTime(batch1.getStartTime().plusMinutes(160));
        batch1.setVehicle(1);
        batch1.setShift(shift);

        StockLocation stockLocation2 = stockLocations.get(1);
        List<Slot> slots2 = slotFactory.createLongerDeliverySlots(stockLocation2, user, 3, 3);
        Slot slot2 = slots2.get(0);
        final Shift shift2 = shiftFactory.createShift(stockLocation2, user, Shift.Type.DRIVER, slot2.getStartTime(), slots2.get(2).getEndTime(), 2);
        List<Batch> batches2 = batchFactory.createBatches(2, user, slot2);

        Batch batch2 = batches2.get(0);
        batch2.setEndTime(batch2.getStartTime().plusMinutes(80));
        batch2.setVehicle(1);
        batch2.setShift(shift2);

        Batch batch3 = batches2.get(1);
        batch3.setEndTime(batch3.getStartTime().plusMinutes(90));
        batch3.setVehicle(2);
        batch3.setShift(shift2);

        batchFactory.saveAll(Lists.newArrayList(batch1, batch2, batch3));
        printPlan(user, stockLocation, slot.getStartTime());

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 1, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            List<Batch> deliveryBatchesFinal = batchRepository.findByType(Batch.Type.RANGER);
            Assert.assertThat(deliveryBatchesFinal, Matchers.hasSize(4));
            Batch batch = deliveryBatchesFinal.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
            Assert.assertEquals(1, (int) batch.getVehicle());
            Assert.assertEquals(shift2.getId(), batch.getShift().getId());
        });
    }

    /**
     *  Slot    00:00-03:00, 03:00-06:00, 06:00-09:00
     *  Shift 1 00:00-09:00, store 1, count 1
     *  Shift 2 01:00-08:00, store 2, count 1
     *  Shift 3 00:30-08:00, store 3, count 1
     *  Set slot on store 3, slot 00:00-03:00 should be assigned to shift 3
     *  */
    @Ignore("Find the earliest start instead of same store")
    @Test
    public void rangerStoreShouldAssignToStoreShift() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(3, user, Slot.Type.LONGER_DELIVERY);
        for (StockLocation stockLocation : stockLocations) {
            stockLocation.setShopperAveragePickingTimePerUniqItem(5);
            stockLocation.setShopperQueueReplacementTime(1);
            stockLocation.setType(StockLocation.Type.SPECIAL);
            stockLocationFactory.save(stockLocation);
        }

        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocations.get(2), user, 3, 3);
        Shift shiftStore1 = shiftFactory.createShift(stockLocations.get(0), user, Shift.Type.DRIVER, slots.get(0).getStartTime(), slots.get(2).getEndTime(), 1);
        Shift shiftStore2 = shiftFactory.createShift(stockLocations.get(1), user, Shift.Type.DRIVER, slots.get(0).getStartTime().plusMinutes(60), slots.get(2).getEndTime().minusMinutes(60), 1);
        Shift shiftStore3 = shiftFactory.createShift(stockLocations.get(2), user, Shift.Type.DRIVER, slots.get(0).getStartTime().plusMinutes(30), slots.get(2).getEndTime().minusMinutes(60), 1);

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocations.get(2), slots.get(0).getId(), 1, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        transactionHelper.withNewTransaction(() -> {
            List<Batch> deliveryBatchesFinal = batchRepository.findByType(Batch.Type.RANGER);
            Assert.assertThat(deliveryBatchesFinal, Matchers.hasSize(1));
            Batch batch = deliveryBatchesFinal.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
            Assert.assertEquals(1, (int) batch.getVehicle());
            Assert.assertEquals(shiftStore3.getId(), batch.getShift().getId());
        });
    }

    /**
     * Slot    00:00-02:00, 02:00-04:00, 05:00-07:00, 07:00-09:00
     * Shift delivery 1 00:00-09:00, count 3
     **/
    @Test
    public void changeSlotRangerBeforeCompleteShouldAssignToSlotStartMinusShopping() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);


        /** Slot configuration */
        StockLocation stockLocation = stockLocations.get(0);
        stockLocation.setType(StockLocation.Type.SPECIAL);
        stockLocationFactory.save(stockLocation);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 2, 4);
        Slot slot3 = slots.get(2);
        slot3.setStartTime(slot3.getStartTime().plusHours(1));
        slot3.setEndTime(slot3.getEndTime().plusHours(1));
        slotFactory.save(slot3);
        Slot slot4 = slots.get(3);
        slot4.setStartTime(slot4.getStartTime().plusHours(1));
        slot4.setEndTime(slot4.getEndTime().plusHours(1));
        slotFactory.save(slot4);

        /** Shift configuration */
        Shift driverShift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slots.get(0).getStartTime(), slot4.getEndTime(), 3);

        int numberOfItems = 2;
        long shoppingTime = (long) (numberOfItems * stockLocation.getShopperAveragePickingTimePerUniqItem());
        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(1).getId(), numberOfItems, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slots.get(0).getStartTime());

        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByNumber("Order-1");
            Batch deliveryBatchShipment1 = shipment1.getRangerJob().get().getBatch();
            Assert.assertEquals(slots.get(1).getStartTime().minusMinutes(shoppingTime), deliveryBatchShipment1.getStartTime());
        });

        shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(3).getId(), numberOfItems, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slots.get(0).getStartTime());

        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByNumber("Order-1");
            Batch deliveryBatchShipment1 = shipment1.getRangerJob().get().getBatch();
            Assert.assertEquals(slots.get(3).getStartTime().minusMinutes(shoppingTime), deliveryBatchShipment1.getStartTime());
        });

        shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(2).getId(), numberOfItems, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slots.get(0).getStartTime());

        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByNumber("Order-1");
            Batch deliveryBatchShipment1 = shipment1.getRangerJob().get().getBatch();
            Assert.assertEquals(slots.get(2).getStartTime().minusMinutes(shoppingTime), deliveryBatchShipment1.getStartTime());
        });

        shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), numberOfItems, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slots.get(0).getStartTime());

        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByNumber("Order-1");
            Batch deliveryBatchShipment1 = shipment1.getRangerJob().get().getBatch();
            Assert.assertEquals(DateTimeUtil.getMaxDatesBetween(driverShift.getStartTime(),slots.get(0).getStartTime().minusMinutes(shoppingTime)), deliveryBatchShipment1.getStartTime());
        });
    }

    @Test
    public void shouldCreateNonPoolingBatchOnMultiShoppingShift() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user, Slot.Type.LONGER_DELIVERY).get(0);
        List<GeoPoint> addresses = Lists.newArrayList(new GeoPoint( -6.2915547, 106.7977159), new GeoPoint(-6.291033, 106.817397), new GeoPoint(-6.291202, 106.7855));
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 3, 1);
        Slot slot = slots.get(0);
        int numberOfDriver = 3;
        Shift shopperShift = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime(), slot.getStartTime().plusHours(8), 1);
        Shift shopperShift2 = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().plusHours(1), slot.getStartTime().plusHours(9), 1);
        Shift driverShift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime(), slot.getStartTime().plusHours(10), numberOfDriver);

        int numberOfShipment = 12;

        for (int i = 0; i < numberOfShipment; i++) {
            Thread.sleep(100);
            JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 1, "Order-" + i, addresses.get(i%3), "Address " + i);
            MvcResult mvcResults = setSlotRequest(shipmentObject, user);
            Assert.assertEquals(200, mvcResults.getResponse().getStatus());
            final int iteration = i;
            printPlan(user, stockLocation, slot.getStartTime());
            transactionHelper.withNewTransaction(() -> {
                List<Batch> deliveryBatches = batchRepository.findByType(Batch.Type.DELIVERY);
                Assert.assertThat(deliveryBatches, Matchers.hasSize(iteration + 1));
                Batch deliveryBatch = deliveryBatches.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
                Assert.assertEquals((iteration % numberOfDriver) + 1, (int) deliveryBatch.getVehicle());
                Assert.assertEquals(driverShift.getId(), deliveryBatch.getShift().getId());
            });
        }
        printPlan(user, stockLocation, slots.get(0).getStartTime());
    }

    @Test
    public void mixClusterSetSlotTest() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);
        int cnt = 0;
        for (StockLocation stockLocation : stockLocations) {
            stockLocation.setShopperAveragePickingTimePerUniqItem(5);
            stockLocation.setShopperQueueReplacementTime(1);
            if (cnt > 0) {
                stockLocation.setType(StockLocation.Type.SPECIAL);
            } else {
                stockLocation.setMaxDeliveryVolume(10);
            }
            stockLocationFactory.save(stockLocation);
            cnt++;
        }

        /** Ranger Store */
        StockLocation rangerStore = stockLocations.stream().filter(sl -> sl.isSpecial()).findFirst().get();
        List<Slot> rangerSlots = slotFactory.createLongerDeliverySlots(rangerStore, user, 3, 3);
        Shift rangerShift = shiftFactory.createShift(rangerStore, user, Shift.Type.DRIVER, rangerSlots.get(0).getStartTime(), rangerSlots.get(2).getEndTime(), 1);

        /** Snd Store */
        StockLocation sndStore = stockLocations.stream().filter(sl -> !sl.isSpecial()).findFirst().get();
        List<Slot> sndSlots = slotFactory.createLongerDeliverySlots(sndStore, user, 3, 3);
        Shift shopperShift = shiftFactory.createShift(sndStore, user, Shift.Type.SHOPPER, sndSlots.get(0).getStartTime(), sndSlots.get(2).getEndTime(), 2);
        Shift driverShift = shiftFactory.createShift(sndStore, user, Shift.Type.DRIVER, sndSlots.get(0).getStartTime(), sndSlots.get(2).getEndTime(), 1);

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(sndStore, sndSlots.get(1).getId(), 2, "Order-2", new GeoPoint(-6.2915547, 106.7977159), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, sndStore, sndSlots.get(0).getStartTime());

        shipmentObject = shipmentJsonObjectFactory.createShipment(rangerStore, rangerSlots.get(1).getId(), 2, "Order-1", new GeoPoint(-6.291033, 106.817397), "Address 1");
        mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, rangerStore, rangerSlots.get(0).getStartTime());

        try {
            // This will make pooling maximum 2 order, because of volume constraint
            transactionHelper.withNewTransaction(() -> {
                for (Shipment shipment : shipmentRepository.findAll()) {
                    Item item = itemRepository.findAllByShipmentId(shipment.getId()).get(0);
                    item.setDepth(200.0);
                    item.setWidth(20.0);
                    item.setHeight(1.0);
                    itemRepository.save(item);
                    shipment.setState(Shipment.State.READY);
                    shipmentRepository.save(shipment);
                }
            });
            Thread.sleep(100);

            AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(user.getToken(), user.getTenant().getToken());
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            slotOptimizationService.shopperSlotOptimization(sndSlots.get(1), shopperShift);
            slotOptimizationService.driverSlotOptimization(sndSlots.get(1));
        } finally {
            SecurityContextHolder.clearContext();
        }
        printPlan(user, sndStore, sndSlots.get(0).getStartTime());

        shipmentObject = shipmentJsonObjectFactory.createShipment(sndStore, sndSlots.get(1).getId(), 2, "Order-4", new GeoPoint(-6.291202, 106.7855), "Address 1");
        mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, sndStore, sndSlots.get(0).getStartTime());

        try {
            // This will make pooling maximum 2 order, because of volume constraint
            transactionHelper.withNewTransaction(() -> {
                for (Shipment shipment : shipmentRepository.findAll()) {
                    Item item = itemRepository.findAllByShipmentId(shipment.getId()).get(0);
                    item.setDepth(200.0);
                    item.setWidth(20.0);
                    item.setHeight(1.0);
                    itemRepository.save(item);
                    shipment.setState(Shipment.State.READY);
                    shipmentRepository.save(shipment);
                }
            });
            Thread.sleep(100);

            AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(user.getToken(), user.getTenant().getToken());
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            slotOptimizationService.shopperSlotOptimization(sndSlots.get(1), shopperShift);
            slotOptimizationService.driverSlotOptimization(sndSlots.get(1));
        } finally {
            SecurityContextHolder.clearContext();
        }
        printPlan(user, sndStore, sndSlots.get(0).getStartTime());

        transactionHelper.withNewTransaction(() -> {
            Shipment shipment1 = shipmentRepository.findByNumber("Order-1");
            Batch deliveryBatchShipment1 = shipment1.getDeliveryOrRangerJob().get().getBatch();
            Assert.assertEquals(Batch.Type.RANGER, deliveryBatchShipment1.getType());


            Shipment shipment2 = shipmentRepository.findByNumber("Order-2");
            Batch deliveryBatchShipment2 = shipment2.getDeliveryOrRangerJob().get().getBatch();
            Assert.assertEquals(Batch.Type.DELIVERY, deliveryBatchShipment2.getType());


            Shipment shipment3 = shipmentRepository.findByNumber("Order-4");
            Batch deliveryBatchShipment3 = shipment3.getDeliveryOrRangerJob().get().getBatch();
            Assert.assertEquals(Batch.Type.DELIVERY, deliveryBatchShipment3.getType());
        });
    }

    /**
     *  Slot    00:00-01:00, 01:00-02:00, 02:00-03:00, 03:00-04:00, 04:00-05:00, 05:00-06:00
     *  Shift 1 00:00-09:00, store 1, count 1
     *  Vehicle 1 latest end time 00:00 - 01:10 - store 1 - shift 1
     *  */
    @Test
    public void widerRangerCase1() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);
        for (StockLocation stockLocation : stockLocations) {
            stockLocation.setShopperAveragePickingTimePerUniqItem(5);
            stockLocation.setShopperQueueReplacementTime(1);
            stockLocation.setType(StockLocation.Type.SPECIAL);
            stockLocationFactory.save(stockLocation);
        }

        /** Cluster 1 */
        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 1, 6);
        Slot slot = slots.get(0);
        Shift shift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime(), slots.get(2).getEndTime(), 1);
        List<Batch> batches = batchFactory.createBatches(1, user, slot);

        Batch batch1 = batches.get(0);
        batch1.setEndTime(batch1.getStartTime().plusMinutes(70));
        batch1.setVehicle(1);
        batch1.setShift(shift);

        StockLocation stockLocation2 = stockLocations.get(1);
        List<Slot> slots2 = slotFactory.createLongerDeliverySlots(stockLocation2, user, 1, 6);

        batchFactory.saveAll(Lists.newArrayList(batch1));
        printPlan(user, stockLocation, slot.getStartTime());

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(2).getId(), 12, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slot.getStartTime());
        transactionHelper.withNewTransaction(() -> {
            List<Batch> deliveryBatchesFinal = batchRepository.findByType(Batch.Type.RANGER);
            Assert.assertThat(deliveryBatchesFinal, Matchers.hasSize(2));
            Batch batch = deliveryBatchesFinal.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
            Assert.assertEquals(1, (int) batch.getVehicle());
            Assert.assertEquals(shift.getId(), batch.getShift().getId());
            Assert.assertEquals(batch1.getEndTime(), batch.getStartTime());
        });
    }

    /**
     *  Slot    00:00-01:00, 01:00-02:00, 02:00-03:00, 03:00-04:00, 04:00-05:00, 05:00-06:00
     *  Shift 1 00:00-09:00, store 1, count 1
     *  Shift 1 00:00-09:00, store 1, count 1
     *  Vehicle 1 latest end time 00:00 - 01:10 - store 1 - shift 1
     *  */
    @Test
    public void widerRangerCase2() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);
        for (StockLocation stockLocation : stockLocations) {
            stockLocation.setShopperAveragePickingTimePerUniqItem(5);
            stockLocation.setShopperQueueReplacementTime(1);
            stockLocation.setType(StockLocation.Type.SPECIAL);
            stockLocationFactory.save(stockLocation);
        }

        /** Cluster 1 */
        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 1, 6);
        Slot slot = slots.get(0);
        Shift shift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime(), slots.get(2).getEndTime(), 1);
        Shift shift2 = shiftFactory.createShift(stockLocations.get(1), user, Shift.Type.DRIVER, slot.getStartTime(), slots.get(2).getEndTime(), 1);
        List<Batch> batches = batchFactory.createBatches(1, user, slot);

        Batch batch1 = batches.get(0);
        batch1.setEndTime(batch1.getStartTime().plusMinutes(70));
        batch1.setVehicle(1);
        batch1.setShift(shift);

        StockLocation stockLocation2 = stockLocations.get(1);
        List<Slot> slots2 = slotFactory.createLongerDeliverySlots(stockLocation2, user, 1, 6);

        batchFactory.saveAll(Lists.newArrayList(batch1));
        printPlan(user, stockLocation, slot.getStartTime());

        int numOfItems = 12;
        Long shoppingTime = (long) ((stockLocation.getShopperAveragePickingTimePerUniqItem() * numOfItems) + stockLocation.getShopperQueueReplacementTime());
        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(2).getId(), 12, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slot.getStartTime());
        transactionHelper.withNewTransaction(() -> {
            List<Batch> deliveryBatchesFinal = batchRepository.findByType(Batch.Type.RANGER);
            Assert.assertThat(deliveryBatchesFinal, Matchers.hasSize(2));
            Batch batch = deliveryBatchesFinal.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
            Assert.assertEquals(1, (int) batch.getVehicle());
            Assert.assertEquals(shift2.getId(), batch.getShift().getId());
            Assert.assertEquals(slots.get(2).getStartTime().minusMinutes(shoppingTime), batch.getStartTime());
        });
    }

    /**
     *  Slot    00:00-01:00, 01:00-02:00, 02:00-03:00, 03:00-04:00, 04:00-05:00, 05:00-06:00
     *  Shift 1 00:00-09:00, store 1, count 1
     *  Batch1 1 latest end time 00:00 - 01:10 - store 1 - shift 1
     *  Batch1 2, Slot 03:00-04:00, Duration 80, 02:10 - 03:30 - store 1 - shift 1
     *  Shipment 1, Slot 02:00-03:00, Duration 155, 02:30 - 02:24 - store 1 - shift 1
     *  */
    @Test
    public void widerRangerCase3() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);
        for (StockLocation stockLocation : stockLocations) {
            stockLocation.setShopperAveragePickingTimePerUniqItem(5);
            stockLocation.setShopperQueueReplacementTime(1);
            stockLocation.setType(StockLocation.Type.SPECIAL);
            stockLocationFactory.save(stockLocation);
        }

        /** Cluster 1 */
        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 1, 6);
        Slot slot = slots.get(0);
        Shift shift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot.getStartTime(), slots.get(2).getEndTime(), 1);
        List<Batch> batches1 = batchFactory.createBatches(1, user, slot);

        Batch batch1 = batches1.get(0);
        batch1.setEndTime(batch1.getStartTime().plusMinutes(70));
        batch1.setVehicle(1);
        batch1.setShift(shift);

        StockLocation stockLocation2 = stockLocations.get(1);
        List<Slot> slots2 = slotFactory.createLongerDeliverySlots(stockLocation2, user, 1, 6);

        List<Batch> batches2 = batchFactory.createBatches(1, user, slots2.get(3));
        Batch batch2 = batches2.get(0);
        batch2.setStartTime(slots2.get(3).getStartTime().minusMinutes(50));
        batch2.setEndTime(slots2.get(3).getStartTime().plusMinutes(30));
        batch2.setVehicle(1);
        batch2.setShift(shift);

        batchFactory.saveAll(Lists.newArrayList(batch1, batch2));
        printPlan(user, stockLocation, slot.getStartTime());

        int numOfItems = 12;
        Long shoppingTime = (long) ((stockLocation.getShopperAveragePickingTimePerUniqItem() * numOfItems) + stockLocation.getShopperQueueReplacementTime());
        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots2.get(2).getId(), 12, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slot.getStartTime());
        transactionHelper.withNewTransaction(() -> {
            List<Batch> deliveryBatchesFinal = batchRepository.findByType(Batch.Type.RANGER);
            Assert.assertThat(deliveryBatchesFinal, Matchers.hasSize(3));
            Batch batch = deliveryBatchesFinal.stream().sorted(Comparator.comparing(Batch::getId).reversed()).collect(Collectors.toList()).get(0);
            Assert.assertEquals(1, (int) batch.getVehicle());
            Assert.assertEquals(shift.getId(), batch.getShift().getId());
            Assert.assertEquals(batch1.getEndTime(), batch.getStartTime());
            Assert.assertEquals(batch1.getEndTime().plusMinutes(85), batch.getEndTime());
        });
    }

    @Test
    public void rangerStoreHasNoDriverShiftButShopperShiftAndTPLEnabled() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);
        for (StockLocation stockLocation : stockLocations) {
            stockLocation.setShopperAveragePickingTimePerUniqItem(5);
            stockLocation.setShopperQueueReplacementTime(1);
            stockLocation.setType(StockLocation.Type.SPECIAL);
            /** Enable lalamove */
            stockLocation.setEnableLalamove(true);
            stockLocation.setPreferences(new HashMap<String, String>() {{
                put("enable_lalamove_delivery_fee", "6000");
                put("lalamove_flat_service_fee", "1000");
            }});
            stockLocationFactory.save(stockLocation);
        }

        StockLocation stockLocation = stockLocations.get(0);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 3, 3);
        Slot slot = slots.get(0);
        Shift shopperShift = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot.getStartTime().minusHours(1), slots.get(2).getEndTime(), 1);

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 1, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(422, mvcResults.getResponse().getStatus());
    }

    @Test
    public void testSetSlotAndOptimizedOnEnablerStore() throws Exception {
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user, Slot.Type.LONGER_DELIVERY);

        /** Slot configuration */
        StockLocation stockLocation = stockLocations.get(0);
        stockLocation.setEnabler(StockLocation.Enabler.HYPERMART);
        stockLocationFactory.save(stockLocation);
        List<Slot> slots = slotFactory.createLongerDeliverySlots(stockLocation, user, 1, 4);

        /** Shift configuration */
        Shift shopperShift = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slots.get(0).getStartTime(), slots.get(3).getEndTime().minusHours(1), 1);
        Shift driverShift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slots.get(0).getStartTime(), slots.get(3).getEndTime(), 1);

        JSONObject shipmentObject = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(1).getId(), 2, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        MvcResult mvcResults = setSlotRequest(shipmentObject, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slots.get(0).getStartTime());

        JSONObject shipmentObject1 = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(3).getId(), 2, "Order-2", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        mvcResults = setSlotRequest(shipmentObject1, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slots.get(0).getStartTime());

        JSONObject shipmentObject2 = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(2).getId(), 2, "Order-3", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        mvcResults = setSlotRequest(shipmentObject2, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slots.get(0).getStartTime());

        JSONObject shipmentObject3 = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(2).getId(), 1, "Order-4", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        mvcResults = setSlotRequest(shipmentObject3, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slots.get(0).getStartTime());

        JSONObject shipmentObject4 = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(0).getId(), 2, "Order-1", new GeoPoint(-6.2925049, 106.821986), "Address 1");
        mvcResults = setSlotRequest(shipmentObject4, user);
        Assert.assertEquals(200, mvcResults.getResponse().getStatus());
        printPlan(user, stockLocation, slots.get(0).getStartTime());

        transactionHelper.withNewTransaction(() -> {
            // This will make pooling maximum 2 order, because of volume constraint
            for (int i = 1; i <= 4; i++) {
                Shipment shipment = shipmentRepository.findByNumber("Order-" + i);
                if (shipment == null)
                    continue;
                shipment.setState(Shipment.State.READY);
                shipmentRepository.save(shipment);
            }
        });

        Thread.sleep(200);
        try {
            AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(user.getToken(), user.getTenant().getToken());
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            slotOptimizationService.shopperSlotOptimization(slots.get(0), shopperShift);
            Thread.sleep(200);
            slotOptimizationService.driverSlotOptimization(slots.get(0));
        } finally {
            SecurityContextHolder.clearContext();
        }
        printPlan(user, stockLocation, slots.get(0).getStartTime());

        transactionHelper.withNewTransaction(() -> {
            List<Batch> shoppingBatches = batchRepository.findByType(Batch.Type.SHOPPING);
            Assert.assertEquals(4, shoppingBatches.size());
            for (Batch batch : shoppingBatches) {
                Shipment shipment = batch.getJobs().get(0).getShipment();
                Slot slot = shipment.getSlot();
                Assert.assertTrue(batch.getStartTime().isBefore(slot.getEndTime()));
            }
        });
    }
}
