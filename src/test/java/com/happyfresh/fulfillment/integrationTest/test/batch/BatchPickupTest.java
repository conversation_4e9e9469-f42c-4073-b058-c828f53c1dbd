package com.happyfresh.fulfillment.integrationTest.test.batch;

import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.JobRepository;
import org.hamcrest.Matchers;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.List;

import static org.hamcrest.Matchers.equalTo;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

public class BatchPickupTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Test
    public void return200IfSuccess() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, driver);

        List<Batch> batches = batchFactory.createBatches(1, driver, driver, slots.get(0));
        Batch shoppingBatches = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).findAny().get();
        shoppingBatches.getJobs().stream().forEach(job -> {
            job.setState(Job.State.FINISHED);
            jobRepository.save(job);
        });

        Batch deliveryBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).findAny().get();

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/"+ deliveryBatch.getId() +"/pickup")
                .header("X-Fulfillment-User-Token", driver.getToken())
                .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.shipments[0].delivery_job.state", Matchers.equalTo(Job.State.STARTED.toString())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.shipments[0].delivery_job.states[*].state", Matchers.containsInAnyOrder(Job.State.INITIAL.toString(), Job.State.STARTED.toString())))
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    public void return200IfSomeShipmentsAreCancelled() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);
        User externalSystemAdmin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN, driver.getTenant());
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, driver);

        Slot slot = slots.get(0);
        Shipment shipment1 = shipmentFactory.createShipment(slot, driver);
        Shipment shipment2 = shipmentFactory.createShipment(slot, driver);
        List<Shipment> shipments = Lists.newArrayList(shipment1, shipment2);

        Batch shoppingBatch = batchFactory.createBatch(driver, shipments, slots.get(0), Batch.Type.SHOPPING);

        shoppingBatch.getJobs().stream().forEach(job -> {
            job.setState(Job.State.FINISHED);
            jobRepository.save(job);
        });
        Batch deliveryBatch = batchFactory.createBatch(driver, shipments, slots.get(0), Batch.Type.DELIVERY);

        // Cancel Shipment1/Job1
        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/"+ shipment1.getNumber() +"/cancel")
                .header("X-Fulfillment-User-Token", externalSystemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", externalSystemAdmin.getTenant().getToken()));

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + deliveryBatch.getId() + "/pickup")
                .header("X-Fulfillment-User-Token", driver.getToken())
                .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    public void returnExceptionIfAllShipmentAreCancelled() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);
        User externalSystemAdmin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN, driver.getTenant());
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, driver);

        Slot slot = slots.get(0);
        Shipment shipment1 = shipmentFactory.createShipment(slot, driver);
        Shipment shipment2 = shipmentFactory.createShipment(slot, driver);
        List<Shipment> shipments = Lists.newArrayList(shipment1, shipment2);

        Batch shoppingBatch = batchFactory.createBatch(driver, shipments, slots.get(0), Batch.Type.SHOPPING);

        shoppingBatch.getJobs().stream().forEach(job -> {
            job.setState(Job.State.FINISHED);
            jobRepository.save(job);
        });
        Batch deliveryBatch = batchFactory.createBatch(driver, shipments, slots.get(0), Batch.Type.DELIVERY);
        Thread.sleep(750); // Prevent race condition with other tests.

        // Cancel Shipment1/Job1
        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/"+ shipment1.getNumber() +"/cancel")
                .header("X-Fulfillment-User-Token", externalSystemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", externalSystemAdmin.getTenant().getToken()));

        // Cancel Shipment2/Job2
        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/"+ shipment2.getNumber() +"/cancel")
                .header("X-Fulfillment-User-Token", externalSystemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", externalSystemAdmin.getTenant().getToken()));

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + deliveryBatch.getId() + "/pickup")
                .header("X-Fulfillment-User-Token", driver.getToken())
                .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().isNotFound());
    }

    @Test
    public void returnUnauthorizedIfNotOwner() throws Exception {
        User shopper = userFactory.createUserData(Role.Name.SHOPPER);
        User driver = userFactory.createUserData(Role.Name.DRIVER, shopper.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, driver);

        Batch batch = batchFactory.createBatch(driver, driver, slots.get(0), Batch.Type.DELIVERY);

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/"+ batch.getId() +"/pickup")
                .header("X-Fulfillment-User-Token", shopper.getToken())
                .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().isUnauthorized());
    }

    @Test
    public void returnExceptionIfShoppingJobStillPending() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, driver);

        List<Batch> batches = batchFactory.createBatches(1, driver, driver, slots.get(0));
        Batch deliveryBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).findAny().get();

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/"+ deliveryBatch.getId() +"/pickup")
                .header("X-Fulfillment-User-Token", driver.getToken())
                .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().isUnprocessableEntity());
    }

    @Test
    public void return4xxOnPickedUpFor2ndTimeAfterFinishedDelivering() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, driver);
        List<Batch> batches = batchFactory.createBatches(1, driver, driver, slots.get(0));
        Batch shoppingBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).findAny().get();
        Batch deliveryBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).findAny().get();
        shoppingBatch.getJobs().forEach(job -> {
            job.setState(Job.State.FINISHED);
            jobRepository.save(job);
        });
        deliveryBatch.getJobs().forEach(job -> {
            job.setState(Job.State.STARTED);
            job.setState(Job.State.ACCEPTED);
            job.setState(Job.State.DELIVERING);
            job.setState(Job.State.FOUND_ADDRESS);
            job.setState(Job.State.FINALIZING_DELIVERY);
            job.setState(Job.State.FINISHED);
            jobRepository.save(job);
        });
        // Based on production bug, somehow order can be picked_up again after finished.
        mvc.perform(MockMvcRequestBuilders.post("/api/batches/"+ deliveryBatch.getId() +"/pickup")
                .header("X-Fulfillment-User-Token", driver.getToken())
                .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().is4xxClientError())
                .andExpect(jsonPath("$.errors[0].message", equalTo("Job is already finished.")));
    }

    @Test
    public void return4xxOnRangerBatchType() throws Exception {
        User externalSystemAdmin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        User ranger = userFactory.createUserData(Role.Name.DRIVER, externalSystemAdmin.getTenant());
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, ranger, StockLocation.Type.SPECIAL);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, ranger);
        Batch rangerBatch = batchFactory.createBatch(ranger, slots.get(0), Batch.Type.RANGER);

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + rangerBatch.getId() + "/pickup")
                .header("X-Fulfillment-User-Token", ranger.getToken())
                .header("X-Fulfillment-Tenant-Token", ranger.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().is4xxClientError())
                .andExpect(jsonPath("$.errors[0].message", equalTo("Invalid batch type.")));
    }
}
