package com.happyfresh.fulfillment.integrationTest.test.lalamove;

import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.messaging.activemq.Sender;
import com.happyfresh.fulfillment.common.property.LalamoveProperty;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.common.BatchHelper;
import com.happyfresh.fulfillment.integrationTest.test.common.MockServerHelper;
import com.happyfresh.fulfillment.integrationTest.test.common.SetSlotHelper;
import com.happyfresh.fulfillment.integrationTest.test.common.ShipmentHelper;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.*;
import com.happyfresh.fulfillment.shipment.service.OrderService;
import org.elasticsearch.common.geo.GeoPoint;
import org.hamcrest.Matchers;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class LalamoveCancelTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private ItemFactory itemFactory;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private StateRepository stateRepository;

    @Autowired
    private SetSlotHelper slotHelper;

    @Autowired
    private ShipmentHelper shipmentHelper;

    @Autowired
    private BatchHelper batchHelper;

    @Autowired
    private ShipmentJsonObjectFactory shipmentJsonObjectFactory;

    @Autowired
    private LalamoveServiceTypeFactory lalamoveServiceTypeFactory;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private LalamoveDeliveryRepository lalamoveDeliveryRepository;

    @Autowired
    private MockServerHelper mockServerHelper;

    @Autowired
    private LalamoveProperty lalamoveProperty;

    @Autowired
    private LalamoveDeliveryFactory lalamoveDeliveryFactory;

    @Autowired
    private ShiftFactory shiftFactory;

    @Autowired
    private LalamoveServiceTypeRepository lalamoveServiceTypeRepository;

    @MockBean
    private OrderService orderService;

    @MockBean
    private Sender sender;

    @Autowired
    private TenantRepository tenantRepository;

    private User systemAdmin;
    private User admin;
    private User userAdmin;
    private User shopper;
    private StockLocation stockLocation;
    private List<Slot> slots;
    private MockRestServiceServer mockServer;
    private JSONObject shipmentObj;
    private String orderNumber;

    @Before
    public void setup() throws Exception {
        orderNumber = "order001";
        systemAdmin = userFactory.createUserData(Role.Name.SYSTEM_ADMIN);
        admin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN, systemAdmin.getTenant());
        userAdmin = userFactory.createUserData(Role.Name.ADMIN, systemAdmin.getTenant());
        shopper = userFactory.createUserData(Role.Name.SHOPPER, systemAdmin.getTenant());
        Thread.sleep(400);


        setupStockLocation();

        shiftFactory.createShopperShifts(stockLocation, LocalDateTime.now(), 2, 8, 1, admin);
        slots = slotFactory.createLongerDeliverySlots(stockLocation, admin, 1, 4, LocalDateTime.now());

        mockServer = mockServerHelper.buildMockServer();

        shipmentObj = shipmentJsonObjectFactory.createShipment(stockLocation, slots.get(2).getId(), 1, orderNumber, new GeoPoint(-6.291202, 106.7855), "Lintasarta");

        setupLalamoveServiceType(stockLocation, true);
    }

    private void setupStockLocation() {
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, admin);
        stockLocation = stockLocations.get(0);
        stockLocation.setEnableGrabExpress(false);
        stockLocation.setEnableGrabExpressCod(false);
        stockLocation.setEnableLalamove(true);
        stockLocation.setMaxShoppingVolume(4000); // Liter
        stockLocation.setPreferences(new HashMap<>() {{
            put("enable_lalamove_delivery_fee", "true");
            put("lalamove_flat_service_fee", "5000");
            put("grab_express_offset_time", "1500");
        }});
        stockLocationFactory.save(stockLocation);
    }

    private void setupLalamoveServiceType(StockLocation stockLocation, boolean isSetupServiceTypeV3) {
        String lalamoveCityCode = "JKT";
        if(isSetupServiceTypeV3)
            lalamoveServiceTypeFactory.createServiceTypeMotorAndMPVV3(admin, stockLocation, lalamoveCityCode);

        State state = stockLocation.getState();
        state.setPreferences(new HashMap<>() {{
            put("lalamove_city_code", lalamoveCityCode);
        }});
        stateRepository.save(state);

        lalamoveServiceTypeFactory.createServiceTypes(admin, stockLocation);
    }

    private void setLalamoveV3Flag(boolean flag) {
        Tenant tenant = stockLocation.getTenant();
        Map<String, String> tenantPreferences = tenant.getPreferences();
        tenantPreferences.put("enable_lalamove_api_v3", String.valueOf(flag));
        tenantRepository.save(tenant);
    }

    private void finalizeShipment(Batch shoppingBatch) throws Exception {
        TimeUnit.MILLISECONDS.sleep(200);
        Shipment shipment = shipmentRepository.findByOrderNumberFetchItems(orderNumber);
        batchHelper.shipmentFinalizeV3(shoppingBatch, shipment, shopper);
    }

    private void switchToHF(Batch shoppingBatch) throws Exception {
        mvc.perform(MockMvcRequestBuilders.put("/api/batches/"+ shoppingBatch.getId() +"/shipments/"+ orderNumber +"/to_hf")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("X-Fulfillment-User-Token", shopper.getToken())
                        .header("X-Fulfillment-Tenant-Token", shopper.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
    }

    public void cancelShipment(String shipmentNumber, User admin) throws Exception {
        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + shipmentNumber + "/cancel")
                        .header("X-Fulfillment-User-Token", admin.getToken())
                        .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.state", Matchers.equalTo("CANCELLED")))
                .andExpect(MockMvcResultMatchers.jsonPath("$.shipment.number", Matchers.equalTo(shipmentNumber)));
    }


    @Test
    public void test_cancelShipment_whenLalamoveV3Disable_shouldCallCancelUsingV2API() throws Exception {
        String uri = lalamoveProperty.getBaseUrl() + "/v2/quotations";
        String responseBody = "{ \"totalFee\": \"35000\", \"totalFeeCurrency\": \"IDR\" }";
        mockServer = mockServerHelper.mock(mockServer, 2, HttpMethod.POST, uri, responseBody);

        uri = lalamoveProperty.getBaseUrl() + "/v2/orders";
        responseBody = "{ \"customerOrderId\": \"124620405314\", \"orderRef\": \"124620405314\" }";
        mockServer = mockServerHelper.mock(mockServer, 1, HttpMethod.POST, uri, responseBody);

        uri = lalamoveProperty.getBaseUrl() + "/v2/orders/124620405314/cancel";
        responseBody = "";
        mockServer = mockServerHelper.mock(mockServer, 1, HttpMethod.PUT, uri, responseBody);

        slotHelper.assertSetRegularSlotV2(true, shipmentObj, admin);
        shipmentHelper.adjustShipment(shipmentObj, admin);

        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);
        finalizeShipment(shoppingBatch);

        TimeUnit.MILLISECONDS.sleep(500);

        transactionHelper.withNewTransaction(() -> {
            LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findAll().get(0);
            Assert.assertEquals(LalamoveDelivery.Status.ORDER_PLACED, lalamoveDelivery.getStatus());
            Assert.assertEquals("124620405314", lalamoveDelivery.getExternalOrderId());
        });

        cancelShipment(orderNumber, admin);

        transactionHelper.withNewTransaction(() -> {
            LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findAll().get(0);
            Assert.assertEquals(LalamoveDelivery.Status.SHIPMENT_CANCELLED, lalamoveDelivery.getStatus());
            Assert.assertEquals("124620405314", lalamoveDelivery.getExternalOrderId());
        });

        mockServer.verify();
    }

    @Test
    public void test_cancelShipment_whenLalamoveV3Enable_shouldCallCancelUsingV3API() throws Exception {
        String responseBody = getResponseFromResourceFile("lalamove_v3_get_quotation_response_success.json");
        String uri = lalamoveProperty.getBaseUrl() + "/v3/quotations";
        mockServer = mockServerHelper.mock(mockServer, 2, HttpMethod.POST, uri, responseBody);

        uri = lalamoveProperty.getBaseUrl() + "/v3/orders";
        responseBody = getResponseFromResourceFile("lalamove_v3_place_order_response_success.json");
        mockServer = mockServerHelper.mock(mockServer, 1, HttpMethod.POST, uri, responseBody);

        uri = lalamoveProperty.getBaseUrl() + "/v3/orders/124620405314";
        responseBody = "";
        mockServer = mockServerHelper.mock(mockServer, 1, HttpMethod.DELETE, uri, responseBody);

        setLalamoveV3Flag(true);

        slotHelper.assertSetRegularSlotV2(true, shipmentObj, admin);
        shipmentHelper.adjustShipment(shipmentObj, admin);

        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);
        finalizeShipment(shoppingBatch);

        TimeUnit.MILLISECONDS.sleep(500);

        transactionHelper.withNewTransaction(() -> {
            LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findAll().get(0);
            Assert.assertEquals(LalamoveDelivery.Status.ORDER_PLACED, lalamoveDelivery.getStatus());
            Assert.assertEquals("124620405314", lalamoveDelivery.getExternalOrderId());
        });

        cancelShipment(orderNumber, admin);

        transactionHelper.withNewTransaction(() -> {
            LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findAll().get(0);
            Assert.assertEquals(LalamoveDelivery.Status.SHIPMENT_CANCELLED, lalamoveDelivery.getStatus());
            Assert.assertEquals("124620405314", lalamoveDelivery.getExternalOrderId());
        });

        mockServer.verify();
    }

    @Test
    public void test_switchToHF_whenLalamoveV3Disable_shouldCallCancelUsingV2API() throws Exception {
        String uri = lalamoveProperty.getBaseUrl() + "/v2/quotations";
        String responseBody = "{ \"totalFee\": \"35000\", \"totalFeeCurrency\": \"IDR\" }";
        mockServer = mockServerHelper.mock(mockServer, 2, HttpMethod.POST, uri, responseBody);

        uri = lalamoveProperty.getBaseUrl() + "/v2/orders";
        responseBody = "{ \"customerOrderId\": \"124620405314\", \"orderRef\": \"124620405314\" }";
        mockServer = mockServerHelper.mock(mockServer, 1, HttpMethod.POST, uri, responseBody);

        uri = lalamoveProperty.getBaseUrl() + "/v2/orders/124620405314/cancel";
        responseBody = "";
        mockServer = mockServerHelper.mock(mockServer, 1, HttpMethod.PUT, uri, responseBody);

        slotHelper.assertSetRegularSlotV2(true, shipmentObj, admin);
        shipmentHelper.adjustShipment(shipmentObj, admin);

        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);
        finalizeShipment(shoppingBatch);

        TimeUnit.MILLISECONDS.sleep(500);

        transactionHelper.withNewTransaction(() -> {
            LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findAll().get(0);
            Assert.assertEquals(LalamoveDelivery.Status.ORDER_PLACED, lalamoveDelivery.getStatus());
            Assert.assertEquals("124620405314", lalamoveDelivery.getExternalOrderId());
        });

        switchToHF(shoppingBatch);

        transactionHelper.withNewTransaction(() -> {
            LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findAll().get(0);
            Assert.assertEquals(LalamoveDelivery.Status.CANCELED, lalamoveDelivery.getStatus());
            Assert.assertEquals("124620405314", lalamoveDelivery.getExternalOrderId());
        });

        mockServer.verify();
    }

    @Test
    public void test_switchToHF_whenLalamoveV3Enable_shouldCallCancelUsingV3API() throws Exception {
        String responseBody = getResponseFromResourceFile("lalamove_v3_get_quotation_response_success.json");
        String uri = lalamoveProperty.getBaseUrl() + "/v3/quotations";
        mockServer = mockServerHelper.mock(mockServer, 2, HttpMethod.POST, uri, responseBody);

        uri = lalamoveProperty.getBaseUrl() + "/v3/orders";
        responseBody = getResponseFromResourceFile("lalamove_v3_place_order_response_success.json");
        mockServer = mockServerHelper.mock(mockServer, 1, HttpMethod.POST, uri, responseBody);

        uri = lalamoveProperty.getBaseUrl() + "/v3/orders/124620405314";
        responseBody = "";
        mockServer = mockServerHelper.mock(mockServer, 1, HttpMethod.DELETE, uri, responseBody);

        setLalamoveV3Flag(true);

        slotHelper.assertSetRegularSlotV2(true, shipmentObj, admin);
        shipmentHelper.adjustShipment(shipmentObj, admin);

        Batch shoppingBatch = batchRepository.findByType(Batch.Type.SHOPPING).get(0);
        batchHelper.assertStartBatch(true, shoppingBatch, shopper);
        finalizeShipment(shoppingBatch);

        TimeUnit.MILLISECONDS.sleep(500);

        transactionHelper.withNewTransaction(() -> {
            LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findAll().get(0);
            Assert.assertEquals(LalamoveDelivery.Status.ORDER_PLACED, lalamoveDelivery.getStatus());
            Assert.assertEquals("124620405314", lalamoveDelivery.getExternalOrderId());
        });

        switchToHF(shoppingBatch);

        transactionHelper.withNewTransaction(() -> {
            LalamoveDelivery lalamoveDelivery = lalamoveDeliveryRepository.findAll().get(0);
            Assert.assertEquals(LalamoveDelivery.Status.CANCELED, lalamoveDelivery.getStatus());
            Assert.assertEquals("124620405314", lalamoveDelivery.getExternalOrderId());
        });

        mockServer.verify();
    }

}
