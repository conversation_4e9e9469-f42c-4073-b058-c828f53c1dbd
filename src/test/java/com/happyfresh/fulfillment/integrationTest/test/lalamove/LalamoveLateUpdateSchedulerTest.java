package com.happyfresh.fulfillment.integrationTest.test.lalamove;

import com.happyfresh.fulfillment.batch.exception.SwitchToHFFailedException;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.service.JedisLockService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.lalamove.model.LalamoveQuotedTotalFee;
import com.happyfresh.fulfillment.lalamove.presenter.LalamoveOrderDetailPresenter;
import com.happyfresh.fulfillment.lalamove.scheduler.LalamoveLateUpdateScheduler;
import com.happyfresh.fulfillment.lalamove.service.LalamoveDeliveryUpdateService;
import com.happyfresh.fulfillment.lalamove.service.api.LalamoveApiService;
import com.happyfresh.fulfillment.repository.LalamoveDeliveryRepository;
import org.junit.*;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;

public class LalamoveLateUpdateSchedulerTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private LalamoveDeliveryFactory lalamoveDeliveryFactory;

    @SpyBean
    private LalamoveDeliveryUpdateService updateService;

    @Autowired
    private LalamoveLateUpdateScheduler scheduler;

    @MockBean
    private JedisLockService jedisLockService;

    @Mock
    private Logger mockLogger;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private LalamoveDeliveryRepository lalamoveDeliveryRepository;

    @MockBean
    private LalamoveApiService lalamoveApiService;

    private LalamoveDelivery delivery;
    private LalamoveDelivery delivery2;

    @BeforeClass
    public static void initProperty() {
        System.setProperty("scheduler.enabled","true");
    }

    @AfterClass
    public static void resetProperty() {
        System.setProperty("scheduler.enabled","false");
    }

    @Override
    protected void setupElasticSearchBeforeEachTests() {
        elasticSearchSetupService.setPreFillSimpleRouteEdges(false); // Prevent prefill ES
    }

    @Before
    public void setUp() throws Exception {
        User user = userFactory.createUserData(Role.Name.SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user);
        Slot slot = slotFactory.createSlot(stockLocations.get(0), user);
        injectLogger();

        StockLocation stockLocation = stockLocations.get(0);
        Shipment shipment = shipmentFactory.createShipment(slot, user);
        delivery = lalamoveDeliveryFactory.create(shipment, user, LalamoveDelivery.Status.ORDER_PLACED, LocalDateTime.now().minusMinutes(5), "LLMORDER1234");

        Shipment shipment2 = shipmentFactory.createShipment(slot, user);
        delivery2 = lalamoveDeliveryFactory.create(shipment2, user, LalamoveDelivery.Status.ORDER_PLACED, LocalDateTime.now().minusMinutes(5), "LLMORDER1235");

        LalamoveQuotedTotalFee totalFee = new LalamoveQuotedTotalFee();
        LalamoveOrderDetailPresenter orderDetailPresenter = new LalamoveOrderDetailPresenter();
        totalFee.setAmount("10800");
        totalFee.setCurrency("ID");
        orderDetailPresenter.setPrice(totalFee);
        orderDetailPresenter.setStatus(LalamoveDelivery.Status.ASSIGNING_DRIVER.toString());
        Mockito.doReturn(Optional.of(orderDetailPresenter)).when(lalamoveApiService).getOrderDetail(any(State.class), anyString());
    }

    private void injectLogger() throws Exception {
        Field loggerField = LalamoveLateUpdateScheduler.class.getDeclaredField("logger");
        loggerField.setAccessible(true);

        Field modifiersField = loggerField.getClass().getDeclaredField("modifiers");
        modifiersField.setAccessible(true);
        modifiersField.setInt(loggerField, loggerField.getModifiers() & ~Modifier.FINAL);

        loggerField.set(scheduler, mockLogger);
    }

    @Test
    public void shouldCallUpdateService() throws Exception{
        Mockito.doReturn(true).when(jedisLockService).lock(anyString(), anyInt(), anyInt());
        scheduler.lateUpdateScheduler();
        Mockito.verify(updateService, Mockito.atLeastOnce()).updateById(delivery.getId());
    }

    @Test
    public void shouldLogExceptions() throws Exception {
        Mockito.doReturn(true).when(jedisLockService).lock(anyString(), anyInt(), anyInt());
        Mockito.doThrow(SwitchToHFFailedException.class)
            .when(updateService)
            .updateById(eq(1L));
        scheduler.lateUpdateScheduler();
        Mockito.verify(updateService, Mockito.atLeastOnce()).updateById(delivery.getId());
        Mockito.verify(mockLogger, Mockito.atLeastOnce()).error(
            eq("[LalamoveLateUpdate] Failed for id 1"),
            any(SwitchToHFFailedException.class));
    }

    @Test
    public void shouldContinueSchedulerAfterUpdateException() throws Exception {
        Mockito.doReturn(true).when(jedisLockService).lock(anyString(), anyInt(), anyInt());
        Mockito.doThrow(SwitchToHFFailedException.class)
            .when(updateService)
            .updateById(eq(1L));
        scheduler.lateUpdateScheduler();
        Mockito.verify(updateService, Mockito.atLeastOnce()).updateById(delivery2.getId());
        transactionHelper.withNewTransaction(() -> {
          LalamoveDelivery finalDelivery = lalamoveDeliveryRepository.getOne(delivery2.getId());
            Assert.assertEquals(LalamoveDelivery.Status.ASSIGNING_DRIVER, finalDelivery.getStatus());
        });
    }

    @Test
    public void shouldLogSchedulerLevelException() throws Exception {
        Mockito.doThrow(InterruptedException.class).when(jedisLockService).lock(anyString(), anyInt(), anyInt());
        scheduler.lateUpdateScheduler();
        Mockito.verify(mockLogger, Mockito.atLeastOnce()).error(
            eq("[LalamoveLateUpdate] Scheduler Failed"),
            any(Exception.class));
    }
}
