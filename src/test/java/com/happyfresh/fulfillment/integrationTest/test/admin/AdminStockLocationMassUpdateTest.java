package com.happyfresh.fulfillment.integrationTest.test.admin;

import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.common.MockServerHelper;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.StockLocationRepository;
import com.happyfresh.fulfillment.stockLocation.service.StockLocationMassUpdateService;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.csv.QuoteMode;
import org.hamcrest.Matchers;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMultipartHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.io.*;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class AdminStockLocationMassUpdateTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private StateFactory stateFactory;

    @Autowired
    private CountryFactory countryFactory;

    @Autowired
    private SupplierFactory supplierFactory;

    @Autowired
    private ClusterFactory clusterFactory;

    @Autowired
    private MockServerHelper mockServerHelper;

    @Autowired
    private StockLocationMassUpdateService stockLocationMassUpdateService;

    @Autowired
    private StockLocationRepository stockLocationRepository;

    @Autowired
    private TransactionHelper transactionHelper;

    private User admin;

    private State state;

    private Cluster cluster;

    private Supplier supplier;

    private List<StockLocation> stockLocations = new ArrayList<>();

    private final String URI = "/api/admin/stock_locations/mass_update";

    @Before
    public void init() {
        admin = userFactory.createUserData(Role.Name.ADMIN);
        stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, admin);
    }

    @Override
    protected void setupElasticSearchBeforeEachTests() {
        elasticSearchSetupService.setPreFillSimpleRouteEdges(false); // Prevent prefill ES
    }

    @Test
    public void index_shouldReturn2xx() throws Exception {
        // modify StockLocation
        StockLocation sl1 = stockLocations.get(0);
        sl1.setOpenAt(LocalTime.of(13, 0));
        sl1.setCloseAt(LocalTime.of(20, 0));
        sl1.setActive(false);
        StockLocation sl2 = stockLocations.get(1);
        sl2.setShopperAveragePickingTimePerUniqItem(11);
        sl2.setShopperQueueReplacementTime(22);
        sl2.setMaxDeliveryHandover(33);
        sl2.setShoppingBatchNotifiedOffset(111);
        sl2.setDeliveryBatchNotifiedOffset(222);
        String csvString = constructInputCsvFile(stockLocations);

        MvcResult mvcResult = assertResponse(csvString, 200);
        MockHttpServletResponse response = mvcResult.getResponse();
        Assert.assertEquals("text/csv", response.getContentType());
        List<String> responseRows = Arrays.stream(response.getContentAsString().split("\n"))
                .map(String::trim)
                .collect(Collectors.toList());

        String expectedResultCsvHeaders = String.join(",", stockLocationMassUpdateService.getResultCsvHeaders());
        Assert.assertEquals(expectedResultCsvHeaders, responseRows.get(0));
        responseRows.remove(0);
        boolean allValid = responseRows.stream()
                .map(row -> Arrays.asList(row.split(","))) // columns in 1 row
                .map(columns -> columns.get(columns.size() - 1)) // last column in 1 row
                .allMatch(result -> result.equals("OK"));
        Assert.assertTrue(allValid);

        transactionHelper.withNewTransaction(() -> {
            List<StockLocation> sls = stockLocationRepository.findAllById(Arrays.asList(sl1.getId(), sl2.getId()));
            StockLocation sl1_ = sls.get(0);
            Assert.assertEquals(LocalTime.of(6, 0), sl1_.getOpenAt());
            Assert.assertEquals(LocalTime.of(13, 0), sl1_.getCloseAt());
            Assert.assertFalse(sl1_.isActive());
            StockLocation sl2_ = sls.get(1);

            Assert.assertEquals(11, sl2_.getShopperAveragePickingTimePerUniqItem(), 0);
            Assert.assertEquals(22, sl2_.getShopperQueueReplacementTime(), 0);
            Assert.assertEquals(33, sl2_.getMaxDeliveryHandover(), 0);
            Assert.assertEquals(111, sl2_.getShoppingBatchNotifiedOffset());
            Assert.assertEquals(222, sl2_.getDeliveryBatchNotifiedOffset());
        });
    }

    @Test
    public void someFieldsSubmitted_shouldReturn200() throws Exception {
        String csvString = "store_id,store_name,open_at,close_at\n" +
                "118,Lotte Mart Fatmawati,09:00,17:00";
        MvcResult mvcResult = assertResponse(csvString, 200);

        String resultCsvString = mvcResult.getResponse().getContentAsString();
        String row = Arrays.stream(resultCsvString.split("\n"))
                .map(String::trim)
                .collect(Collectors.toList()).get(1);
        List<String> values = Arrays.stream(row.split(",")).collect(Collectors.toList());
        String validationResult = values.get(values.size() - 1);
        Assert.assertEquals("OK", validationResult);
    }

    @Test
    public void dateTimeFields_shouldReceiveLocalTimezone_andPersistedToDbAsUTC() throws Exception {
        StockLocation sl1 = stockLocations.get(0);
        sl1.getState().setTimeZone("Asia/Kuala_Lumpur");
        stateFactory.save(sl1.getState());
        stockLocationFactory.save(sl1);
        LocalTime sl1OpenAt = sl1.getOpenAt(); // remember
        String csvString = "store_id,store_name,close_at\n" +
                "118,Lotte Mart Fatmawati,19:00";
        assertResponse(csvString, 200);
        transactionHelper.withNewTransaction(() -> {
            StockLocation sl = stockLocationRepository.findByExternalId(118L);
            Assert.assertEquals(sl1OpenAt, sl.getOpenAt()); // should not affected
            Assert.assertEquals(LocalTime.of(11, 0), sl.getCloseAt());
        });

        sl1 = stockLocations.get(0);
        sl1.getState().setTimeZone("Asia/Jakarta");
        stateFactory.save(sl1.getState());
        stockLocationFactory.save(sl1);
        csvString = "store_id,store_name,open_at,close_at\n" +
                "118,Lotte Mart Fatmawati,09:00,19:00";
        assertResponse(csvString, 200);
        transactionHelper.withNewTransaction(() -> {
            StockLocation sl = stockLocationRepository.findByExternalId(118L);
            Assert.assertEquals(LocalTime.of(2, 0), sl.getOpenAt());
            Assert.assertEquals(LocalTime.of(12, 0), sl.getCloseAt());
        });
    }

    @Test
    public void someFieldsSubmitted_someFieldsInvalid_shouldReturn207() throws Exception {
        StockLocation sl1 = stockLocations.get(1); // remember initial value
        LocalTime sl1OpenAt = sl1.getOpenAt();
        LocalTime sl1CloseAt = sl1.getCloseAt();

        String csvString = "store_id,store_name,open_at,close_at,active\n" +
                "118,Lotte Mart Fatmawati,09:00,17:00,TRUE\n" +
                "1,Lotte Mart Gandaria City,29:00,17:00,false\n"; // invalid hour 29
        MvcResult mvcResult = assertResponse(csvString, 207);

        String resultCsvString = mvcResult.getResponse().getContentAsString();
        List<String> rows = Arrays.stream(resultCsvString.split("\n"))
                .map(String::trim)
                .collect(Collectors.toList());
        List<String> values = Arrays.stream(rows.get(1).split(",")).collect(Collectors.toList());
        String validationResult = values.get(values.size() - 1);
        Assert.assertEquals("OK", validationResult);

        transactionHelper.withNewTransaction(() -> {
            StockLocation sl = stockLocationRepository.findByExternalId(118L);
            Assert.assertEquals(LocalTime.of(2, 0), sl.getOpenAt());
            Assert.assertEquals(LocalTime.of(10, 0), sl.getCloseAt());
            Assert.assertTrue(sl.isActive());
            // should not persist invalid row
            sl = stockLocationRepository.findByExternalId(1L);
            Assert.assertEquals(sl1OpenAt, sl.getOpenAt());
            Assert.assertEquals(sl1CloseAt, sl.getCloseAt());
        });

        values = Arrays.stream(rows.get(2).split(",")).collect(Collectors.toList());
        validationResult = values.get(values.size() - 1);
        Assert.assertEquals("Hour should follow 24h HH:mm format", validationResult);
    }

    @Test
    public void duplicateStoreIds_shouldReturn207() throws Exception {
        String csvString = "store_id,store_name,open_at,close_at\n" +
                "118,Lotte Mart Fatmawati,09:00,17:00\n" +
                "118,Lotte Mart Gandaria,09:00,17:00\n"; // duplicate id
        MvcResult mvcResult = assertResponse(csvString, 207);

        String resultCsvString = mvcResult.getResponse().getContentAsString();
        List<String> rows = Arrays.stream(resultCsvString.split("\n"))
                .map(String::trim)
                .collect(Collectors.toList());
        List<String> values = Arrays.stream(rows.get(1).split(",")).collect(Collectors.toList());
        String validationResult = values.get(values.size() - 1);
        Assert.assertEquals("store_id 118 has more than 1 row in CSV file", validationResult);
        values = Arrays.stream(rows.get(2).split(",")).collect(Collectors.toList());
        validationResult = values.get(values.size() - 1);
        Assert.assertEquals("store_id 118 has more than 1 row in CSV file", validationResult);

        transactionHelper.withNewTransaction(() -> {
            StockLocation sl = stockLocationRepository.findByExternalId(118L);
            Assert.assertNotEquals(LocalTime.of(9, 0), sl.getOpenAt());
            Assert.assertNotEquals(LocalTime.of(17, 0), sl.getCloseAt());
        });
    }

    @Test
    public void unknownStoreIds_shouldReturn207() throws Exception {
        String csvString = "store_id,store_name,open_at,close_at\n" +
                "999,Lotte Mart Fatmawati,09:00,17:00\n";
        MvcResult mvcResult = assertResponse(csvString, 207);

        String resultCsvString = mvcResult.getResponse().getContentAsString();
        List<String> rows = Arrays.stream(resultCsvString.split("\n"))
                .map(String::trim)
                .collect(Collectors.toList());
        List<String> values = Arrays.stream(rows.get(1).split(",")).collect(Collectors.toList());
        String validationResult = values.get(values.size() - 1);
        Assert.assertEquals("Unknown store_id 999", validationResult);
    }

    @Test
    public void invalidCsvFormat_shouldReturn400() throws Exception {
        // no header
        String csvString = "Lotte Mart Fatmawati,00:00,17:00,true,2,5,5,10,60,60";
        assertResponse(csvString, 400);

        // no rows
        csvString = "store_id,store_name,open_at,close_at,active,shopper_average_picking_time_per_uniq_item,shopper_queue_replacement_time,shopper_handover_to_driver_time,max_delivery_handover,shopping_batch_notified_offset,delivery_batch_notified_offset";
        assertResponse(csvString, 400, "Cannot process CSV with empty rows");

        // no store_id
        csvString = "store_name,open_at,close_at,active,shopper_average_picking_time_per_uniq_item,shopper_queue_replacement_time,shopper_handover_to_driver_time,max_delivery_handover,shopping_batch_notified_offset,delivery_batch_notified_offset\n" +
        "Lotte Mart Fatmawati,00:00,17:00,true,2,5,5,10,60,60";
        assertResponse(csvString, 400);

        // no store_name
        csvString = "store_id,open_at,close_at,active,shopper_average_picking_time_per_uniq_item,shopper_queue_replacement_time,shopper_handover_to_driver_time,max_delivery_handover,shopping_batch_notified_offset,delivery_batch_notified_offset\n" +
                "118,00:00,17:00,true,2,5,5,10,60,60";
        assertResponse(csvString, 400);

        // + unknown header
        csvString = "unknown_header,store_id,store_name,open_at,close_at,active,shopper_average_picking_time_per_uniq_item,shopper_queue_replacement_time,shopper_handover_to_driver_time,max_delivery_handover,shopping_batch_notified_offset,delivery_batch_notified_offset\n" +
                "unknown value,118,Lotte Mart Fatmawati,00:00,17:00,true,2,5,5,10,60,60";
        assertResponse(csvString, 400);

        // column count > header count
        csvString = "store_id,store_name,open_at,close_at,active,shopper_average_picking_time_per_uniq_item,shopper_queue_replacement_time,shopper_handover_to_driver_time,max_delivery_handover,shopping_batch_notified_offset,delivery_batch_notified_offset\n" +
                "118,Lotte Mart Fatmawati,00:00,17:00,true,2,5,5,10,60";
        assertResponse(csvString, 400);
    }

    @Test
    public void noFileOrBlankFile_shouldReturn400() throws Exception {
        // blank file
        assertResponse("", 400, "Cannot process empty CSV");
        // no file
        mvc.perform(MockMvcRequestBuilders.multipart(URI)
                        .header("X-Fulfillment-User-Token", admin.getToken())
                        .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().isBadRequest());
    }

    @Test
    public void invalidValue_shouldReturn400() throws Exception {
        String validHeader = String.join(",", stockLocationMassUpdateService.getValidCsvHeaders());

        // invalid hour: 25:00
        String csvString = validHeader + "\n" +
                "118,Lotte Mart Fatmawati,25:00,17:00,true,2,5,5,10,60,60";
        MvcResult mvcResult = assertResponse(csvString, 207);
        String resultCsvString = mvcResult.getResponse().getContentAsString();
        String row = Arrays.stream(resultCsvString.split("\n"))
                .map(String::trim)
                .collect(Collectors.toList()).get(1);
        List<String> values = Arrays.stream(row.split(",")).collect(Collectors.toList());
        String validationResult = values.get(values.size() - 1);
        Assert.assertEquals("Hour should follow 24h HH:mm format", validationResult);

        // negative minutes
        csvString = validHeader + "\n" +
                "118,Lotte Mart Fatmawati,09:00,17:00,true,-111,5,5,10,60,-999";
        mvcResult = assertResponse(csvString, 207);
        resultCsvString = mvcResult.getResponse().getContentAsString();
        row = Arrays.stream(resultCsvString.split("\n"))
                .map(String::trim)
                .collect(Collectors.toList()).get(1);
        values = Arrays.stream(row.split(",")).collect(Collectors.toList());
        validationResult = values.get(values.size() - 1);
        Assert.assertThat(validationResult, Matchers.containsString("Picking time per item must be 0 or a positive real number"));
        Assert.assertThat(validationResult, Matchers.containsString("Driver job visibility offset must be 0 or a positive integer"));

        // invalid boolean
        csvString = validHeader + "\n" +
                "118,Lotte Mart Fatmawati,09:00,17:00,bener,1,5,5,10,60,60";
        mvcResult = assertResponse(csvString, 207);
        resultCsvString = mvcResult.getResponse().getContentAsString();
        row = Arrays.stream(resultCsvString.split("\n"))
                .map(String::trim)
                .collect(Collectors.toList()).get(1);
        values = Arrays.stream(row.split(",")).collect(Collectors.toList());
        validationResult = values.get(values.size() - 1);
        Assert.assertEquals("Active must be 'True' or 'False'", validationResult);
    }

    private MvcResult assertResponse(String csvString, int expectedStatus) throws Exception {
        return assertResponse(csvString, expectedStatus, null);
    }

    private MvcResult assertResponse(String csvString, int expectedStatus, String expectedErrorMsg) throws Exception {
        System.out.println(csvString);
        InputStream fileStream = new ByteArrayInputStream(csvString.getBytes());
        MockMultipartFile file = new MockMultipartFile("file", fileStream);
        fileStream.close();

        MvcResult mvcResult = callEndpoint(file);
        MockHttpServletResponse response = mvcResult.getResponse();

        Assert.assertEquals(expectedStatus, response.getStatus());
        if (expectedErrorMsg != null)
            Assert.assertThat(response.getContentAsString(), Matchers.containsString(expectedErrorMsg));

        return mvcResult;
    }

    private MvcResult callEndpoint(MockMultipartFile file) throws Exception {
        MockMultipartHttpServletRequestBuilder builder = MockMvcRequestBuilders.multipart(URI);
        return mvc.perform(builder.file(file)
                .header("X-Fulfillment-User-Token", admin.getToken())
                .header("X-Fulfillment-Tenant-Token", admin.getTenant().getToken()))
                .andReturn();
    }

    private String constructInputCsvFile(List<StockLocation> stockLocation) throws IOException {
        final CSVFormat format = CSVFormat.DEFAULT.withQuoteMode(QuoteMode.MINIMAL);
        try (
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format)
        ) {
            csvPrinter.printRecord(stockLocationMassUpdateService.getValidCsvHeaders());
            for (StockLocation sl : stockLocations) {
                List<String> csvRowsString = Arrays.asList(
                        sl.getExternalId().toString(),
                        sl.getName(),
                        sl.getOpenAt().toString(),
                        sl.getCloseAt().toString(),
                        String.valueOf(sl.isActive()),
                        String.valueOf(sl.getShopperAveragePickingTimePerUniqItem()),
                        String.valueOf(sl.getShopperQueueReplacementTime()),
                        String.valueOf(sl.getShopperHandoverToDriverTime()),
                        String.valueOf(sl.getMaxDeliveryHandover()),
                        String.valueOf(sl.getShoppingBatchNotifiedOffset()),
                        String.valueOf(sl.getDeliveryBatchNotifiedOffset()));
                csvPrinter.printRecord(csvRowsString);
            }
            csvPrinter.flush();
            return out.toString();
        } catch (IOException e) {
            throw new IOException(e);
        }
    }

}
