package com.happyfresh.fulfillment.integrationTest.test.factory;

import com.happyfresh.fulfillment.entity.Cluster;
import com.happyfresh.fulfillment.entity.Slot;
import com.happyfresh.fulfillment.entity.User;
import com.happyfresh.fulfillment.repository.ClusterRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;

@Service
@Profile("it")
public class ClusterFactory {

    @Autowired
    private ClusterRepository repo;

    @Transactional
    public Cluster save(Cluster cluster) {
        return repo.save(cluster);
    }

    public Cluster create(User user) {
        Cluster cluster = new Cluster();
        cluster.setName("Fatmawati Area");
        cluster.setTenant(user.getTenant());
        cluster.setSlotType(Slot.Type.ONE_HOUR);
        cluster.setCreatedBy(user.getId());
        if (repo != null){
            repo.save(cluster);
        }

        return cluster;
    }

    @Transactional
    public List<Cluster> createN(int n, User creator) {
        List<Cluster> clusters = new ArrayList<>();
        for (int i = 0; i < n; i++) {
            Cluster cluster = new Cluster();
            cluster.setName("Cluster_name_" + i);
            cluster.setTenant(creator.getTenant());
            cluster.setSlotType(Slot.Type.ONE_HOUR);
            cluster.setCreatedBy(creator.getId());
            if (repo != null){
                repo.save(cluster);
            } else {
                cluster.setId((long) i + 1);
            }

            clusters.add(cluster);
        }

        return clusters;
    }
}
