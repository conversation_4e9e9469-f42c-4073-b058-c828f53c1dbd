package com.happyfresh.fulfillment.integrationTest.test.grabExpress;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.grabExpress.scheduler.AutoBookingGrabExpressScheduler;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.repository.GrabExpressDeliveryRepository;
import com.happyfresh.fulfillment.repository.UserRepository;
import com.happyfresh.fulfillment.shipment.service.OrderService;
import org.hibernate.Hibernate;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

public class TriggerBookingOnFinalizeTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private ItemFactory itemFactory;

    @Autowired
    private ShipmentJsonObjectFactory shipmentJsonObjectFactory;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private GrabExpressDeliveryRepository grabExpressDeliveryRepository;

    @Autowired
    private GrabExpressDeliveryFactory grabExpressDeliveryFactory;

    @Autowired
    private AutoBookingGrabExpressScheduler autoBookingGrabExpressScheduler;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private TransactionHelper transactionHelper;

    @MockBean
    private OrderService orderService;

    @Test
    public void test() {}

    //@Test //activated only when needed, causes takes time because of delay/sleep time
    public void shouldCreateBookingScheduledAtBasedOnOffsetTime() throws Exception {
        int offsetTime = 15;

        User user = userFactory.createUserData(Role.Name.SHOPPER);
        User systemAdmin = userFactory.createUserData(Role.Name.SYSTEM_ADMIN, user.getTenant());

        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user).get(0);
        stockLocation.setPreferences(ImmutableMap.of("grab_express_offset_time", String.valueOf(offsetTime)));
        stockLocationFactory.save(stockLocation);

        Slot slot = slotFactory.createSlot(stockLocation, user, 0, 2);

        Shipment shipment = shipmentFactory.createShipment(slot, user);
        Item item = itemFactory.createItems(shipment, user, 1).get(0);
        List<Shipment> shipments = Lists.newArrayList(shipment);

        Batch shoppingBatch = batchFactory.createBatch(user, shipments, slot, Batch.Type.SHOPPING);
        Batch deliveryBatch = batchFactory.createBatch(user, shipments, slot, Batch.Type.DELIVERY);
        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.GRAB_EXPRESS);
        batchRepository.save(deliveryBatch);

        String url = String.format("/api/batches/%d/start", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item.getId(), item.getSku(), item.getRequestedQty());
        object1.put("id", item.getId());
        object1.put("found_qty", item.getRequestedQty());

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);

        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        url = String.format("/api/batches/%d/shipments/%s/finalize", shoppingBatch.getId(), shipment.getNumber());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .content(requestBody.toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.response.shipment.order_total").value(15000.0))
                .andExpect(jsonPath("$.response.shipment.shopping_job.state").value("finalizing"));

        List<GrabExpressDelivery> grabExpressDeliveries = grabExpressDeliveryRepository.findAllByShipmentOrderNumber(shipment.getOrderNumber());
        Assert.assertEquals(1, grabExpressDeliveries.size());

        GrabExpressDelivery grabExpressDelivery = grabExpressDeliveries.get(0);
        Assert.assertNotNull(grabExpressDelivery);
        Assert.assertNull(grabExpressDelivery.getStatus());
        Assert.assertEquals(slot.getStartTime().minusMinutes(offsetTime), grabExpressDelivery.getBookingScheduledAt());

        User externalSystemAdmin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN, user.getTenant());
        // Order cancel
        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/"+ shipment.getNumber() +"/cancel")
                .header("X-Fulfillment-User-Token", externalSystemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", externalSystemAdmin.getTenant().getToken()));

        grabExpressDeliveries = grabExpressDeliveryRepository.findAllByShipmentOrderNumber(shipment.getOrderNumber());
        Assert.assertEquals(0, grabExpressDeliveries.size());
    }

    //@Test //activated only when needed, causes takes time because of delay/sleep time
    public void shouldCreateBookingScheduledAtBasedOnDelayTime() throws Exception {
        User user = userFactory.createUserData(Role.Name.SHOPPER);
        User systemAdmin = userFactory.createUserData(Role.Name.SYSTEM_ADMIN, user.getTenant());

        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user).get(0);
        Slot slot = slotFactory.createSlot(stockLocation, user, 0, 1);
        LocalDateTime now = LocalDateTime.now();
        long remainingMinute = ChronoUnit.MINUTES.between(now, slot.getStartTime());

        long delayTime = 2;
        long offsetTime = remainingMinute + 5;
        stockLocation.setPreferences(ImmutableMap.of(
                "grab_express_offset_time", String.valueOf(offsetTime),
                "grab_express_delay_time", String.valueOf(delayTime)
        ));
        stockLocationFactory.save(stockLocation);

        Shipment shipment = shipmentFactory.createShipment(slot, user);
        Item item = itemFactory.createItems(shipment, user, 1).get(0);
        List<Shipment> shipments = Lists.newArrayList(shipment);

        Batch shoppingBatch = batchFactory.createBatch(user, shipments, slot, Batch.Type.SHOPPING);
        Batch deliveryBatch = batchFactory.createBatch(user, shipments, slot, Batch.Type.DELIVERY);
        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.GRAB_EXPRESS);
        batchRepository.save(deliveryBatch);

        String url = String.format("/api/batches/%d/start", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item.getId(), item.getSku(), item.getRequestedQty());
        object1.put("id", item.getId());
        object1.put("found_qty", item.getRequestedQty());

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);

        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        url = String.format("/api/batches/%d/shipments/%s/finalize", shoppingBatch.getId(), shipment.getNumber());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .content(requestBody.toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.response.shipment.order_total").value(15000.0))
                .andExpect(jsonPath("$.response.shipment.shopping_job.state").value("finalizing"));

        List<GrabExpressDelivery> grabExpressDeliveries = grabExpressDeliveryRepository.findAllByShipmentOrderNumber(shipment.getOrderNumber());
        Assert.assertEquals(1, grabExpressDeliveries.size());

        GrabExpressDelivery grabExpressDelivery = grabExpressDeliveries.get(0);
        Assert.assertNotNull(grabExpressDelivery);
        Assert.assertNull(grabExpressDelivery.getStatus());
        Assert.assertTrue(slot.getStartTime().minusMinutes(remainingMinute).isBefore(grabExpressDelivery.getBookingScheduledAt()));
        Assert.assertEquals(now.plusMinutes(stockLocation.getGrabExpressDelayTime()).withSecond(0).withNano(0), grabExpressDelivery.getBookingScheduledAt());
    }

    //@Test //activated only when needed, causes takes time because of delay/sleep time
    public void shouldCreateBookingUsingScheduler() throws Exception {
        User user = userFactory.createUserData(Role.Name.SHOPPER);
        User systemAdmin = userFactory.createUserData(Role.Name.SYSTEM_ADMIN, user.getTenant());

        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user).get(0);
        Slot slot = slotFactory.createSlot(stockLocation, user, 0, 1);
        LocalDateTime now = LocalDateTime.now();
        long remainingMinute = ChronoUnit.MINUTES.between(now, slot.getStartTime());

        long delayTime = 0;
        long offsetTime = remainingMinute + 5;
        stockLocation.setPreferences(ImmutableMap.of(
                "grab_express_offset_time", String.valueOf(offsetTime),
                "grab_express_delay_time", String.valueOf(delayTime)
        ));
        stockLocationFactory.save(stockLocation);

        Shipment shipment = shipmentFactory.createShipment(slot, user);
        Item item = itemFactory.createItems(shipment, user, 1).get(0);
        List<Shipment> shipments = Lists.newArrayList(shipment);

        Batch shoppingBatch = batchFactory.createBatch(user, shipments, slot, Batch.Type.SHOPPING);
        Batch deliveryBatch = batchFactory.createBatch(user, shipments, slot, Batch.Type.DELIVERY);
        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.GRAB_EXPRESS);
        batchRepository.save(deliveryBatch);

        String url = String.format("/api/batches/%d/start", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        grabExpressDeliveryFactory.createEmptyGrabExpressDelivery(shipment,user, LocalDateTime.now().minusMinutes(10));

        List<GrabExpressDelivery> grabExpressDeliveries = grabExpressDeliveryRepository.findAllByShipmentOrderNumber(shipment.getOrderNumber());
        Assert.assertEquals(1, grabExpressDeliveries.size());
        GrabExpressDelivery grabExpressDelivery = grabExpressDeliveries.get(0);
        Assert.assertNotNull(grabExpressDelivery);
        Assert.assertNull(grabExpressDelivery.getStatus());

        autoBookingGrabExpressScheduler.autoBookingUnallocatedStatus();

        Thread.sleep(3000);

        grabExpressDeliveries = grabExpressDeliveryRepository.findAllByShipmentOrderNumber(shipment.getOrderNumber());
        Assert.assertEquals(1, grabExpressDeliveries.size());
        grabExpressDelivery = grabExpressDeliveries.get(0);
        Assert.assertNotNull(grabExpressDelivery);
        Assert.assertEquals(GrabExpressDelivery.Status.ALLOCATING, grabExpressDelivery.getStatus());

        User externalSystemAdmin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN, user.getTenant());
        // Order cancel
        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/"+ shipment.getNumber() +"/cancel")
                .header("X-Fulfillment-User-Token", externalSystemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", externalSystemAdmin.getTenant().getToken()));

        grabExpressDeliveries = grabExpressDeliveryRepository.findAllByShipmentOrderNumber(shipment.getOrderNumber());
        Assert.assertEquals(1, grabExpressDeliveries.size());
        grabExpressDelivery = grabExpressDeliveries.get(0);
        Assert.assertNotNull(grabExpressDelivery);
        Assert.assertEquals(GrabExpressDelivery.Status.CANCELED, grabExpressDelivery.getStatus());
    }

    //@Test //activated only when needed, causes takes time because of delay/sleep time
    public void shouldCreateBookingAsyncToGrab() throws Exception {
        User user = userFactory.createUserData(Role.Name.SHOPPER);
        User systemAdmin = userFactory.createUserData(Role.Name.SYSTEM_ADMIN, user.getTenant());

        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user).get(0);
        Slot slot = slotFactory.createSlot(stockLocation, user, 0, 1);
        LocalDateTime now = LocalDateTime.now().withSecond(0).withNano(0);
        long remainingMinute = ChronoUnit.MINUTES.between(now, slot.getStartTime());

        long delayTime = 1;
        long offsetTime = remainingMinute + 5;
        stockLocation.setPreferences(ImmutableMap.of(
                "grab_express_offset_time", String.valueOf(offsetTime),
                "grab_express_delay_time", String.valueOf(delayTime)
        ));
        stockLocationFactory.save(stockLocation);

        Shipment shipment = shipmentFactory.createShipment(slot, user);
        Item item = itemFactory.createItems(shipment, user, 1).get(0);
        List<Shipment> shipments = Lists.newArrayList(shipment);

        Batch shoppingBatch = batchFactory.createBatch(user, shipments, slot, Batch.Type.SHOPPING);
        Batch deliveryBatch = batchFactory.createBatch(user, shipments, slot, Batch.Type.DELIVERY);
        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.GRAB_EXPRESS);
        batchRepository.save(deliveryBatch);

        String url = String.format("/api/batches/%d/start", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item.getId(), item.getSku(), item.getRequestedQty());
        object1.put("id", item.getId());
        object1.put("found_qty", item.getRequestedQty());

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);

        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        url = String.format("/api/batches/%d/shipments/%s/finalize", shoppingBatch.getId(), shipment.getNumber());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .content(requestBody.toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.response.shipment.order_total").value(15000.0))
                .andExpect(jsonPath("$.response.shipment.shopping_job.state").value("finalizing"));


        List<GrabExpressDelivery> grabExpressDeliveries = grabExpressDeliveryRepository.findAllByShipmentOrderNumber(shipment.getOrderNumber());
        Assert.assertEquals(1, grabExpressDeliveries.size());
        GrabExpressDelivery grabExpressDelivery = grabExpressDeliveries.get(0);
        Assert.assertNotNull(grabExpressDelivery);
        Assert.assertNull(grabExpressDelivery.getStatus());

        Thread.sleep(65000);

        grabExpressDeliveries = grabExpressDeliveryRepository.findAllByShipmentOrderNumber(shipment.getOrderNumber());
        Assert.assertEquals(1, grabExpressDeliveries.size());
        grabExpressDelivery = grabExpressDeliveries.get(0);
        Assert.assertNotNull(grabExpressDelivery);
        Assert.assertEquals(GrabExpressDelivery.Status.ALLOCATING, grabExpressDelivery.getStatus());
    }

    //@Test //activated only when needed, causes takes time because of delay/sleep time
    public void shouldCreateBookingAsyncToGrabWithShoppingPooling() throws Exception {
        User user = userFactory.createUserData(Role.Name.SHOPPER);
        User systemAdmin = userFactory.createUserData(Role.Name.SYSTEM_ADMIN, user.getTenant());

        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user).get(0);
        Slot slot = slotFactory.createSlot(stockLocation, user, 0, 1);
        LocalDateTime now = LocalDateTime.now().withSecond(0).withNano(0);
        long remainingMinute = ChronoUnit.MINUTES.between(now, slot.getStartTime());

        long delayTime = 1;
        long offsetTime = remainingMinute + 5;
        stockLocation.setPreferences(ImmutableMap.of(
                "grab_express_offset_time", String.valueOf(offsetTime),
                "grab_express_delay_time", String.valueOf(delayTime)
        ));
        stockLocationFactory.save(stockLocation);

        Shipment shipment1 = shipmentFactory.createShipment(slot, user);
        Shipment shipment2 = shipmentFactory.createShipment(slot, user);
        Item item1 = itemFactory.createItems(shipment1, user, 1).get(0);
        Item item2 = itemFactory.createItems(shipment2, user, 1).get(0);
        List<Shipment> shipments = Lists.newArrayList(shipment1, shipment2);

        Batch shoppingBatch = batchFactory.createBatch(user, shipments, slot, Batch.Type.SHOPPING);
        Batch deliveryBatch = batchFactory.createBatch(user, shipments, slot, Batch.Type.DELIVERY);
        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.GRAB_EXPRESS);
        batchRepository.save(deliveryBatch);

        String url = String.format("/api/batches/%d/start", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item1.getId(), item1.getSku(), item1.getRequestedQty());
        object1.put("id", item1.getId());
        object1.put("found_qty", item1.getRequestedQty());

        JSONObject object2 = shipmentJsonObjectFactory.createItemObject(item2.getId(), item2.getSku(), item2.getRequestedQty());
        object2.put("id", item2.getId());
        object2.put("found_qty", item2.getRequestedQty());

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);
        itemsArray.put(object2);

        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        Mockito.when(orderService.getOrderTotal(shipment1.getNumber())).thenReturn(15000.0);

        url = String.format("/api/v2/batches/%d/finalize", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .content(requestBody.toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());


        List<GrabExpressDelivery> grabExpressDeliveries = grabExpressDeliveryRepository.findAllByShipmentOrderNumber(shipment1.getOrderNumber());
        Assert.assertEquals(1, grabExpressDeliveries.size());
        GrabExpressDelivery grabExpressDelivery = grabExpressDeliveries.get(0);
        Assert.assertNotNull(grabExpressDelivery);
        Assert.assertNull(grabExpressDelivery.getStatus());

        List<GrabExpressDelivery> grabExpressDeliveries1 = grabExpressDeliveryRepository.findAllByShipmentOrderNumber(shipment2.getOrderNumber());
        Assert.assertEquals(1, grabExpressDeliveries1.size());
        GrabExpressDelivery grabExpressDelivery1 = grabExpressDeliveries1.get(0);
        Assert.assertNotNull(grabExpressDelivery1);
        Assert.assertNull(grabExpressDelivery1.getStatus());

        Thread.sleep(65000);

        grabExpressDeliveries = grabExpressDeliveryRepository.findAllByShipmentOrderNumber(shipment1.getOrderNumber());
        Assert.assertEquals(1, grabExpressDeliveries.size());
        grabExpressDelivery = grabExpressDeliveries.get(0);
        Assert.assertNotNull(grabExpressDelivery);
        Assert.assertEquals(GrabExpressDelivery.Status.ALLOCATING, grabExpressDelivery.getStatus());

        grabExpressDeliveries1 = grabExpressDeliveryRepository.findAllByShipmentOrderNumber(shipment2.getOrderNumber());
        Assert.assertEquals(1, grabExpressDeliveries1.size());
        grabExpressDelivery1 = grabExpressDeliveries1.get(0);
        Assert.assertNotNull(grabExpressDelivery1);
        Assert.assertEquals(GrabExpressDelivery.Status.ALLOCATING, grabExpressDelivery1.getStatus());
    }

    //@Test //activated only when needed, causes takes time because of delay/sleep time
    public void shouldReCreateBookingAsyncToGrabWhenErrorBooking() throws Exception {
        User user = userFactory.createUserData(Role.Name.SHOPPER, "");
        User systemAdmin = userFactory.createUserData(Role.Name.SYSTEM_ADMIN, user.getTenant());

        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user).get(0);
        Slot slot = slotFactory.createSlot(stockLocation, user, 0, 1);
        LocalDateTime now = LocalDateTime.now().withSecond(0).withNano(0);
        long remainingMinute = ChronoUnit.MINUTES.between(now, slot.getStartTime());

        long delayTime = 0;
        long offsetTime = remainingMinute + 5;
        stockLocation.setPreferences(ImmutableMap.of(
                "grab_express_offset_time", String.valueOf(offsetTime),
                "grab_express_delay_time", String.valueOf(delayTime)
        ));
        stockLocationFactory.save(stockLocation);

        Shipment shipment = shipmentFactory.createShipment(slot, user);
        Item item = itemFactory.createItems(shipment, user, 1).get(0);
        List<Shipment> shipments = Lists.newArrayList(shipment);

        Batch shoppingBatch = batchFactory.createBatch(user, shipments, slot, Batch.Type.SHOPPING);
        Batch deliveryBatch = batchFactory.createBatch(user, shipments, slot, Batch.Type.DELIVERY);
        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.GRAB_EXPRESS);
        batchRepository.save(deliveryBatch);

        String url = String.format("/api/batches/%d/start", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item.getId(), item.getSku(), item.getRequestedQty());
        object1.put("id", item.getId());
        object1.put("found_qty", item.getRequestedQty());

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);

        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        url = String.format("/api/batches/%d/shipments/%s/finalize", shoppingBatch.getId(), shipment.getNumber());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .content(requestBody.toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.response.shipment.order_total").value(15000.0))
                .andExpect(jsonPath("$.response.shipment.shopping_job.state").value("finalizing"));

        Thread.sleep(30000);

        List<GrabExpressDelivery> grabExpressDeliveries = grabExpressDeliveryRepository.findAllByShipmentOrderNumber(shipment.getOrderNumber());
        Assert.assertEquals(1, grabExpressDeliveries.size());
        GrabExpressDelivery grabExpressDelivery = grabExpressDeliveries.get(0);
        Assert.assertNotNull(grabExpressDelivery);
        Assert.assertNull(grabExpressDelivery.getStatus());
        Assert.assertEquals(1, grabExpressDelivery.getRetryCount().longValue());

        Thread.sleep(123000);

        grabExpressDeliveries = grabExpressDeliveryRepository.findAllByShipmentOrderNumber(shipment.getOrderNumber());
        Assert.assertEquals(1, grabExpressDeliveries.size());
        grabExpressDelivery = grabExpressDeliveries.get(0);
        Assert.assertNotNull(grabExpressDelivery);
        Assert.assertNull(grabExpressDelivery.getStatus());
        Assert.assertEquals(2, grabExpressDelivery.getRetryCount().longValue());

        Thread.sleep(60000);

        final long userId = user.getId();
        transactionHelper.withNewTransaction(() -> changePhone(userId, "085624339944"));

        Thread.sleep(123000);

        grabExpressDeliveries = grabExpressDeliveryRepository.findAllByShipmentOrderNumber(shipment.getOrderNumber());
        Assert.assertEquals(1, grabExpressDeliveries.size());
        grabExpressDelivery = grabExpressDeliveries.get(0);
        Assert.assertNotNull(grabExpressDelivery);
        Assert.assertEquals(GrabExpressDelivery.Status.ALLOCATING, grabExpressDelivery.getStatus());
        Assert.assertEquals(2, grabExpressDelivery.getRetryCount().longValue());
    }

    //@Test //activated only when needed, causes takes time because of delay/sleep time
    public void shouldRetryBookingAsyncToGrabUntilMaxAndChangeBatchTypeToHF() throws Exception {
        User user = userFactory.createUserData(Role.Name.SHOPPER, "");
        User systemAdmin = userFactory.createUserData(Role.Name.SYSTEM_ADMIN, user.getTenant());

        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user).get(0);
        Slot slot = slotFactory.createSlot(stockLocation, user, 0, 1);
        LocalDateTime now = LocalDateTime.now().withSecond(0).withNano(0);
        long remainingMinute = ChronoUnit.MINUTES.between(now, slot.getStartTime());

        long delayTime = 0;
        long offsetTime = remainingMinute + 5;
        stockLocation.setPreferences(ImmutableMap.of(
                "grab_express_offset_time", String.valueOf(offsetTime),
                "grab_express_delay_time", String.valueOf(delayTime)
        ));
        stockLocationFactory.save(stockLocation);

        Shipment shipment = shipmentFactory.createShipment(slot, user);
        Item item = itemFactory.createItems(shipment, user, 1).get(0);
        List<Shipment> shipments = Lists.newArrayList(shipment);

        Batch shoppingBatch = batchFactory.createBatch(user, shipments, slot, Batch.Type.SHOPPING);
        Batch deliveryBatch = batchFactory.createBatch(user, shipments, slot, Batch.Type.DELIVERY);
        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.GRAB_EXPRESS);
        batchRepository.save(deliveryBatch);

        String url = String.format("/api/batches/%d/start", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item.getId(), item.getSku(), item.getRequestedQty());
        object1.put("id", item.getId());
        object1.put("found_qty", item.getRequestedQty());

        JSONArray itemsArray = new JSONArray();
        itemsArray.put(object1);

        JSONObject requestBody = new JSONObject();
        requestBody.put("items", itemsArray);

        Mockito.when(orderService.getOrderTotal(shipment.getNumber())).thenReturn(15000.0);

        url = String.format("/api/batches/%d/shipments/%s/finalize", shoppingBatch.getId(), shipment.getNumber());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .content(requestBody.toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(jsonPath("$.response.shipment.order_total").value(15000.0))
                .andExpect(jsonPath("$.response.shipment.shopping_job.state").value("finalizing"));

        Thread.sleep(5000);

        List<GrabExpressDelivery> grabExpressDeliveries = grabExpressDeliveryRepository.findAllByShipmentOrderNumber(shipment.getOrderNumber());
        Assert.assertEquals(1, grabExpressDeliveries.size());
        GrabExpressDelivery grabExpressDelivery = grabExpressDeliveries.get(0);
        Assert.assertNotNull(grabExpressDelivery);
        Assert.assertNull(grabExpressDelivery.getStatus());
        Assert.assertEquals(1, grabExpressDelivery.getRetryCount().longValue());

        Thread.sleep(123000);

        grabExpressDeliveries = grabExpressDeliveryRepository.findAllByShipmentOrderNumber(shipment.getOrderNumber());
        Assert.assertEquals(1, grabExpressDeliveries.size());
        grabExpressDelivery = grabExpressDeliveries.get(0);
        Assert.assertNotNull(grabExpressDelivery);
        Assert.assertNull(grabExpressDelivery.getStatus());
        Assert.assertEquals(2, grabExpressDelivery.getRetryCount().longValue());

        Thread.sleep(183000);

        grabExpressDeliveries = grabExpressDeliveryRepository.findAllByShipmentOrderNumber(shipment.getOrderNumber());
        Assert.assertEquals(0, grabExpressDeliveries.size());

        Optional<Batch> newDeliveryBatch = batchRepository.findById(deliveryBatch.getId());
        Assert.assertNull(newDeliveryBatch.get().getTplType());
        Assert.assertEquals(Batch.DeliveryType.NORMAL, newDeliveryBatch.get().getDeliveryType());
    }

    //@Test //activated only when needed, causes takes time because of delay/sleep time
    public void shouldCreateBookingAsyncToGrabWithShoppingPoolingOneOrderWithReplacement() throws Exception {
        User user = userFactory.createUserData(Role.Name.SHOPPER);
        User systemAdmin = userFactory.createUserData(Role.Name.SYSTEM_ADMIN, user.getTenant());

        StockLocation stockLocation = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, user).get(0);
        Slot slot = slotFactory.createSlot(stockLocation, user, 0, 1);
        LocalDateTime now = LocalDateTime.now().withSecond(0).withNano(0);
        long remainingMinute = ChronoUnit.MINUTES.between(now, slot.getStartTime());

        long delayTime = 1;
        long offsetTime = remainingMinute + 5;
        stockLocation.setPreferences(ImmutableMap.of(
                "grab_express_offset_time", String.valueOf(offsetTime),
                "grab_express_delay_time", String.valueOf(delayTime)
        ));
        stockLocationFactory.save(stockLocation);

        Shipment shipment1 = shipmentFactory.createShipment(slot, user);
        Shipment shipment2 = shipmentFactory.createShipment(slot, user);
        Item item1 = itemFactory.createItems(shipment1, user, 1).get(0);
        Item item2 = itemFactory.createItems(shipment2, user, 1).get(0);
        List<Shipment> shipments = Lists.newArrayList(shipment1, shipment2);

        Batch shoppingBatch = batchFactory.createBatch(user, shipments, slot, Batch.Type.SHOPPING);
        Batch deliveryBatch = batchFactory.createBatch(user, shipments, slot, Batch.Type.DELIVERY);
        deliveryBatch.setDeliveryType(Batch.DeliveryType.TPL);
        deliveryBatch.setTplType(Batch.TplType.GRAB_EXPRESS);
        batchRepository.save(deliveryBatch);

        String url = String.format("/api/batches/%d/start", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        JSONObject object1 = shipmentJsonObjectFactory.createItemObject(item1.getId(), item1.getSku(), item1.getRequestedQty());
        object1.put("id", item1.getId());
        object1.put("found_qty", item1.getRequestedQty());

        JSONObject object2 = shipmentJsonObjectFactory.createItemObject(item2.getId(), item2.getSku(), item2.getRequestedQty());
        object2.put("id", item2.getId());
        object2.put("found_qty", item2.getRequestedQty());

        JSONArray itemsArray1 = new JSONArray();
        itemsArray1.put(object1);

        JSONObject requestBody1 = new JSONObject();
        requestBody1.put("items", itemsArray1);

        Mockito.when(orderService.getOrderTotal(shipment1.getNumber())).thenReturn(15000.0);

        url = String.format("/api/batches/%d/shipments/%s/finalize", shoppingBatch.getId(), shipment1.getNumber());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .content(requestBody1.toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        JSONArray itemsArray2 = new JSONArray();
        itemsArray2.put(object1);
        itemsArray2.put(object2);

        JSONObject requestBody2 = new JSONObject();
        requestBody2.put("items", itemsArray2);

        url = String.format("/api/v2/batches/%d/finalize", shoppingBatch.getId());
        mvc.perform(MockMvcRequestBuilders.post(url)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .content(requestBody2.toString())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        List<GrabExpressDelivery> grabExpressDeliveries = grabExpressDeliveryRepository.findAllByShipmentOrderNumber(shipment1.getOrderNumber());
        Assert.assertEquals(1, grabExpressDeliveries.size());
        GrabExpressDelivery grabExpressDelivery = grabExpressDeliveries.get(0);
        Assert.assertNotNull(grabExpressDelivery);
        Assert.assertNull(grabExpressDelivery.getStatus());

        List<GrabExpressDelivery> grabExpressDeliveries1 = grabExpressDeliveryRepository.findAllByShipmentOrderNumber(shipment2.getOrderNumber());
        Assert.assertEquals(1, grabExpressDeliveries1.size());
        GrabExpressDelivery grabExpressDelivery1 = grabExpressDeliveries1.get(0);
        Assert.assertNotNull(grabExpressDelivery1);
        Assert.assertNull(grabExpressDelivery1.getStatus());
    }

    private User changePhone(Long userId, String phone) {
        User user = userRepository.getOne(userId);
        user.setPhone(phone);
        user = userRepository.save(user);
        Hibernate.initialize(user.getTenant());
        return user;
    }

    private Batch getDeliveryBatch(Long batchId) {
        return batchRepository.getOne(batchId);
    }
}
