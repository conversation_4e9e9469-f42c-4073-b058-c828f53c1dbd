package com.happyfresh.fulfillment.integrationTest.test.enabler;

import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.property.JubelioProperty;
import com.happyfresh.fulfillment.common.service.JubelioApiService;
import com.happyfresh.fulfillment.common.service.SymmetricEncryptionService;
import com.happyfresh.fulfillment.enabler.form.JubelioCreateSalesOrderForm;
import com.happyfresh.fulfillment.enabler.form.JubelioCreateSalesOrderItemForm;
import com.happyfresh.fulfillment.enabler.form.JubelioItemForm;
import com.happyfresh.fulfillment.enabler.form.JubelioUpdateSalesOrderForm;
import com.happyfresh.fulfillment.enabler.presenter.JubelioInventoriesPresenter;
import com.happyfresh.fulfillment.enabler.presenter.JubelioSalesOrderPresenter;
import com.happyfresh.fulfillment.entity.Role;
import com.happyfresh.fulfillment.entity.StockLocation;
import com.happyfresh.fulfillment.entity.User;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.enabler.helper.JubelioMockServerHelper;
import com.happyfresh.fulfillment.integrationTest.test.factory.StockLocationFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.UserFactory;
import com.happyfresh.fulfillment.stockLocation.service.StockLocationService;
import org.hamcrest.Matchers;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;

import java.net.URI;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static java.nio.file.Files.readAllBytes;
import static java.nio.file.Paths.get;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.*;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withStatus;

public class JubelioApiServiceTest extends BaseTest {

    @Autowired
    private JubelioApiService service;

    @Autowired
    private StockLocationFactory slFactory;

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private JubelioMockServerHelper mockServerHelper;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private StockLocationService stockLocationService;

    @Autowired
    private JubelioProperty jubelioProperty;

    @Autowired
    private SymmetricEncryptionService encryptionService;

    private StockLocation stockLocation;

    private User creator;

    private String plainToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************.Whj7i1jJPIi7YkbNxjs63GVXEouE5h0bQSSvagXyf8Y";

    @Before
    public void init() throws Exception {
        if (this.creator == null) {
            this.creator = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        }
        setupBrandStore();
    }

    private void setupBrandStore() throws Exception {
        this.stockLocation = slFactory.createBrandStoreStockLocations(1, creator, StockLocation.Type.ORIGINAL).get(0);
        Map<String, String> prefs = new HashMap<>();
        prefs.put("jubelio_email", "<EMAIL>");
        prefs.put("jubelio_password",  encryptionService.encryptString("Password01!"));
        prefs.put("jubelio_last_token_used", encryptionService.encryptString(this.plainToken));
        this.stockLocation.setPreferences(prefs);
        slFactory.save(this.stockLocation);
    }

    @Ignore("Not used anymore")
    @Test
    public void shouldCallLoginEndpointOnce_whenJubelioTokenIsNull() throws Exception {
        Map<String, String> prefs = this.stockLocation.getPreferences();
        prefs.remove("jubelio_last_token_used");
        this.stockLocation.setPreferences(prefs);
        slFactory.save(this.stockLocation);

        MockRestServiceServer mockServer = mockServerHelper.mockLoginApiCall();
        byte[] response = readAllBytes(get("src", "test", "resources", "fixtures", "jubelio_inventory_response.json"));
        mockServer.expect(ExpectedCount.once(), requestTo(new URI("https://api.jubelio.com/inventory/")))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withStatus(HttpStatus.OK).contentType(MediaType.APPLICATION_JSON).body(response));

        JubelioInventoriesPresenter inventories = service.getInventories(this.stockLocation);

        mockServer.verify();
        Assert.assertEquals(3, inventories.getData().size());
        transactionHelper.withNewTransactionReadOnly(() -> {
            Assert.assertNotNull(stockLocationService.getJubelioLastTokenUsed(this.stockLocation));
        });
    }

    @Ignore("Not used anymore")
    @Test
    public void shouldNotCallLoginEndpoint_whenJubelioTokenIsAvailable() throws Exception {
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        byte[] response = readAllBytes(get("src", "test", "resources", "fixtures", "jubelio_inventory_response.json"));
        mockServer.expect(ExpectedCount.once(), requestTo(new URI("https://api.jubelio.com/inventory/")))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withStatus(HttpStatus.OK).contentType(MediaType.APPLICATION_JSON).body(response));

        JubelioInventoriesPresenter inventories = service.getInventories(this.stockLocation);

        mockServer.verify();
        Assert.assertEquals(3, inventories.getData().size());
    }

    @Ignore("Not used anymore")
    @Test
    public void getInventory() throws Exception {
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        byte[] response = readAllBytes(get("src", "test", "resources", "fixtures", "jubelio_inventory_response.json"));
        mockServer.expect(ExpectedCount.once(), requestTo(new URI("https://api.jubelio.com/inventory/")))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withStatus(HttpStatus.OK).contentType(MediaType.APPLICATION_JSON).body(response));

        JubelioInventoriesPresenter inventories = service.getInventories(this.stockLocation);

        mockServer.verify();
        Assert.assertEquals(3, inventories.getData().size());
    }

    @Ignore("Not used anymore")
    @Test
    public void getInventory_withInvalidLoginCredential() throws Exception {
        Map<String, String> prefs = this.stockLocation.getPreferences();
        prefs.remove("jubelio_last_token_used");
        this.stockLocation.setPreferences(prefs);
        slFactory.save(this.stockLocation);

        MockRestServiceServer mockServer = mockServerHelper.mockFailedLoginApiCall();
        JubelioInventoriesPresenter inventories = service.getInventories(this.stockLocation);

        mockServer.verify();
        Assert.assertNull(inventories);
    }

    @Ignore("Not used anymore")
    @Test
    public void getInventory_withTokenExpired_shouldGetNewToken() throws Exception {
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        // 401 token unauthorized
        byte[] response = readAllBytes(get("src", "test", "resources", "fixtures", "jubelio_unauthorized_token_response.json"));
        mockServer.expect(ExpectedCount.once(), requestTo(new URI("https://api.jubelio.com/inventory/")))
                .andExpect(method(HttpMethod.GET))
                .andExpect(header("authorization", "invalidToken123"))
                .andRespond(withStatus(HttpStatus.UNAUTHORIZED)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(response)
                );
        // login to get new token
        response = readAllBytes(get("src", "test", "resources", "fixtures", "jubelio_login_response.json"));
        mockServer.expect(ExpectedCount.once(), requestTo(new URI("https://api.jubelio.com/login")))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withStatus(HttpStatus.OK)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(response)
                );
        // Second attempt to get response with new token
        response = readAllBytes(get("src", "test", "resources", "fixtures", "jubelio_inventory_response.json"));
        mockServer.expect(ExpectedCount.once(), requestTo(new URI("https://api.jubelio.com/inventory/")))
                .andExpect(method(HttpMethod.GET))
                .andExpect(header("authorization", this.plainToken))
                .andRespond(withStatus(HttpStatus.OK)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(response)
                );

        stockLocationService.setAndSaveJubelioLastTokenUsed(this.stockLocation, "invalidToken123");

        JubelioInventoriesPresenter inventories = service.getInventories(this.stockLocation);

        mockServer.verify();
        Assert.assertEquals(3, inventories.getData().size());
    }

    @Ignore("Not used anymore")
    @Test
    public void getSalesOrder() throws Exception {
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        byte[] response = readAllBytes(get("src", "test", "resources", "fixtures", "jubelio_sales_order_detail_response.json"));
        mockServer.expect(ExpectedCount.once(), requestTo(new URI("https://api.jubelio.com/sales/orders/32")))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withStatus(HttpStatus.OK).contentType(MediaType.APPLICATION_JSON).body(response));

        JubelioSalesOrderPresenter so = service.getSalesOrder(32L, this.stockLocation).get();

        mockServer.verify();
        Assert.assertEquals(2, so.getItems().size());
        Assert.assertEquals(LocalDateTime.of(2019, 11, 15, 19, 0), so.getTransactionDate());
    }

    @Ignore("Not used anymore")
    @Test
    public void getSalesOrder_notFound() throws Exception {
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        byte[] response = readAllBytes(get("src", "test", "resources", "fixtures", "jubelio_sales_order_detail_404_response.json"));
        mockServer.expect(ExpectedCount.once(), requestTo(new URI("https://api.jubelio.com/sales/orders/32")))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withStatus(HttpStatus.NOT_FOUND).contentType(MediaType.APPLICATION_JSON).body(response));

        Optional<JubelioSalesOrderPresenter> so = service.getSalesOrder(32L, this.stockLocation);

        mockServer.verify();
        Assert.assertFalse(so.isPresent());
    }

    @Ignore("Not used anymore")
    @Test
    public void getSalesOrder_withTokenExpired_shouldGetNewToken() throws Exception {
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        // 401 token unauthorized
        byte[] response = readAllBytes(get("src", "test", "resources", "fixtures", "jubelio_unauthorized_token_response.json"));
        mockServer.expect(ExpectedCount.once(), requestTo(new URI("https://api.jubelio.com/sales/orders/32")))
                .andExpect(method(HttpMethod.GET))
                .andExpect(header("authorization", "invalidToken123"))
                .andRespond(withStatus(HttpStatus.UNAUTHORIZED)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(response)
                );
        // login to get new token
        response = readAllBytes(get("src", "test", "resources", "fixtures", "jubelio_login_response.json"));
        mockServer.expect(ExpectedCount.once(), requestTo(new URI("https://api.jubelio.com/login")))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withStatus(HttpStatus.OK)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(response)
                );
        // Second attempt to get response with new token
        response = readAllBytes(get("src", "test", "resources", "fixtures", "jubelio_sales_order_detail_response.json"));
        mockServer.expect(ExpectedCount.once(), requestTo(new URI("https://api.jubelio.com/sales/orders/32")))
                .andExpect(method(HttpMethod.GET))
                .andExpect(header("authorization", this.plainToken))
                .andRespond(withStatus(HttpStatus.OK).contentType(MediaType.APPLICATION_JSON).body(response));

        stockLocationService.setAndSaveJubelioLastTokenUsed(this.stockLocation, "invalidToken123");

        JubelioSalesOrderPresenter so = service.getSalesOrder(32L, this.stockLocation).get();

        mockServer.verify();
        Assert.assertEquals(2, so.getItems().size());
        Assert.assertEquals(LocalDateTime.of(2019, 11, 15, 19, 0), so.getTransactionDate());
    }

    @Ignore("Not used anymore")
    @Test
    public void getSalesOrder_withTokenExpired_failedToGetNewOne() throws Exception {
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        // 401 token unauthorized
        byte[] response = readAllBytes(get("src", "test", "resources", "fixtures", "jubelio_unauthorized_token_response.json"));
        mockServer.expect(ExpectedCount.once(), requestTo(new URI("https://api.jubelio.com/sales/orders/32")))
                .andExpect(method(HttpMethod.GET))
                .andExpect(header("authorization", "invalidToken123"))
                .andRespond(withStatus(HttpStatus.UNAUTHORIZED)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(response)
                );
        // login to get new token
        response = readAllBytes(get("src", "test", "resources", "fixtures", "jubelio_login_response.json"));
        mockServer.expect(ExpectedCount.once(), requestTo(new URI("https://api.jubelio.com/login")))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withStatus(HttpStatus.OK)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(response)
                );
        // Failed on second attempt
        mockServer.expect(ExpectedCount.once(), requestTo(new URI("https://api.jubelio.com/sales/orders/32")))
                .andExpect(method(HttpMethod.GET))
                .andExpect(header("authorization", this.plainToken))
                .andRespond(withStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body("{\"code\": \"Error message\"}")
                );

        stockLocationService.setAndSaveJubelioLastTokenUsed(this.stockLocation, "invalidToken123");

        Optional<JubelioSalesOrderPresenter> so = service.getSalesOrder(32L, this.stockLocation);

        mockServer.verify();
        Assert.assertFalse(so.isPresent());
    }

    @Ignore("Not used anymore")
    @Test
    public void createSalesOrder() {
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer.expect(method(HttpMethod.POST))
                .andExpect(requestTo("https://api.jubelio.com/sales/orders/"))
                .andExpect(header("authorization", this.plainToken))
                .andExpect(header("Content-Type", "application/json"))
                .andExpect(jsonPath("$.salesorder_no", Matchers.equalTo("T87298839")))
                .andExpect(jsonPath("$.items", Matchers.hasSize(2)))
                .andRespond(withStatus(HttpStatus.OK)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body("{\"id\": 32}")
                );

        Long salesOrderId = service.createSalesOrder(createSalesOrderForm(), this.stockLocation);

        mockServer.verify();
        Assert.assertEquals(32L, salesOrderId.longValue());
    }

    @Ignore("Not used anymore")
    @Test
    public void createSalesOrder_withTokenExpired_shouldGetNewToken() throws Exception {
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        // 401 token unauthorized
        byte[] response = readAllBytes(get("src", "test", "resources", "fixtures", "jubelio_unauthorized_token_response.json"));
        mockServer.expect(method(HttpMethod.POST))
                .andExpect(requestTo("https://api.jubelio.com/sales/orders/"))
                .andExpect(header("authorization", "invalidToken123"))
                .andExpect(header("Content-Type", "application/json"))
                .andRespond(withStatus(HttpStatus.UNAUTHORIZED)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(response)
                );
        // login to get new token
        response = readAllBytes(get("src", "test", "resources", "fixtures", "jubelio_login_response.json"));
        mockServer.expect(ExpectedCount.once(), requestTo("https://api.jubelio.com/login"))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withStatus(HttpStatus.OK)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(response)
                );
        // Second attempt to get response with new token
        mockServer.expect(method(HttpMethod.POST))
                .andExpect(requestTo("https://api.jubelio.com/sales/orders/"))
                .andExpect(header("authorization", this.plainToken))
                .andExpect(header("Content-Type", "application/json"))
                .andExpect(jsonPath("$.salesorder_no", Matchers.equalTo("T87298839")))
                .andExpect(jsonPath("$.items", Matchers.hasSize(2)))
                .andRespond(withStatus(HttpStatus.OK)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body("{\"id\": 32}")
                );

        stockLocationService.setAndSaveJubelioLastTokenUsed(this.stockLocation, "invalidToken123");

        Long salesOrderId = service.createSalesOrder(createSalesOrderForm(), this.stockLocation);

        mockServer.verify();
        Assert.assertEquals(32L, salesOrderId.longValue());
    }

    @Ignore("Not used anymore")
    @Test
    public void editSalesOrder() {
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer.expect(method(HttpMethod.POST))
                .andExpect(requestTo("https://api.jubelio.com/sales/orders/"))
                .andExpect(header("authorization", this.plainToken))
                .andExpect(header("Content-Type", "application/json"))
                .andExpect(jsonPath("$.salesorder_id", Matchers.equalTo(32)))
                .andExpect(jsonPath("$.salesorder_no", Matchers.equalTo("T87298839")))
                .andExpect(jsonPath("$.items", Matchers.hasSize(2)))
                .andRespond(withStatus(HttpStatus.OK)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body("{\"id\": 32}")
                );

        Boolean success = service.editSalesOrder(32L, createUpdateSalesOrderForm(), this.stockLocation);

        mockServer.verify();
        Assert.assertTrue(success);
    }

    @Ignore("Not used anymore")
    @Test
    public void editSalesOrder_withTokenExpired_shouldGetNewToken() throws Exception {
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        // 401 token unauthorized
        byte[] response = readAllBytes(get("src", "test", "resources", "fixtures", "jubelio_unauthorized_token_response.json"));
        mockServer.expect(method(HttpMethod.POST))
                .andExpect(requestTo("https://api.jubelio.com/sales/orders/"))
                .andExpect(header("authorization", "invalidToken123"))
                .andExpect(header("Content-Type", "application/json"))
                .andRespond(withStatus(HttpStatus.UNAUTHORIZED)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(response)
                );
        // login to get new token
        response = readAllBytes(get("src", "test", "resources", "fixtures", "jubelio_login_response.json"));
        mockServer.expect(ExpectedCount.once(), requestTo("https://api.jubelio.com/login"))
                .andExpect(method(HttpMethod.POST))
                .andRespond(withStatus(HttpStatus.OK)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(response)
                );
        // Second attempt to get response with new token
        mockServer.expect(method(HttpMethod.POST))
                .andExpect(requestTo("https://api.jubelio.com/sales/orders/"))
                .andExpect(header("authorization", this.plainToken))
                .andExpect(header("Content-Type", "application/json"))
                .andExpect(jsonPath("$.salesorder_id", Matchers.equalTo(32)))
                .andExpect(jsonPath("$.salesorder_no", Matchers.equalTo("T87298839")))
                .andExpect(jsonPath("$.items", Matchers.hasSize(2)))
                .andRespond(withStatus(HttpStatus.OK)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body("{\"id\": 32}")
                );

        stockLocationService.setAndSaveJubelioLastTokenUsed(this.stockLocation, "invalidToken123");

        Boolean success = service.editSalesOrder(32L, createUpdateSalesOrderForm(), this.stockLocation);

        mockServer.verify();
        Assert.assertTrue(success);
    }

    @Ignore("Not used anymore")
    @Test
    public void editSalesOrder_withInvalidPayload() {
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer.expect(method(HttpMethod.POST))
                .andExpect(requestTo("https://api.jubelio.com/sales/orders/"))
                .andExpect(header("authorization", this.plainToken))
                .andExpect(header("Content-Type", "application/json"))
                .andExpect(jsonPath("$.salesorder_id", Matchers.equalTo(111)))
                .andRespond(withStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body("{\"code\": \"Error message\"}")
                );

        Boolean success = service.editSalesOrder(111L, createUpdateSalesOrderForm(), this.stockLocation);

        mockServer.verify();
        Assert.assertFalse(success);
    }

    @Ignore("Not used anymore")
    @Test
    public void markSalesOrderAsComplete() {
        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        mockServer.expect(method(HttpMethod.POST))
                .andExpect(requestTo("https://api.jubelio.com/sales/orders/mark-as-complete"))
                .andExpect(header("authorization", this.plainToken))
                .andExpect(header("Content-Type", "application/json"))
                .andExpect(content().string("{\"ids\": [\"447\"]}"))
                .andRespond(withStatus(HttpStatus.OK)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body("{\"status\": \"ok\"}")
                );
        Boolean success = service.markSalesOrderAsComplete(447L, this.stockLocation);
        mockServer.verify();
        Assert.assertTrue(success);
    }

    private JubelioCreateSalesOrderForm createSalesOrderForm() {
        JubelioCreateSalesOrderForm form = new JubelioCreateSalesOrderForm();

        form.setSalesorderNo("T87298839");
        form.setLocationId(-1);
        form.setContactId(-1);
        form.setCustomerName("Jane Doe");
        form.setTransactionDate(LocalDateTime.of(2019, 11, 15, 19, 0));
        form.setIsTaxIncluded(true);
        form.setNote("Please be careful");
        form.setSubTotal(35000D);
        form.setTotalDisc(0D);
        form.setTotalTax(0D);
        form.setGrandTotal(5000D);
        form.setRefNo("00023");
        form.setLocationId(-1);
        form.setSource(jubelioProperty.getSourceId());
        form.setIsCanceled(false);
        form.setCancelReason("");
        form.setCancelReason("");
        form.setChannelStatus("Paid");
        form.setShippingCost(15000D);
        form.setInsuranceCost(0D);
        form.setIsPaid(true);
        form.setShippingFullName("Jon Doe");
        form.setShippingPhone("0897847412374");
        form.setShippingAddress("Talavera Office");
        form.setShippingArea("Cilandak");
        form.setShippingCity("Jakarta Selatan");
        form.setShippingProvince("DKI Jakarta");
        form.setShippingPostCode("12590");
        form.setShippingCountry("Indonesia");
        form.setAddDisc(0D);
        form.setAddFee(0D);

        JubelioCreateSalesOrderItemForm item1 = new JubelioCreateSalesOrderItemForm();
        item1.setItemId(2L);
        item1.setDescription("Coklat Zimbabwe");
        item1.setTaxId(1);
        item1.setPrice(2500D);
        item1.setUnit("Buah");
        item1.setQtyInBase(2);
        item1.setDisc(0D);
        item1.setDiscAmount(0D);
        item1.setTaxAmount(0D);
        item1.setAmount(5000D);
        item1.setLocationId(-1);
        item1.setShipper(null);
        JubelioCreateSalesOrderItemForm item2 = new JubelioCreateSalesOrderItemForm();
        item2.setItemId(3L);
        item2.setDescription("Coklat Swiss");
        item2.setTaxId(1);
        item2.setPrice(30000D);
        item2.setUnit("Buah");
        item2.setQtyInBase(1);
        item2.setDisc(0D);
        item2.setDiscAmount(0D);
        item2.setTaxAmount(0D);
        item2.setAmount(3000D);
        item2.setLocationId(-1);
        item1.setShipper(null);

        form.setItems(Arrays.asList(item1, item2));

        return form;
    }

    private JubelioUpdateSalesOrderForm createUpdateSalesOrderForm() {
        JubelioUpdateSalesOrderForm form = new JubelioUpdateSalesOrderForm();

        form.setSalesorderNo("T87298839");
        form.setLocationId(-1);
        form.setContactId(-1);
        form.setCustomerName("Jane Doe");
        form.setTransactionDate(LocalDateTime.of(2019, 11, 15, 19, 0));
        form.setIsTaxIncluded(true);
        form.setNote("Please be careful");
        form.setSubTotal(35000D);
        form.setTotalDisc(0D);
        form.setTotalTax(0D);
        form.setGrandTotal(5000D);
        form.setRefNo("00023");
        form.setLocationId(-1);
        form.setSource(jubelioProperty.getSourceId());
        form.setIsCanceled(false);
        form.setCancelReason("");
        form.setCancelReason("");
        form.setChannelStatus("Paid");
        form.setShippingCost(15000D);
        form.setInsuranceCost(0D);
        form.setIsPaid(true);
        form.setShippingFullName("Jon Doe");
        form.setShippingPhone("0897847412374");
        form.setShippingAddress("Talavera Office");
        form.setShippingArea("Cilandak");
        form.setShippingCity("Jakarta Selatan");
        form.setShippingProvince("DKI Jakarta");
        form.setShippingPostCode("12590");
        form.setShippingCountry("Indonesia");
        form.setAddDisc(0D);
        form.setAddFee(0D);

        JubelioItemForm item1 = new JubelioItemForm();
        item1.setItemId(2L);
        item1.setDescription("Coklat Zimbabwe");
        item1.setTaxId(1);
        item1.setPrice(2500D);
        item1.setUnit("Buah");
        item1.setQtyInBase(2);
        item1.setDisc(0D);
        item1.setDiscAmount(0D);
        item1.setTaxAmount(0D);
        item1.setAmount(5000D);
        item1.setLocationId(-1);
        item1.setShipper(null);
        JubelioItemForm item2 = new JubelioItemForm();
        item2.setItemId(3L);
        item2.setDescription("Coklat Swiss");
        item2.setTaxId(1);
        item2.setPrice(30000D);
        item2.setUnit("Buah");
        item2.setQtyInBase(1);
        item2.setDisc(0D);
        item2.setDiscAmount(0D);
        item2.setTaxAmount(0D);
        item2.setAmount(3000D);
        item2.setLocationId(-1);
        item1.setShipper(null);

        form.setItems(Arrays.asList(item1, item2));

        return form;
    }
}
