package com.happyfresh.fulfillment.integrationTest.test.shipment;

import com.happyfresh.fulfillment.batch.service.ShopperAutoAssignmentService;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.repository.JobRepository;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import com.happyfresh.fulfillment.slot.presenter.SlotOptimizationEvent;
import com.happyfresh.fulfillment.slot.service.SlotOptimizationService;
import com.happyfresh.fulfillment.user.service.UserService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

public class CancelShipmentLongerDeliveryTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private ShiftFactory shiftFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private JobFactory jobFactory;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private ClusterFactory clusterFactory;

    @Autowired
    private ItemFactory itemFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private SlotOptimizationService slotOptimizationService;

    @Autowired
    private UserService userService;

    @Autowired
    private TransactionHelper transactionHelper;

    @SpyBean
    private ShopperAutoAssignmentService shopperAutoAssignmentService;

    private double shopperAveragePickingTimePerUniqItem = 3;
    private User user;
    private StockLocation stockLocation;
    private StockLocation rangerStockLocation;
    private String canceledOrderNumber;
    private Slot slot1;
    private Slot slot2;
    private Shift shopperShift;
    private Shift driverShift;

    @Before
    public void setUp() throws Exception {
        Thread.sleep(1000);

        user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        // Create role System Admin in order Optimization to work
        userFactory.createUserData(Role.Name.SYSTEM_ADMIN, user.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user);
        stockLocation = stockLocations.get(0);
        stockLocation.setShopperAveragePickingTimePerUniqItem(shopperAveragePickingTimePerUniqItem);
        stockLocationFactory.save(stockLocation);

        rangerStockLocation = stockLocations.get(1);
        rangerStockLocation.setType(StockLocation.Type.SPECIAL);
        stockLocationFactory.save(rangerStockLocation);

        Cluster cluster = stockLocation.getCluster();
        cluster.setSlotType(Slot.Type.LONGER_DELIVERY);
        clusterFactory.save(cluster);

        // Slot tomorrow
        slot1 = slotFactory.createLongerDeliverySlot(stockLocation, user, 1, 1, 4);
        slot2 = slotFactory.createLongerDeliverySlot(stockLocation, user, 1, 5, 4);

        shopperShift = shiftFactory.createShift(stockLocation, user, Shift.Type.SHOPPER, slot1.getStartTime().minusHours(2), slot2.getEndTime().minusHours(2), 2);
        driverShift = shiftFactory.createShift(stockLocation, user, Shift.Type.DRIVER, slot1.getStartTime(), slot2.getEndTime(), 1);

        canceledOrderNumber = "H234567";

        // First Shipment
        Shipment shipment1 = shipmentFactory.createShipment(slot1, user, "H234567", "H234567", Shipment.State.READY);
        Batch shoppingBatch1 = batchFactory.createBatch(user, shipment1, slot1, Batch.Type.SHOPPING);
        shoppingBatch1.setShift(shopperShift);
        shoppingBatch1.setVehicle(1);
        batchFactory.save(shoppingBatch1);

        Batch deliveryBatch1 = batchFactory.createBatch(user, shipment1, slot1, Batch.Type.DELIVERY);
        deliveryBatch1.setShift(driverShift);
        batchFactory.save(deliveryBatch1);

        // Item height = width = depth = 10 cm
        // Total Volume: 3000mL
        itemFactory.createItems(shipment1, user, 3);
    }

    @Test
    public void shouldOptimizeIfShoppingNotStarted() throws Exception {
        optimizeWithSecondShipment();

        List<Batch> batches = batchRepository.findAll();
        Batch shoppingBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).findAny().get();
        Batch deliveryBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).findAny().get();

        LocalDateTime shoppingBatchStartTime = shoppingBatch.getStartTime();
        LocalDateTime shoppingBatchEndTime = shoppingBatch.getEndTime();

        LocalDateTime deliveryBatchStartTime = deliveryBatch.getStartTime();
        LocalDateTime deliveryBatchEndTime = deliveryBatch.getEndTime();

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + canceledOrderNumber + "/cancel")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()));

        Thread.sleep(1000);

        batches = batchRepository.findAll();
        shoppingBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).findAny().get();
        deliveryBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).findAny().get();

        Assert.assertEquals(shoppingBatchStartTime, shoppingBatch.getStartTime());
        Assert.assertTrue(shoppingBatchEndTime.isAfter(shoppingBatch.getEndTime()));

        Assert.assertEquals(deliveryBatchStartTime, deliveryBatch.getStartTime());
        Assert.assertTrue(deliveryBatchEndTime.isAfter(deliveryBatch.getEndTime()));
    }

    @Test
    public void shouldOptimizeDeliveryOnlyIfShoppingHasStarted() throws Exception {
        optimizeWithSecondShipment();

        List<Batch> batches = batchRepository.findAll();
        Batch shoppingBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).findAny().get();
        Batch deliveryBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).findAny().get();

        shoppingBatch.setUser(user);
        batchFactory.save(shoppingBatch);

        LocalDateTime shoppingBatchStartTime = shoppingBatch.getStartTime();
        LocalDateTime shoppingBatchEndTime = shoppingBatch.getEndTime();

        LocalDateTime deliveryBatchStartTime = deliveryBatch.getStartTime();
        LocalDateTime deliveryBatchEndTime = deliveryBatch.getEndTime();

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + canceledOrderNumber + "/cancel")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()));

        Thread.sleep(1000);

        batches = batchRepository.findAll();
        shoppingBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).findAny().get();
        deliveryBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).findAny().get();

        Assert.assertEquals(shoppingBatchStartTime, shoppingBatch.getStartTime());
        Assert.assertEquals(shoppingBatchEndTime, shoppingBatch.getEndTime());

        Assert.assertEquals(deliveryBatchStartTime, deliveryBatch.getStartTime());
        Assert.assertTrue(deliveryBatchEndTime.isAfter(deliveryBatch.getEndTime()));
    }

    @Test
    public void shouldNotOptimizeIfShoppingAndDeliveryHasStarted() throws Exception {
        optimizeWithSecondShipment();

        List<Batch> batches = batchRepository.findAll();
        Batch shoppingBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).findAny().get();
        Batch deliveryBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).findAny().get();

        shoppingBatch.setUser(user);
        batchFactory.save(shoppingBatch);

        deliveryBatch.setUser(user);
        batchFactory.save(deliveryBatch);

        LocalDateTime shoppingBatchStartTime = shoppingBatch.getStartTime();
        LocalDateTime shoppingBatchEndTime = shoppingBatch.getEndTime();

        LocalDateTime deliveryBatchStartTime = deliveryBatch.getStartTime();
        LocalDateTime deliveryBatchEndTime = deliveryBatch.getEndTime();

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + canceledOrderNumber + "/cancel")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()));

        Thread.sleep(1000);

        batches = batchRepository.findAll();
        shoppingBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).findAny().get();
        deliveryBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).findAny().get();

        Assert.assertEquals(shoppingBatchStartTime, shoppingBatch.getStartTime());
        Assert.assertEquals(shoppingBatchEndTime, shoppingBatch.getEndTime());

        Assert.assertEquals(deliveryBatchStartTime, deliveryBatch.getStartTime());
        Assert.assertEquals(deliveryBatchEndTime, deliveryBatch.getEndTime());
    }

    @Test
    public void shouldDeleteJobAndBatchIfItIsNotOptimizedAndShoppingNotStarted() throws Exception {
        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + canceledOrderNumber + "/cancel")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()));

        Thread.sleep(1000);

        List<Batch> batches = batchRepository.findAll();
        List<Job> jobs = jobRepository.findAll();
        Assert.assertEquals(0, jobs.size());
        Assert.assertEquals(0, batches.size());
    }

    // Only delete delivery job and delivery batch
    @Test
    public void shouldNotDeleteJobAndBatchIfItIsNotOptimizedAndShoppingStarted() throws Exception {
        List<Batch> batches = batchRepository.findAll();
        Batch shoppingBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).findAny().get();

        shoppingBatch.setUser(user);
        batchFactory.save(shoppingBatch);

        List<Job> jobs = jobRepository.findAll();
        Job shoppingJob = jobs.stream().filter(job -> job.isShopping()).findAny().get();

        shoppingJob.setState(Job.State.FINALIZING);
        jobFactory.save(shoppingJob);

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + canceledOrderNumber + "/cancel")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()));

        Thread.sleep(1000);

        batches = batchRepository.findAll();
        jobs = jobRepository.findAll();
        Assert.assertEquals(1, jobs.size());
        Assert.assertEquals(1, batches.size());
    }

    //  Do not delete shopping/delivery job and shopping/delivery batch
    @Test
    public void shouldNotDeleteJobAndBatchIfItIsNotOptimizedAndShoppingAndDeliveryStarted() throws Exception {
        List<Batch> batches = batchRepository.findAll();
        Batch shoppingBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.SHOPPING).findAny().get();
        Batch deliveryBatch = batches.stream().filter(batch -> batch.getType() == Batch.Type.DELIVERY).findAny().get();

        List<Job> jobs = jobRepository.findAll();
        Job shoppingJob = jobs.stream().filter(job -> job.isShopping()).findAny().get();
        Job deliveryJob = jobs.stream().filter(job -> job.isDelivery()).findAny().get();

        shoppingBatch.setUser(user);
        batchFactory.save(shoppingBatch);

        shoppingJob.setState(Job.State.FINALIZING);
        jobFactory.save(shoppingJob);

        deliveryBatch.setUser(user);
        batchFactory.save(deliveryBatch);

        deliveryJob.setState(Job.State.ACCEPTED);
        jobFactory.save(deliveryJob);

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + canceledOrderNumber + "/cancel")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()));

        Thread.sleep(1000);

        batches = batchRepository.findAll();
        jobs = jobRepository.findAll();
        Assert.assertEquals(2, jobs.size());
        Assert.assertEquals(2, batches.size());
    }

    private void optimizeWithSecondShipment() throws Exception {
        canceledOrderNumber = "H345678";

        // Second Shipment
        Shipment shipment2 = shipmentFactory.createShipment(slot1, user, canceledOrderNumber, canceledOrderNumber, Shipment.State.READY);
        Batch shoppingBatch2 = batchFactory.createBatch(user, shipment2, slot1, Batch.Type.SHOPPING);
        shoppingBatch2.setShift(shopperShift);
        shoppingBatch2.setVehicle(1);
        batchFactory.save(shoppingBatch2);

        Batch deliveryBatch2 = batchFactory.createBatch(user, shipment2, slot1, Batch.Type.DELIVERY);
        deliveryBatch2.setShift(driverShift);
        batchFactory.save(deliveryBatch2);

        // Item height = width = depth = 10 cm
        // Total Volume: 3000mL
        itemFactory.createItems(shipment2, user, 3);

        // Optimize First
        try {
            AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(user.getToken(), user.getTenant().getToken());
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            slotOptimizationService.shopperSlotOptimization(shipment2.getSlot(), shopperShift);
        } finally {
            SecurityContextHolder.clearContext();
        }
    }

    @Test
    public void cancelShipment_whenStockLocationSpecial_shouldNotTriggerAutoAssignment() throws Exception {
        Slot slot = slotFactory.createLongerDeliverySlot(rangerStockLocation, user, 0, 1, 4);

        Shipment shipment = shipmentFactory.createShipment(slot, user, "Order-801", "Order-801", Shipment.State.READY);
        Batch shoppingBatch = batchFactory.createBatch(user, shipment, slot1, Batch.Type.SHOPPING);
        shoppingBatch.setShift(shopperShift);
        shoppingBatch.setVehicle(1);
        batchFactory.save(shoppingBatch);

        Batch deliveryBatch1 = batchFactory.createBatch(user, shipment, slot1, Batch.Type.DELIVERY);
        deliveryBatch1.setShift(driverShift);
        batchFactory.save(deliveryBatch1);

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + "Order-801" + "/cancel")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()));

        Thread.sleep(1000);

        Optional<Batch> batchResult = batchRepository.findById(shoppingBatch.getId());
        List<Job> jobs = jobRepository.findAllByShipmentIdIn(Arrays.asList(shipment.getId()));
        Assert.assertEquals(0, jobs.size());
        Assert.assertFalse(batchResult.isPresent());
        Mockito.verify(shopperAutoAssignmentService, Mockito.never()).publishAutoAssignmentEvent(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyLong(), Mockito.any(SlotOptimizationEvent.AutoAssignmentTriggerEvent.class));
    }


    @Test
    public void cancelShipment_whenStockLocationOriginal_shouldTriggerAutoAssignment() throws Exception {
        Slot slot = slotFactory.createLongerDeliverySlot(stockLocation, user, 0, 1, 4);
        Shipment shipment = shipmentFactory.createShipment(slot, user, "Order-802", "Order-802", Shipment.State.READY);
        Batch shoppingBatch = batchFactory.createBatch(user, shipment, slot, Batch.Type.SHOPPING);
        shoppingBatch.setShift(shopperShift);
        shoppingBatch.setVehicle(1);
        batchFactory.save(shoppingBatch);

        Batch deliveryBatch1 = batchFactory.createBatch(user, shipment, slot, Batch.Type.DELIVERY);
        deliveryBatch1.setShift(driverShift);
        batchFactory.save(deliveryBatch1);

        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/" + "Order-802" + "/cancel")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()));

        Thread.sleep(1000);

        Optional<Batch> batchResult = batchRepository.findById(shoppingBatch.getId());
        List<Job> jobs = jobRepository.findAllByShipmentIdIn(Arrays.asList(shipment.getId()));
        Assert.assertEquals(0, jobs.size());
        Assert.assertFalse(batchResult.isPresent());

        Mockito.verify(shopperAutoAssignmentService).publishAutoAssignmentEvent(Mockito.anyString(),
                Mockito.anyString(), Mockito.isNull(), Mockito.eq(SlotOptimizationEvent.AutoAssignmentTriggerEvent.SLOT_OPTIMIZATION));
    }
}
