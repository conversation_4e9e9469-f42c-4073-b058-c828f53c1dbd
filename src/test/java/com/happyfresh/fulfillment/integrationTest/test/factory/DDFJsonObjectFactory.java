package com.happyfresh.fulfillment.integrationTest.test.factory;

import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

@Service
@Profile("it")
public class DDFJsonObjectFactory {

    public JSONObject createDDF() throws Exception {
        JSONObject ddfObject = new JSONObject();
        JSONArray ddfArray = new JSONArray();

        ddfArray.put(createFreeDeliveryThresholdMatrix(1D));
        ddfArray.put(createNextHourMultiplierMatrix(2D));

        // Per Km
        ddfArray.put(createTravellingDistanceAdditionMatrix(0, 1, 3D));

        // Per distance, in this case the value is 5
        ddfArray.put(createTravellingDistanceAdditionMatrix(5, 0, 3D));

        ddfArray.put(createExemptedCapacityMatrix(4D));
        ddfArray.put(createSlotUtilizationMultiplierMatrix(5D));
        ddfArray.put(createRemainingTimeMultiplierMatrix(6D));
        ddfArray.put(createTimeBasedFeeMatrix(7D));

        ddfObject.put("ddf", ddfArray);

        return ddfObject;
    }

    public JSONObject createFreeDeliveryThresholdMatrix(double value) throws Exception {
        JSONObject ddfObject = new JSONObject();
        ddfObject.put("column_id", 0);
        ddfObject.put("row_id", 0);
        ddfObject.put("type", "FreeDeliveryThresholdMatrix");
        ddfObject.put("value", value);

        return ddfObject;
    }

    public JSONObject createNextHourMultiplierMatrix(double value) throws Exception {
        JSONObject ddfObject = new JSONObject();
        ddfObject.put("column_id", 0);
        ddfObject.put("row_id", 0);
        ddfObject.put("type", "NextHourMultiplierMatrix");
        ddfObject.put("value", value);

        return ddfObject;

    }

    public JSONObject createTravellingDistanceAdditionMatrix(int colId, int rowId, double value) throws Exception {
        JSONObject ddfObject = new JSONObject();
        ddfObject.put("column_id", colId);
        ddfObject.put("row_id", rowId);
        ddfObject.put("type", "TravellingDistanceAdditionMatrix");
        ddfObject.put("value", value);

        return ddfObject;

    }

    public JSONObject createExemptedCapacityMatrix(double value) throws Exception {
        JSONObject ddfObject = new JSONObject();
        ddfObject.put("column_id", 0);
        ddfObject.put("row_id", 0);
        ddfObject.put("type", "ExemptedCapacityMatrix");
        ddfObject.put("value", value);

        return ddfObject;

    }

    public JSONObject createSlotUtilizationMultiplierMatrix(double value) throws Exception {
        JSONObject ddfObject = new JSONObject();
        ddfObject.put("column_id", 1);
        ddfObject.put("row_id", 0);
        ddfObject.put("type", "SlotUtilizationMultiplierMatrix");
        ddfObject.put("value", value);

        return ddfObject;

    }

    public JSONObject createRemainingTimeMultiplierMatrix(double value) throws Exception {
        JSONObject ddfObject = new JSONObject();
        ddfObject.put("column_id", 0);
        ddfObject.put("row_id", 2);
        ddfObject.put("type", "RemainingTimeMultiplierMatrix");
        ddfObject.put("value", value);

        return ddfObject;

    }

    public JSONObject createTimeBasedFeeMatrix(double value) throws Exception {
        JSONObject ddfObject = new JSONObject();
        ddfObject.put("column_id", 5);
        ddfObject.put("row_id", 10);
        ddfObject.put("type", "TimeBasedFeeMatrix");
        ddfObject.put("value", value);

        return ddfObject;

    }


}
