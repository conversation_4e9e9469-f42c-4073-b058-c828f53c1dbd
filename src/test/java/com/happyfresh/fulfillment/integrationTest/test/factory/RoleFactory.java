package com.happyfresh.fulfillment.integrationTest.test.factory;

import com.happyfresh.fulfillment.entity.Role;
import com.happyfresh.fulfillment.entity.Tenant;
import com.happyfresh.fulfillment.repository.RoleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
public class RoleFactory {
    @Autowired
    private RoleRepository roleRepository;

    @Transactional
    public Role createRole(Role.Name roleName, Long creatorId, Tenant tenant) {
        Role role = new Role();
        role.setName(roleName);
        role.setCreatedAt(LocalDateTime.now());
        role.setCreatedBy(creatorId);
        role.setTenant(tenant);
        if (roleRepository != null) {
            return roleRepository.save(role);
        } else {
            return null;
        }
    }

    @Transactional
    public Role createBackOfficeRole(Long creatorId, Tenant tenant) {
        return createRole(Role.Name.BACK_OFFICE, creatorId, tenant);
    }

    @Transactional
    public Role createODRangerRole(Long creatorId, Tenant tenant) {
        return createRole(Role.Name.ON_DEMAND_RANGER, creatorId, tenant);
    }

    @Transactional
    public Role createShopperRole(Long creatorId, Tenant tenant) {
        return createRole(Role.Name.SHOPPER, creatorId, tenant);
    }

    @Transactional
    public Role createDriverRole(Long creatorId, Tenant tenant) {
        return createRole(Role.Name.DRIVER, creatorId, tenant);
    }
}
