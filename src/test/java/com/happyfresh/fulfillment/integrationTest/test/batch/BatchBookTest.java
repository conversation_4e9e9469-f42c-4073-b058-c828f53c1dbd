package com.happyfresh.fulfillment.integrationTest.test.batch;

import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.messaging.kafka.KafkaMessage;
import com.happyfresh.fulfillment.common.messaging.kafka.KafkaTopicConfig;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import org.hamcrest.Matchers;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;

public class BatchBookTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private BatchFactory batchFactory;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private ItemFactory itemFactory;

    @Autowired
    private ClusterFactory clusterFactory;

    @Autowired
    private ShiftFactory shiftFactory;

    @Autowired
    private JobFactory jobFactory;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private TransactionHelper transactionHelper;

    @MockBean
    private KafkaMessage kafkaMessage;

    @Test
    public void return200IfExists() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, driver);

        Batch batch = batchFactory.createBatch(driver, slots.get(0), Batch.Type.DELIVERY);

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + batch.getId() + "/book")
                .header("X-Fulfillment-User-Token", driver.getToken())
                .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.shipments[0].delivery_job.state", Matchers.equalTo(Job.State.INITIAL.toString())))
                .andExpect(MockMvcResultMatchers.status().isOk());

        // Not fulfilled by strato
        Mockito.verify(kafkaMessage, Mockito.never())
                .publish(eq(KafkaTopicConfig.STRATO_INTEGRATION_TOPIC), anyString(), anyString());
    }

    @Test
    public void return200IfExists_sendWebhookIfFulfilledByStrato() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        StockLocation stockLocation = stockLocations.get(0);
        stockLocation.setEnabler(StockLocation.Enabler.HFC);
        stockLocation.setEnablerPlatform(StockLocation.EnablerPlatform.STRATO);
        stockLocationFactory.save(stockLocation);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, driver);

        Batch batch = batchFactory.createBatch(driver, slots.get(0), Batch.Type.DELIVERY);

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + batch.getId() + "/book")
                .header("X-Fulfillment-User-Token", driver.getToken())
                .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.shipments[0].delivery_job.state", Matchers.equalTo(Job.State.INITIAL.toString())))
                .andExpect(MockMvcResultMatchers.status().isOk());

        Mockito.verify(kafkaMessage, Mockito.atLeastOnce())
                .publish(eq(KafkaTopicConfig.STRATO_INTEGRATION_TOPIC), anyString(), anyString());
    }

    @Test
    public void return200IfSomeShipmentsAreCancelled() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);
        User externalSystemAdmin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN, driver.getTenant());
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, driver);

        Slot slot = slots.get(0);
        Shipment shipment1 = shipmentFactory.createShipment(slot, driver);
        Shipment shipment2 = shipmentFactory.createShipment(slot, driver);
        List<Shipment> shipments = Lists.newArrayList(shipment1, shipment2);

        Batch batch = batchFactory.createBatch(driver, shipments, slot, Batch.Type.DELIVERY);

        // Cancel Shipment1/Job1
        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/"+ shipment1.getNumber() +"/cancel")
                .header("X-Fulfillment-User-Token", externalSystemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", externalSystemAdmin.getTenant().getToken()));

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + batch.getId() + "/book")
                .header("X-Fulfillment-User-Token", driver.getToken())
                .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.shipments[0].delivery_job.state", Matchers.equalTo(Job.State.INITIAL.toString())))
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    public void returnExceptionIfAllShipmentAreCancelled() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);
        User externalSystemAdmin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN, driver.getTenant());
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, driver.getTenant());
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, driver);

        Slot slot = slots.get(0);
        Shipment shipment1 = shipmentFactory.createShipment(slot, driver);
        Shipment shipment2 = shipmentFactory.createShipment(slot, driver);
        List<Shipment> shipments = Lists.newArrayList(shipment1, shipment2);

        Batch shoppingBatch = batchFactory.createBatch(shopper, shipments, slot, Batch.Type.SHOPPING);
        Batch batch = batchFactory.createBatch(driver, shipments, slot, Batch.Type.DELIVERY);
        Thread.sleep(750); // Prevent race condition with other tests.

        // Cancel Shipment1/Job1
        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/"+ shipment1.getNumber() +"/cancel")
                .header("X-Fulfillment-User-Token", externalSystemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", externalSystemAdmin.getTenant().getToken()));

        // Cancel Shipment2/Job2
        mvc.perform(MockMvcRequestBuilders.put("/api/shipments/"+ shipment2.getNumber() +"/cancel")
                .header("X-Fulfillment-User-Token", externalSystemAdmin.getToken())
                .header("X-Fulfillment-Tenant-Token", externalSystemAdmin.getTenant().getToken()));

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + batch.getId() + "/book")
                .header("X-Fulfillment-User-Token", driver.getToken())
                .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().isNotFound());
    }

    @Test
    public void return404IfNotExists() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);
        mvc.perform(MockMvcRequestBuilders.post("/api/batches/543/book")
                .header("X-Fulfillment-User-Token", driver.getToken())
                .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().isNotFound());
    }

    @Test
    public void returnExceptionIfAlreadyTaken() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, driver);

        Batch batch = batchFactory.createBatch(driver, driver, slots.get(0), Batch.Type.DELIVERY);

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + batch.getId() + "/book")
                .header("X-Fulfillment-User-Token", driver.getToken())
                .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().isUnprocessableEntity());

    }

    @Test
    public void return200IfUserStillHavePendingBatch() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 16, Slot.Type.ONE_HOUR, driver);

        Batch bookedBatch = batchFactory.createBatch(driver, driver, slots.get(0), Batch.Type.DELIVERY);
        Batch unbookedBatch = batchFactory.createBatch(driver, slots.get(1), Batch.Type.DELIVERY);

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + unbookedBatch.getId() + "/book")
                .header("X-Fulfillment-User-Token", driver.getToken())
                .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

    }

    @Test
    public void returnExceptionIfRangerJobBooked() throws Exception {
        User driver = userFactory.createUserData(Role.Name.DRIVER);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, driver);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, driver);

        Batch batch = batchFactory.createBatch(driver, slots.get(0), Batch.Type.RANGER);

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + batch.getId() + "/book")
                .header("X-Fulfillment-User-Token", driver.getToken())
                .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken()))
                .andExpect(MockMvcResultMatchers.status().isUnprocessableEntity());
    }

    @Test
    public void returnShipmentByNumber() throws Exception {
        User systemAdmin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        User driver = userFactory.createUserData(Role.Name.DRIVER, systemAdmin.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, systemAdmin);
        List<Slot> slots = slotFactory.createSlots(stockLocations, 6, 6, 14, 14, Slot.Type.ONE_HOUR, systemAdmin);

        Shipment shipment = shipmentFactory.createShipment(slots.get(0), systemAdmin, "R234567", "H234567");
        List<Item> items = itemFactory.createItems(shipment, systemAdmin, 1);

        Batch shoppingBatch = batchFactory.createBatch(systemAdmin, shipment, slots.get(0), Batch.Type.SHOPPING);
        Batch deliveryBatch = batchFactory.createBatch(systemAdmin, shipment, slots.get(0), Batch.Type.DELIVERY);

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + deliveryBatch.getId() + "/book")
                .header("X-Fulfillment-User-Token", driver.getToken())
                .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken())
                .header("Locale", "en"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.batch.shipments[0].delivery_job.delivery_sequence", Matchers.equalTo(1)))
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    public void return200IfAllShoppingStartedOnLDSCluster() throws Exception {
        User systemAdmin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        User driver = userFactory.createUserData(Role.Name.DRIVER, systemAdmin.getTenant());
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, systemAdmin.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, systemAdmin);
        StockLocation stockLocation = stockLocations.get(0);
        Cluster cluster = stockLocation.getCluster();
        cluster.setSlotType(Slot.Type.LONGER_DELIVERY);
        clusterFactory.save(cluster);

        Slot slot = slotFactory.createLongerDeliverySlot(stockLocation, systemAdmin, 0, 1, 4);
        Shift shiftShopper = shiftFactory.createShift(stockLocation, systemAdmin, Shift.Type.SHOPPER, slot.getStartTime().minusHours(2), slot.getEndTime().minusHours(2), 1);
        Shift shiftDriver = shiftFactory.createShift(stockLocation, systemAdmin, Shift.Type.DRIVER, slot.getStartTime(), slot.getEndTime(), 2);

        // First Shipment
        Shipment shipment1 = shipmentFactory.createShipment(slot, systemAdmin, "H234567", "H234567", Shipment.State.READY);
        Batch shoppingBatch1 = batchFactory.createBatch(systemAdmin, shipment1, slot, Batch.Type.SHOPPING);
        shoppingBatch1.setShift(shiftShopper);
        shoppingBatch1.setVehicle(1);
        shoppingBatch1.setDeliveryType(null);
        shoppingBatch1.setUser(shopper);
        batchFactory.save(shoppingBatch1);

        // Item height = width = depth = 10 cm
        // Total Volume: 3000mL
        itemFactory.createItems(shipment1, systemAdmin, 3);

        // Second Shipment
        Shipment shipment2 = shipmentFactory.createShipment(slot, systemAdmin, "H345678", "H345678", Shipment.State.READY);
        Batch shoppingBatch2 = batchFactory.createBatch(systemAdmin, shipment2, slot, Batch.Type.SHOPPING);
        shoppingBatch2.setShift(shiftShopper);
        shoppingBatch2.setVehicle(1);
        shoppingBatch2.setDeliveryType(null);
        shoppingBatch2.setUser(shopper);
        batchFactory.save(shoppingBatch2);

        // Item height = width = depth = 10 cm
        // Total Volume: 3000mL
        itemFactory.createItems(shipment2, systemAdmin, 3);

        List<Shipment> shipments = new ArrayList<>();
        shipments.add(shipment1);
        shipments.add(shipment2);

        Batch deliveryBatch = batchFactory.createBatch(systemAdmin, shipments, slot, Batch.Type.DELIVERY);
        deliveryBatch.setShift(shiftDriver);
        batchFactory.save(deliveryBatch);

        transactionHelper.withNewTransaction(() -> {
            Shipment _shipment1 = shipmentRepository.findByNumber(shipment1.getNumber());
            Shipment _shipment2 = shipmentRepository.findByNumber(shipment2.getNumber());

            Job shoppingJob1 = _shipment1.getShoppingJob().get();
            shoppingJob1.setState(Job.State.STARTED);
            jobFactory.save(shoppingJob1);

            Job shoppingJob2 = _shipment2.getShoppingJob().get();
            shoppingJob2.setState(Job.State.STARTED);
            jobFactory.save(shoppingJob2);
        });

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + deliveryBatch.getId() + "/book")
                .header("X-Fulfillment-User-Token", driver.getToken())
                .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken())
                .header("Locale", "en"))
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    public void return422IfOneOfTheShoppingAreNotStartedOnLDSCluster() throws Exception {
        User systemAdmin = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        User driver = userFactory.createUserData(Role.Name.DRIVER, systemAdmin.getTenant());
        User shopper = userFactory.createUserData(Role.Name.SHOPPER, systemAdmin.getTenant());

        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(1, systemAdmin);
        StockLocation stockLocation = stockLocations.get(0);
        Cluster cluster = stockLocation.getCluster();
        cluster.setSlotType(Slot.Type.LONGER_DELIVERY);
        clusterFactory.save(cluster);

        Slot slot = slotFactory.createLongerDeliverySlot(stockLocation, systemAdmin, 0, 1, 4);
        Shift shiftShopper = shiftFactory.createShift(stockLocation, systemAdmin, Shift.Type.SHOPPER, slot.getStartTime().minusHours(2), slot.getEndTime().minusHours(2), 1);
        Shift shiftDriver = shiftFactory.createShift(stockLocation, systemAdmin, Shift.Type.DRIVER, slot.getStartTime(), slot.getEndTime(), 2);

        // First Shipment
        Shipment shipment1 = shipmentFactory.createShipment(slot, systemAdmin, "H234567", "H234567", Shipment.State.READY);
        Batch shoppingBatch1 = batchFactory.createBatch(systemAdmin, shipment1, slot, Batch.Type.SHOPPING);
        shoppingBatch1.setShift(shiftShopper);
        shoppingBatch1.setVehicle(1);
        shoppingBatch1.setDeliveryType(null);
        shoppingBatch1.setUser(shopper);
        batchFactory.save(shoppingBatch1);

        // Item height = width = depth = 10 cm
        // Total Volume: 3000mL
        itemFactory.createItems(shipment1, systemAdmin, 3);

        // Second Shipment
        Shipment shipment2 = shipmentFactory.createShipment(slot, systemAdmin, "H345678", "H345678", Shipment.State.READY);
        Batch shoppingBatch2 = batchFactory.createBatch(systemAdmin, shipment2, slot, Batch.Type.SHOPPING);
        shoppingBatch2.setShift(shiftShopper);
        shoppingBatch2.setVehicle(1);
        shoppingBatch2.setDeliveryType(null);
        batchFactory.save(shoppingBatch2);

        // Item height = width = depth = 10 cm
        // Total Volume: 3000mL
        itemFactory.createItems(shipment2, systemAdmin, 3);

        List<Shipment> shipments = new ArrayList<>();
        shipments.add(shipment1);
        shipments.add(shipment2);

        Batch deliveryBatch = batchFactory.createBatch(systemAdmin, shipments, slot, Batch.Type.DELIVERY);
        deliveryBatch.setShift(shiftDriver);
        batchFactory.save(deliveryBatch);

        transactionHelper.withNewTransaction(() -> {
            Shipment _shipment1 = shipmentRepository.findByNumber(shipment1.getNumber());
            Shipment _shipment2 = shipmentRepository.findByNumber(shipment2.getNumber());

            Job shoppingJob1 = _shipment1.getShoppingJob().get();
            shoppingJob1.setState(Job.State.STARTED);
            jobFactory.save(shoppingJob1);
        });

        mvc.perform(MockMvcRequestBuilders.post("/api/batches/" + deliveryBatch.getId() + "/book")
                .header("X-Fulfillment-User-Token", driver.getToken())
                .header("X-Fulfillment-Tenant-Token", driver.getTenant().getToken())
                .header("Locale", "en"))
                .andExpect(MockMvcResultMatchers.status().isUnprocessableEntity());
    }
}
