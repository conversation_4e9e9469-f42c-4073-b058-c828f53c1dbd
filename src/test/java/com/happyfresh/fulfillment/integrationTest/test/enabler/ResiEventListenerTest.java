package com.happyfresh.fulfillment.integrationTest.test.enabler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.presenter.ResiEvent;
import com.happyfresh.fulfillment.common.service.SymmetricEncryptionService;
import com.happyfresh.fulfillment.enabler.form.ResiWebhookForm;
import com.happyfresh.fulfillment.enabler.messaging.ResiEventListener;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.enabler.helper.JubelioMockServerHelper;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.JubelioDeliveryRepository;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;

import java.util.HashMap;
import java.util.Map;
import static java.nio.file.Files.readAllBytes;
import static java.nio.file.Paths.get;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.*;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withStatus;

public class ResiEventListenerTest extends BaseTest {

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private StockLocationFactory slFactory;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private ShipmentFactory shipmentFactory;

    @Autowired
    private JubelioDeliveryFactory jubelioDeliveryFactory;

    @Autowired
    private JubelioDeliveryRepository jubelioDeliveryRepository;

    @Autowired
    private JubelioMockServerHelper mockServerHelper;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private ResiEventListener listener;

    @Autowired
    private SymmetricEncryptionService encryptionService;

    private User creator;

    private Shipment shipment;

    private JubelioDelivery jubelioDelivery;

    private String webhookBody;

    private String plainToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************.Whj7i1jJPIi7YkbNxjs63GVXEouE5h0bQSSvagXyf8Y";

    @Before
    public void init() throws Exception {
        if (this.creator == null) {
            this.creator = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        }
        if (this.shipment == null) {
            StockLocation stockLocation = slFactory.createBrandStoreStockLocations(1, this.creator, StockLocation.Type.ORIGINAL).get(0);
            Map<String, String> prefs = new HashMap<>();
            prefs.put("jubelio_email", "<EMAIL>");
            prefs.put("jubelio_password",  encryptionService.encryptString("Password01!"));
            prefs.put("jubelio_last_token_used", encryptionService.encryptString(this.plainToken));
            stockLocation.setPreferences(prefs);
            slFactory.save(stockLocation);

            userFactory.createUserData(Role.Name.JUBELIO_SHOPPER, this.creator.getTenant());
            userFactory.createUserData(Role.Name.JUBELIO_DRIVER, this.creator.getTenant());

            Slot slot = slotFactory.createSlot(stockLocation, this.creator, 0, 0);
            this.shipment = shipmentFactory.createShipment(slot, this.creator);
        }
        if (this.jubelioDelivery == null) {
            this.jubelioDelivery = jubelioDeliveryFactory.createOne(this.shipment, this.creator);
        }
        if (this.webhookBody == null) {
            final byte[] webhookFixture = readAllBytes(get("src", "test", "resources", "fixtures", "resi_webhook_payload.json"));
            this.webhookBody = mapper.readTree(webhookFixture).get("body").toString();
        }
    }

    @Ignore("Not used anymore")
    @Test
    public void serializeDeserializeResiWebhookForm() throws Exception {
        ResiWebhookForm form = mapper.readValue(this.webhookBody, ResiWebhookForm.class);

        // String -> ResiWebhookForm -> String
        String string = mapper.writeValueAsString(form);
        ResiWebhookForm deserializedForm = mapper.readValue(string, ResiWebhookForm.class);
        Assert.assertEquals(form.getResult().getSummary().getDate(), deserializedForm.getResult().getSummary().getDate());
        String serializedForm = mapper.writeValueAsString(deserializedForm);
        Assert.assertEquals(string, serializedForm);
    }

    @Ignore("Not used anymore")
    @Test
    public void resiEventListener_shouldCompleteSalesOrder() throws Exception {
        final Long salesOrderId = this.jubelioDelivery.getSalesOrderId();

        MockRestServiceServer mockServer = mockServerHelper.buildMockServer();
        byte[] response = readAllBytes(get("src", "test", "resources", "fixtures", "jubelio_sales_order_detail_response.json"));
        mockServer.expect(ExpectedCount.once(), requestTo("https://api.jubelio.com/sales/orders/" + salesOrderId))
                .andExpect(method(HttpMethod.GET))
                .andRespond(withStatus(HttpStatus.OK).contentType(MediaType.APPLICATION_JSON).body(response));

        mockServer.expect(ExpectedCount.min(1), requestTo("https://api.jubelio.com/sales/orders/mark-as-complete"))
                .andExpect(method(HttpMethod.POST))
                .andExpect(content().string("{\"ids\": [\"" + salesOrderId + "\"]}"))
                .andRespond(withStatus(HttpStatus.OK).contentType(MediaType.APPLICATION_JSON).body("{\"status\": \"ok\"}"));

        ResiEvent event = buildResiEventFromWebhook();

        transactionHelper.withNewTransactionReadOnly(() -> {
            listener.listen(event);

            JubelioDelivery jubDelivery = jubelioDeliveryRepository.findById(this.jubelioDelivery.getId()).get();
            Assert.assertNotNull(jubDelivery);
            Assert.assertEquals(JubelioDelivery.State.COMPLETED, jubDelivery.getState());
        });
    }

    private ResiEvent buildResiEventFromWebhook() throws Exception {
        ResiWebhookForm form = mapper.readValue(this.webhookBody, ResiWebhookForm.class);
        Assert.assertEquals("DELIVERED", form.getResult().getStatus());

        this.shipment.setOrderNumber(form.getExternalId());
        shipmentFactory.save(this.shipment);

        ResiEvent event = new ResiEvent();
        event.setPayload(mapper.writeValueAsString(form));

        return event;
    }

}
