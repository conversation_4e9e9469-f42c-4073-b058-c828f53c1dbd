package com.happyfresh.fulfillment.integrationTest.test.slot;

import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.*;
import com.happyfresh.fulfillment.repository.ClusterRepository;
import org.elasticsearch.common.geo.GeoPoint;
import org.hamcrest.Matchers;
import org.json.JSONObject;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.List;

public class SlotAvailabilityClusterTest extends BaseTest {

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private ShipmentJsonObjectFactory shipmentJsonObjectFactory;

    @Autowired
    private ClusterRepository clusterRepository;

    @Autowired
    private RouteEdgeFactory routeEdgeFactory;

    @Before
    public void setUp() throws InterruptedException {
        Thread.sleep(750);
    }

    @Test
    public void rangerAndSndOnSameClusterTestCase1() throws Exception {
        /** Ranger store -> ranger 1
         *  SND store -> shopper 1, driver 1
         *  Create 2 order on SND batch, should return false on Ranger store availability */
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user);

        /** SND store */
        StockLocation sndStockLocation = stockLocations.get(0);
        sndStockLocation.setType(StockLocation.Type.ORIGINAL);
        sndStockLocation.setShopperAveragePickingTimePerUniqItem(20);
        sndStockLocation.setMaxDeliveryHandover(15);
        stockLocationFactory.save(sndStockLocation);
        Slot sndSlot = slotFactory.createSlot(sndStockLocation, user, 0, 2);

        /** Ranger store */
        StockLocation rangerStockLocation = stockLocations.get(1);
        rangerStockLocation.setType(StockLocation.Type.SPECIAL);
        rangerStockLocation.setShopperAveragePickingTimePerUniqItem(20);
        stockLocationFactory.save(rangerStockLocation);
        Slot rangerSlot = slotFactory.createSlot(rangerStockLocation, user, 0, 2);

        GeoPoint sndStore = new GeoPoint(sndStockLocation.getLat(), sndStockLocation.getLon());
        GeoPoint menaraFIF = new GeoPoint(-6.292569, 106.783729);
        GeoPoint antam = new GeoPoint( -6.302788, 106.842377);
        routeEdgeFactory.create(sndStore, menaraFIF, 1500, 1000);
        routeEdgeFactory.create(sndStore, antam, 1500, 1000);

        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(sndStockLocation, sndSlot.getId(), 1, "Order-1", menaraFIF, "Menara FIF");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        shipmentObj = shipmentJsonObjectFactory.createShipment(sndStockLocation, sndSlot.getId(), 1, "Order-2", antam, "Antam");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        shipmentObj = shipmentJsonObjectFactory.createShipment(rangerStockLocation, 1, "Order-3", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/available")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[0].available", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
    }

    @Test
    public void rangerAndSndOnSameClusterTestCase2() throws Exception {
        /** Ranger store -> ranger 1
         *  SND store -> shopper 1, driver 1
         *  Create 2 order on Ranger batch, should return false on SND store availability */
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user);

        /** SND store */
        StockLocation sndStockLocation = stockLocations.get(0);
        sndStockLocation.setType(StockLocation.Type.ORIGINAL);
        sndStockLocation.setShopperAveragePickingTimePerUniqItem(20);
        sndStockLocation.setEnableGrabExpress(false);
        sndStockLocation.setEnableGrabExpressCod(false);
        stockLocationFactory.save(sndStockLocation);
        Slot sndSlot = slotFactory.createSlot(sndStockLocation, user, 0, 2);

        /** Ranger store */
        StockLocation rangerStockLocation = stockLocations.get(1);
        rangerStockLocation.setType(StockLocation.Type.SPECIAL);
        rangerStockLocation.setShopperAveragePickingTimePerUniqItem(20);
        rangerStockLocation.setEnableGrabExpress(false);
        rangerStockLocation.setEnableGrabExpressCod(false);
        stockLocationFactory.save(rangerStockLocation);
        Slot rangerSlot = slotFactory.createSlot(rangerStockLocation, user, 0, 2);

        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(rangerStockLocation, rangerSlot.getId(), 1, "Order-1", new GeoPoint(-6.291202, 106.7855), "Lintasarta");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        shipmentObj = shipmentJsonObjectFactory.createShipment(rangerStockLocation, rangerSlot.getId(), 1, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        shipmentObj = shipmentJsonObjectFactory.createShipment(sndStockLocation, 1, "Order-3", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/available")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[0].available", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
    }

    @Test
    public void rangerAndSndOnSameClusterTestCase3() throws Exception {
        /** Ranger store -> ranger 1
         *  SND store -> shopper 1, driver 1
         *  Create 2 order Long Distance on SND batch, should return false on Ranger store availability */
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user);

        /** SND store */
        StockLocation sndStockLocation = stockLocations.get(0);
        sndStockLocation.setType(StockLocation.Type.ORIGINAL);
        sndStockLocation.setShopperAveragePickingTimePerUniqItem(20);
        stockLocationFactory.save(sndStockLocation);
        Slot sndSlot1 = slotFactory.createSlot(sndStockLocation, user, 0, 2);
        Slot sndSlot2 = slotFactory.createSlot(sndStockLocation, user, 0, 3);

        /** Ranger store */
        StockLocation rangerStockLocation = stockLocations.get(1);
        rangerStockLocation.setType(StockLocation.Type.SPECIAL);
        rangerStockLocation.setShopperAveragePickingTimePerUniqItem(20);
        stockLocationFactory.save(rangerStockLocation);
        Slot rangerSlot1 = slotFactory.createSlot(rangerStockLocation, user, 0, 2);
        Slot rangerSlot2 = slotFactory.createSlot(rangerStockLocation, user, 0, 3);

        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(sndStockLocation, sndSlot1.getId(), 1, "Order-1", new GeoPoint(-6.291654, 106.905593), "Lubang Buaya");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        shipmentObj = shipmentJsonObjectFactory.createShipment(sndStockLocation, sndSlot1.getId(), 1, "Order-2", new GeoPoint( -6.291654, 106.905593), "Lubang Buaya");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        shipmentObj = shipmentJsonObjectFactory.createShipment(rangerStockLocation, 1, "Order-3", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/available")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[0].available", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[1].available", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
    }

    @Test
    public void rangerAndSndOnSameClusterTestCase4() throws Exception {
        /** Ranger store -> ranger 1
         *  SND store -> shopper 1, driver 1
         *  Create 2 order Wider Ranger on Ranger batch, should return false on SND store availability */
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user);

        /** SND store */
        StockLocation sndStockLocation = stockLocations.get(0);
        sndStockLocation.setType(StockLocation.Type.ORIGINAL);
        sndStockLocation.setShopperAveragePickingTimePerUniqItem(20);
        sndStockLocation.setEnableGrabExpress(false);
        sndStockLocation.setEnableGrabExpressCod(false);
        stockLocationFactory.save(sndStockLocation);
        Slot sndSlot1 = slotFactory.createSlot(sndStockLocation, user, 0, 2);
        Slot sndSlot2 = slotFactory.createSlot(sndStockLocation, user, 0, 3);

        /** Ranger store */
        StockLocation rangerStockLocation = stockLocations.get(1);
        rangerStockLocation.setType(StockLocation.Type.SPECIAL);
        rangerStockLocation.setShopperAveragePickingTimePerUniqItem(20);
        rangerStockLocation.setEnableGrabExpress(false);
        rangerStockLocation.setEnableGrabExpressCod(false);
        stockLocationFactory.save(rangerStockLocation);
        Slot rangerSlot1 = slotFactory.createSlot(rangerStockLocation, user, 0, 2);
        Slot rangerSlot2 = slotFactory.createSlot(rangerStockLocation, user, 0, 3);

        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(rangerStockLocation, rangerSlot2.getId(), 3, "Order-1", new GeoPoint(-6.291202, 106.7855), "Lintasarta");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        shipmentObj = shipmentJsonObjectFactory.createShipment(rangerStockLocation, rangerSlot2.getId(), 3, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        shipmentObj = shipmentJsonObjectFactory.createShipment(sndStockLocation, 1, "Order-3", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/available")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[0].available", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[1].available", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
    }

    @Test
    public void rangerAndSndOnSameClusterTestCase5() throws Exception {
        /** Ranger store -> ranger 1
         *  SND store -> shopper 1, driver 1
         *  Create 2 order SND batch, should return false on Wider Ranger on Ranger store availability */
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user);

        /** SND store */
        StockLocation sndStockLocation = stockLocations.get(0);
        sndStockLocation.setType(StockLocation.Type.ORIGINAL);
        sndStockLocation.setShopperAveragePickingTimePerUniqItem(20);
        sndStockLocation.setMaxDeliveryHandover(15);
        stockLocationFactory.save(sndStockLocation);
        Slot sndSlot1 = slotFactory.createSlot(sndStockLocation, user, 0, 2);
        Slot sndSlot2 = slotFactory.createSlot(sndStockLocation, user, 0, 3);

        /** Ranger store */
        StockLocation rangerStockLocation = stockLocations.get(1);
        rangerStockLocation.setType(StockLocation.Type.SPECIAL);
        rangerStockLocation.setShopperAveragePickingTimePerUniqItem(20);
        stockLocationFactory.save(rangerStockLocation);
        Slot rangerSlot1 = slotFactory.createSlot(rangerStockLocation, user, 0, 2);
        Slot rangerSlot2 = slotFactory.createSlot(rangerStockLocation, user, 0, 3);

        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(sndStockLocation, sndSlot1.getId(), 1, "Order-1", new GeoPoint( -6.302788, 106.842377), "Antam");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        shipmentObj = shipmentJsonObjectFactory.createShipment(rangerStockLocation, 3, "Order-3", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/available")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[0].available", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[1].available", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        shipmentObj = shipmentJsonObjectFactory.createShipment(sndStockLocation, sndSlot1.getId(), 1, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        shipmentObj = shipmentJsonObjectFactory.createShipment(rangerStockLocation, 3, "Order-3", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/available")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[0].available", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[1].available", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
    }


    @Test
    public void rangerAndSndOnSameClusterTestCase6() throws Exception {
        /** Ranger store -> ranger 1
         *  SND store -> shopper 1, driver 1
         *  Create 2 order Ranger batch, should return false on Long Distance on SND store availability */
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user);

        /** SND store */
        StockLocation sndStockLocation = stockLocations.get(0);
        sndStockLocation.setType(StockLocation.Type.ORIGINAL);
        sndStockLocation.setShopperAveragePickingTimePerUniqItem(20);
        sndStockLocation.setEnableGrabExpress(false);
        sndStockLocation.setEnableGrabExpressCod(false);
        stockLocationFactory.save(sndStockLocation);
        Slot sndSlot1 = slotFactory.createSlot(sndStockLocation, user, 0, 2);
        Slot sndSlot2 = slotFactory.createSlot(sndStockLocation, user, 0, 3);

        /** Ranger store */
        StockLocation rangerStockLocation = stockLocations.get(1);
        rangerStockLocation.setType(StockLocation.Type.SPECIAL);
        rangerStockLocation.setShopperAveragePickingTimePerUniqItem(20);
        rangerStockLocation.setEnableGrabExpress(false);
        rangerStockLocation.setEnableGrabExpressCod(false);
        stockLocationFactory.save(rangerStockLocation);
        Slot rangerSlot1 = slotFactory.createSlot(rangerStockLocation, user, 0, 2);
        Slot rangerSlot2 = slotFactory.createSlot(rangerStockLocation, user, 0, 3);

        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(rangerStockLocation, rangerSlot1.getId(), 1, "Order-1", new GeoPoint(-6.291202, 106.7855), "Lintasarta");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        shipmentObj = shipmentJsonObjectFactory.createShipment(sndStockLocation, 1, "Order-3", new GeoPoint( -6.291654, 106.905593), "Lubang Buaya");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/available")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[0].available", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[1].available", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        shipmentObj = shipmentJsonObjectFactory.createShipment(rangerStockLocation, rangerSlot1.getId(), 1, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        shipmentObj = shipmentJsonObjectFactory.createShipment(sndStockLocation, 1, "Order-3", new GeoPoint( -6.291654, 106.905593), "Lubang Buaya");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/available")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[0].available", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[1].available", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
    }

    @Test
    public void rangerAndRangerOnSameClusterTestCase1() throws Exception {
        /** Ranger store 1 -> ranger 1
         *  Ranger store 2 -> ranger 0
         *  Create 1 order on Ranger Store 1, should return false on Ranger store 2 availability */
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user);

        /** Ranger store 1 */
        StockLocation rangerStockLocation1 = stockLocations.get(0);
        rangerStockLocation1.setType(StockLocation.Type.SPECIAL);
        rangerStockLocation1.setShopperAveragePickingTimePerUniqItem(20);
        stockLocationFactory.save(rangerStockLocation1);
        Slot rangerSlot1 = slotFactory.createSlot(rangerStockLocation1, user, 0, 2);

        /** Ranger store 2 */
        StockLocation rangerStockLocation2 = stockLocations.get(1);
        rangerStockLocation2.setType(StockLocation.Type.SPECIAL);
        rangerStockLocation2.setShopperAveragePickingTimePerUniqItem(20);
        stockLocationFactory.save(rangerStockLocation2);
        Slot rangerSlot2 = slotFactory.createSlot(rangerStockLocation2, user, 0, 2);
        rangerSlot2.setDriverCount(0);
        rangerSlot2.setShopperCount(0);
        slotFactory.save(rangerSlot2);

        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(rangerStockLocation2, 1, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/available")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[0].available", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        shipmentObj = shipmentJsonObjectFactory.createShipment(rangerStockLocation1, rangerSlot1.getId(), 1, "Order-1", new GeoPoint(-6.292569, 106.783729), "Menara FIF");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        shipmentObj = shipmentJsonObjectFactory.createShipment(rangerStockLocation2, 1, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/available")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[0].available", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
    }

    @Test
    public void rangerAndRangerOnSameClusterTestCase2() throws Exception {
        /** Ranger store 1, cluster 1 -> ranger 1
         *  Ranger store 2, cluster 1 -> ranger 0
         *  Ranger store 3, cluster 2 -> ranger 1
         *  Create 1 order on Ranger store -> should return false on Ranger store 2 availability, should return true on Ranger store 3 availability */
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(3, user);

        /** Ranger store 1 */
        StockLocation rangerStockLocation1 = stockLocations.get(0);
        rangerStockLocation1.setType(StockLocation.Type.SPECIAL);
        rangerStockLocation1.setShopperAveragePickingTimePerUniqItem(20);
        stockLocationFactory.save(rangerStockLocation1);
        Slot rangerSlot1 = slotFactory.createSlot(rangerStockLocation1, user, 0, 2);

        /** Ranger store 2 */
        StockLocation rangerStockLocation2 = stockLocations.get(1);
        rangerStockLocation2.setType(StockLocation.Type.SPECIAL);
        rangerStockLocation2.setShopperAveragePickingTimePerUniqItem(20);
        stockLocationFactory.save(rangerStockLocation2);
        Slot rangerSlot2 = slotFactory.createSlot(rangerStockLocation2, user, 0, 2);
        rangerSlot2.setDriverCount(0);
        rangerSlot2.setShopperCount(0);
        slotFactory.save(rangerSlot2);

        /** Ranger store 3 */
        Cluster cluster2 = new Cluster();
        cluster2.setName("CL-02");
        cluster2.setTenant(user.getTenant());
        cluster2.setCreatedBy(user.getId());
        cluster2.setSlotType(Slot.Type.ONE_HOUR);
        clusterRepository.save(cluster2);
        StockLocation rangerStockLocation3 = stockLocations.get(2);
        rangerStockLocation3.setCluster(cluster2);
        rangerStockLocation3.setType(StockLocation.Type.SPECIAL);
        rangerStockLocation3.setShopperAveragePickingTimePerUniqItem(20);
        stockLocationFactory.save(rangerStockLocation3);
        Slot rangerSlot3 = slotFactory.createSlot(rangerStockLocation3, user, 0, 2);

        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(rangerStockLocation1, rangerSlot1.getId(), 1, "Order-1", new GeoPoint(-6.292569, 106.783729), "Menara FIF");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        shipmentObj = shipmentJsonObjectFactory.createShipment(rangerStockLocation2, 1, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/available")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[0].available", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        shipmentObj = shipmentJsonObjectFactory.createShipment(rangerStockLocation3, 1, "Order-2", new GeoPoint( -6.292569, 106.783729), "Menara FIF");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/available")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[0].available", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());
    }

    @Test
    public void sndAndSndOnSameClusterTestCase() throws Exception {
        /** SnD store -> shopper 1, driver 0
         *  SND store -> shopper 1, driver 1
         *  Create 2 order on SND batch, should return false on SnD store availability */
        User user = userFactory.createUserData(Role.Name.EXTERNAL_SYSTEM_ADMIN);
        List<StockLocation> stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user);

        /** SND store */
        StockLocation sndStockLocation1 = stockLocations.get(0);
        sndStockLocation1.setType(StockLocation.Type.ORIGINAL);
        sndStockLocation1.setShopperAveragePickingTimePerUniqItem(20);
        sndStockLocation1.setMaxDeliveryHandover(15);
        sndStockLocation1.setEnableGrabExpress(false);
        sndStockLocation1.setEnableGrabExpressCod(false);
        stockLocationFactory.save(sndStockLocation1);
        Slot sndSlot1 = slotFactory.createSlot(sndStockLocation1, user, 0, 2);

        StockLocation sndStockLocation2 = stockLocations.get(1);
        sndStockLocation2.setType(StockLocation.Type.ORIGINAL);
        sndStockLocation2.setShopperAveragePickingTimePerUniqItem(20);
        sndStockLocation2.setMaxDeliveryHandover(15);
        sndStockLocation2.setEnableGrabExpress(false);
        sndStockLocation2.setEnableGrabExpressCod(false);
        stockLocationFactory.save(sndStockLocation2);
        Slot sndSlot2 = slotFactory.createSlot(sndStockLocation2, user, 0, 2);
        sndSlot2.setDriverCount(0);
        slotFactory.save(sndSlot2);

        GeoPoint sndStore1 = new GeoPoint(sndStockLocation1.getLat(), sndStockLocation1.getLon());
        GeoPoint sndStore2 = new GeoPoint(sndStockLocation2.getLat(), sndStockLocation2.getLon());
        GeoPoint menaraFIF = new GeoPoint(-6.292569, 106.783729);
        GeoPoint antam = new GeoPoint( -6.302788, 106.842377);
        routeEdgeFactory.create(sndStore1, menaraFIF, 1500, 1000);
        routeEdgeFactory.create(sndStore1, antam, 1500, 1000);

        routeEdgeFactory.create(sndStore2, menaraFIF, 1500, 1000);
        routeEdgeFactory.create(sndStore2, antam, 1500, 1000);

        JSONObject shipmentObj = shipmentJsonObjectFactory.createShipment(sndStockLocation1, sndSlot1.getId(), 1, "Order-1", menaraFIF, "Menara FIF");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/available")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[0].available", Matchers.equalTo(true)))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj.toString()))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        JSONObject shipmentObj3 = shipmentJsonObjectFactory.createShipment(sndStockLocation2, sndSlot2.getId(), 1, "Order-2", antam, "Antam");
        mvc.perform(MockMvcRequestBuilders.post("/api/slots/available")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj3.toString()))
                .andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.jsonPath("$.slots[0].available", Matchers.equalTo(false)))
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful());

        mvc.perform(MockMvcRequestBuilders.post("/api/slots/set_slot")
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken())
                .contentType(MediaType.APPLICATION_JSON)
                .content(shipmentObj3.toString()))
                .andExpect(MockMvcResultMatchers.status().isUnprocessableEntity());
    }

}
