package com.happyfresh.fulfillment.integrationTest.test.slot;

import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.integrationTest.test.BaseTest;
import com.happyfresh.fulfillment.integrationTest.test.factory.ShipmentFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.SlotFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.StockLocationFactory;
import com.happyfresh.fulfillment.integrationTest.test.factory.UserFactory;
import com.happyfresh.fulfillment.repository.ClusterRepository;
import com.happyfresh.fulfillment.repository.SlotRepository;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class SlotUploadV2Test extends BaseTest {

    @Autowired
    private SlotRepository slotRepository;

    @Autowired
    private StockLocationFactory stockLocationFactory;

    @Autowired
    private UserFactory userFactory;

    @Autowired
    private TransactionHelper transactionHelper;

    @Autowired
    private SlotFactory slotFactory;

    @Autowired
    private ClusterRepository clusterRepository;

    @Autowired
    private ShipmentFactory shipmentFactory;

    private User user;
    private DateTimeFormatter formatter;
    private List<StockLocation> stockLocations;

    @Before
    public void setUp() {
        formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        user = userFactory.createUserData(Role.Name.ADMIN);
        stockLocations = stockLocationFactory.createNStockLocationsOnTheSameCluster(2, user);
    }

    @Test
    public void slotUploadTest() throws Exception {
        InputStream csvStream = new ByteArrayInputStream(constructCSVString().getBytes());
        MockMultipartFile file = new MockMultipartFile("file", csvStream);

        mvc.perform(MockMvcRequestBuilders.multipart("/api/slots/v2/upload").file(file)
            .header("X-Fulfillment-User-Token", user.getToken())
            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()))
            .andExpect(status().is2xxSuccessful());

        transactionHelper.withNewTransaction(() -> {
            List<Slot> slots = slotRepository.findAll();
            List<Slot> store118Slots = slots.stream().filter( slot -> slot.getStockLocation().getExternalId() == 118).collect(Collectors.toList());
            List<Slot> store1Slots = slots.stream().filter( slot -> slot.getStockLocation().getExternalId() == 1).collect(Collectors.toList());
            Assert.assertEquals(2, store1Slots.size());
            Assert.assertEquals(2, store118Slots.size());
            // Should store datetime in UTC
            Assert.assertNotNull(store118Slots.stream().filter(s -> s.getStartTime().isEqual(LocalDateTime.parse("2020-06-14 05:00:00", formatter))).findFirst().orElse(null));
            Assert.assertNotNull(store118Slots.stream().filter(s -> s.getStartTime().isEqual(LocalDateTime.parse("2020-06-14 09:00:00", formatter))).findFirst().orElse(null));
        });
    }

    @Test
    public void shouldAllowSlotBeforeStartOfDayInUtc() throws Exception {
        LocalDateTime startA = LocalDateTime.of(2021, 3, 2, 5, 0); // 05:00 WIB
        LocalDateTime startB = LocalDateTime.of(2021, 3, 2, 9, 0); // 09:00 WIB
        String csvString = constructCSVString(startA, startB, true);

        InputStream csvStream = new ByteArrayInputStream(csvString.getBytes());
        MockMultipartFile file = new MockMultipartFile("file", csvStream);

        mvc.perform(MockMvcRequestBuilders.multipart("/api/slots/v2/upload").file(file)
                .header("X-Fulfillment-User-Token", user.getToken())
                .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()))
                .andExpect(status().is2xxSuccessful());

        transactionHelper.withNewTransaction(() -> {
            List<Slot> slots = slotRepository.findAll();
            List<Slot> store118Slots = slots.stream().filter( slot -> slot.getStockLocation().getExternalId() == 118).collect(Collectors.toList());
            List<Slot> store1Slots = slots.stream().filter( slot -> slot.getStockLocation().getExternalId() == 1).collect(Collectors.toList());
            Assert.assertEquals(2, store1Slots.size());
            Assert.assertEquals(2, store118Slots.size());
            // Should store datetime in UTC
            Assert.assertNotNull(store118Slots.stream().filter(s -> s.getStartTime().isEqual(LocalDateTime.parse("2021-03-01 22:00:00", formatter))).findFirst().orElse(null));
            Assert.assertNotNull(store118Slots.stream().filter(s -> s.getStartTime().isEqual(LocalDateTime.parse("2021-03-02 02:00:00", formatter))).findFirst().orElse(null));
        });
    }

    @Test
    public void shouldNotAllowUpdateSlotWithExistingShipment() throws Exception {
        StockLocation store1 = stockLocations.get(0);
        LocalDate slotsFromDt = LocalDateTime.now().plusDays(1).toLocalDate();
        LocalDate slotsToDt = slotsFromDt.plusDays(6);
        List<Slot> oneHourSlots = slotFactory.createSlots(
            Collections.singletonList(store1), slotsFromDt, slotsToDt, Slot.Type.ONE_HOUR, user, 1, 1);
        Assert.assertEquals(98, oneHourSlots.size());
        Assert.assertTrue(oneHourSlots.stream().allMatch( s -> s.getType().equals(Slot.Type.ONE_HOUR)));
        Shipment shipment = shipmentFactory.createShipment(oneHourSlots.get(0), user);

        transactionHelper.withNewTransaction( () -> {
            stockLocations.forEach( stockLocation -> {
                Cluster cluster = stockLocation.getCluster();
                cluster.setSlotType(Slot.Type.LONGER_DELIVERY);
                clusterRepository.save(cluster);
            });
        });

        InputStream csvStream = new ByteArrayInputStream(constructCSVString(slotsFromDt, true).getBytes());
        MockMultipartFile file = new MockMultipartFile("file", csvStream);

        mvc.perform(MockMvcRequestBuilders.multipart("/api/slots/v2/upload").file(file)
            .header("X-Fulfillment-User-Token", user.getToken())
            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()))
            .andExpect(status().is4xxClientError());
    }

    @Test
    public void shouldDeleteSlotsWithClusterTypeChange() throws Exception {
        StockLocation store1 = stockLocations.get(0);
        LocalDate slotsFromDt = LocalDateTime.now().plusDays(1).toLocalDate();
        LocalDate slotsToDt = slotsFromDt.plusDays(6);
        List<Slot> oneHourSlots = slotFactory.createSlots(
            Collections.singletonList(store1), slotsFromDt, slotsToDt, Slot.Type.ONE_HOUR, user, 1, 1);
        Assert.assertEquals(98, oneHourSlots.size());
        Assert.assertTrue(oneHourSlots.stream().allMatch( s -> s.getType().equals(Slot.Type.ONE_HOUR)));
        Shipment shipment = shipmentFactory.createShipment(oneHourSlots.get(0), user);

        transactionHelper.withNewTransaction( () -> {
            stockLocations.forEach( stockLocation -> {
                Cluster cluster = stockLocation.getCluster();
                cluster.setSlotType(Slot.Type.LONGER_DELIVERY);
                clusterRepository.save(cluster);
            });
        });

        InputStream csvStream = new ByteArrayInputStream(constructCSVString(slotsFromDt.plusDays(1), true).getBytes());
        MockMultipartFile file = new MockMultipartFile("file", csvStream);

        mvc.perform(MockMvcRequestBuilders.multipart("/api/slots/v2/upload").file(file)
            .header("X-Fulfillment-User-Token", user.getToken())
            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()))
            .andExpect(status().is2xxSuccessful());

        transactionHelper.withNewTransaction(() -> {
            List<Slot> slots = slotRepository.findAll();
            Assert.assertEquals(18, slots.size());
            Assert.assertTrue(slots.stream()
                .filter( s -> s.getStartTime().compareTo(slotsFromDt.plusDays(1).atStartOfDay()) > 0 )
                .allMatch( s -> s.getType().equals(Slot.Type.LONGER_DELIVERY)));
        });
    }

    @Test
    public void shouldValidateSameLocalDateForStartAndEnd() throws Exception {
        String csvString = constructCSVString(LocalDate.of(2020, 6, 14), false);
        String addedCsvString = csvString +
            "118,2020-06-15 09:00:00,2020-06-16 13:00:00,true";
        InputStream csvStream = new ByteArrayInputStream(addedCsvString.getBytes());
        MockMultipartFile file = new MockMultipartFile("file", csvStream);

        mvc.perform(MockMvcRequestBuilders.multipart("/api/slots/v2/upload").file(file)
            .header("X-Fulfillment-User-Token", user.getToken())
            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()))
            .andExpect(status().is4xxClientError());
    }

    @Test
    public void shouldHavePositiveCapacityForOpen() throws Exception {
        InputStream csvStream = new ByteArrayInputStream(constructCSVString().getBytes());
        MockMultipartFile file = new MockMultipartFile("file", csvStream);

        mvc.perform(MockMvcRequestBuilders.multipart("/api/slots/v2/upload").file(file)
            .header("X-Fulfillment-User-Token", user.getToken())
            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()))
            .andExpect(status().is2xxSuccessful());

        transactionHelper.withNewTransaction(() -> {
            List<Slot> slots = slotRepository.findAll();
            Assert.assertTrue(slots.stream()
                .allMatch( s -> s.getShopperCount() == 1 && s.getDriverCount() == 1 ));
        });
    }

    @Test
    public void shouldHaveNegativeCapacityForNotOpen() throws Exception {
        InputStream csvStream = new ByteArrayInputStream(constructCSVString(LocalDate.of(2020, 6, 14), false).getBytes());
        MockMultipartFile file = new MockMultipartFile("file", csvStream);

        mvc.perform(MockMvcRequestBuilders.multipart("/api/slots/v2/upload").file(file)
            .header("X-Fulfillment-User-Token", user.getToken())
            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()))
            .andExpect(status().is2xxSuccessful());

        transactionHelper.withNewTransaction(() -> {
            List<Slot> slots = slotRepository.findAll();
            Assert.assertTrue(slots.stream()
                .allMatch( s -> s.getShopperCount() == -1 && s.getDriverCount() == -1 ));
        });
    }

    @Test
    public void shouldValidateNonOverlappingSlotsOnStore() throws Exception {
        LocalDateTime startA = LocalDateTime.of(2020, 6, 14, 12, 0);
        LocalDateTime startB = LocalDateTime.of(2020, 6, 14, 15, 0);
        String csvString = constructCSVString(startA, startB, true);
        InputStream csvStream = new ByteArrayInputStream(csvString.getBytes());
        MockMultipartFile file = new MockMultipartFile("file", csvStream);

        mvc.perform(MockMvcRequestBuilders.multipart("/api/slots/v2/upload").file(file)
            .header("X-Fulfillment-User-Token", user.getToken())
            .header("X-Fulfillment-Tenant-Token", user.getTenant().getToken()))
            .andDo(print())
            .andExpect(status().is4xxClientError());
    }

    private String constructCSVString() {
        return constructCSVString(LocalDate.of(2020, 6, 14), true);
    }

    private String constructCSVString(LocalDate date, boolean open) {
        return constructCSVString(date.atTime(12, 0), date.atTime(16, 0), open);
    }

    private String constructCSVString(LocalDateTime dateTimeA, LocalDateTime dateTimeB, boolean open) {
        String startDateStringA = dateTimeA.format(formatter);
        String endDateStringA = dateTimeA.plusHours(4).format(formatter);
        String startDateStringB = dateTimeB.format(formatter);
        String endDateStringB = dateTimeB.plusHours(4).format(formatter);
        return "Store ID,Start,End,Open\n" +
            String.format("118,%s,%s,%b\n", startDateStringA, endDateStringA, open) +
            String.format("118,%s,%s,%b\n", startDateStringB, endDateStringB, open) +
            String.format("1,%s,%s,%b\n", startDateStringA, endDateStringA, open) +
            String.format("1,%s,%s,%b\n", startDateStringB, endDateStringB, open);
    }
}
