package com.happyfresh.fulfillment.shipment.service;

import com.happyfresh.fulfillment.batch.service.BatchAvailabilityService;
import com.happyfresh.fulfillment.common.util.AutowireUtil;
import com.happyfresh.fulfillment.entity.Shipment;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.persistence.PostPersist;
import javax.persistence.PostUpdate;

@Component
@Scope("prototype")
public class ShipmentEntityListener {

    @PostPersist
    @PostUpdate
    public void afterUpdate(Shipment shipment) {
        invalidateBatchesAvailableCaches(shipment);
    }

    private void invalidateBatchesAvailableCaches(Shipment shipment) {
        if (shipment.getSlot() != null) {
            BatchAvailabilityService batchAvailabilityService = AutowireUtil.getBean(BatchAvailabilityService.class);
            batchAvailabilityService.invalidateShoppingAndDeliveryBatchesAvailable(shipment.getSlot().getStockLocation());
        }
    }
}
