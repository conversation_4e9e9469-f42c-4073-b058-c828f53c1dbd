package com.happyfresh.fulfillment.shipment.service;

import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.repository.ItemRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ItemService {

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private CategoryOrderService categoryOrderService;

    @Autowired
    private ItemRepository itemRepository;

    @Transactional
    public Item save(Shipment shipment, StockLocation stockLocation, Item item, String currency, boolean isFinalizing) {

        if (!isFinalizing || item.isReplacement()) {
            Integer position = item.getCategory().getPosition();
            Category category = categoryService.save(item.getCategory());
            CategoryOrder categoryOrder = categoryOrderService.save(category, stockLocation, position);
            item.setCategory(category);
            item.setCategoryOrder(categoryOrder);
            item.setCurrency(currency);

            if (shipment != null) {
                item.setShipment(shipment);
            }
        }

        return itemRepository.save(item);
    }

    @Transactional
    public Item save(StockLocation stockLocation, Item item, String currency) {
        return save(null, stockLocation, item, currency, true);
    }
}
