package com.happyfresh.fulfillment.shipment.service;

import com.happyfresh.fulfillment.common.util.WebRequestLogger;
import com.happyfresh.fulfillment.entity.Item;
import com.happyfresh.fulfillment.entity.ItemReplacementPreference;
import com.happyfresh.fulfillment.repository.ItemReplacementPreferenceRepository;
import com.happyfresh.fulfillment.shipment.form.ItemReplacementPreferenceForm;
import com.happyfresh.fulfillment.shipment.form.ReplacementVariantForm;
import com.happyfresh.fulfillment.shipment.mapper.ItemReplacementPreferenceMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ItemReplacementPreferenceService {

    private static final Logger logger = LoggerFactory.getLogger(ItemReplacementPreferenceService.class);

    @Autowired
    private ItemReplacementPreferenceMapper replacementPreferenceMapper;

    @Autowired
    private ItemReplacementPreferenceRepository replacementPreferenceRepository;

    @Autowired
    private ApplicationContext applicationContext;

    @Transactional
    public ItemReplacementPreference createOrUpdateBySku(Item item, ItemReplacementPreferenceForm preferenceForm) {
        ReplacementVariantForm variantForm = preferenceForm.getReplacementVariant();
        ItemReplacementPreference preference;
        if (variantForm != null) {
            ItemReplacementPreference existingPreference = getItemReplacementPreference(item, variantForm);
            if (existingPreference != null) {
                preference = updateReplacementPreference(item, variantForm, existingPreference);
            } else {
                // clear existing, remove this to allow multiple item replacement preferences
                removeExistingReplacementPreferences(item);
                preference = createReplacementPreference(item, variantForm);
            }
            if (anyMissingPriceField(variantForm)) {
                warnMissingPriceField(item);
            }
            return preference;
        } else {
            // Handle remove replacement variant
            removeExistingReplacementPreferences(item);
            return null;
        }
    }

    private ItemReplacementPreference getItemReplacementPreference(Item item, ReplacementVariantForm variantForm) {
        return item.getReplacementPreferences().stream()
            .collect(Collectors.toMap(ItemReplacementPreference::getSku, Function.identity()))
            .get(variantForm.getSku());
    }

    private ItemReplacementPreference createReplacementPreference(Item item, ReplacementVariantForm variantForm) {
        ItemReplacementPreference preference = replacementPreferenceMapper.fromVariantForm(variantForm);
        preference.setCurrency(item.getCurrency());
        return preference;
    }

    private ItemReplacementPreference updateReplacementPreference(Item item, ReplacementVariantForm variantForm, ItemReplacementPreference existingPreference) {
        replacementPreferenceMapper.updateFromVariantForm(variantForm, existingPreference);
        existingPreference.setCurrency(item.getCurrency());
        return existingPreference;
    }

    private void removeExistingReplacementPreferences(Item item) {
        Set<ItemReplacementPreference> existingPreferences = item.getReplacementPreferences();
        if (!existingPreferences.isEmpty()) {
            item.clearReplacementPreferences();
            replacementPreferenceRepository.deleteAll(existingPreferences);
        }
    }

    private boolean anyMissingPriceField(ReplacementVariantForm variantForm) {
        return variantForm.getPrice() == null ||
            variantForm.getCostPrice() == null ||
            variantForm.getNormalPrice() == null ||
            variantForm.getUnitPrice() == null ||
            variantForm.getNormalCostPrice() == null ||
            variantForm.getSupermarketUnitCostPrice() == null;
    }

    private void warnMissingPriceField(Item item) {
        String sku = item.getSku();
        String message = String.format("Missing replacement preference price field for item %s", sku);
        try {
            WebRequestLogger webRequestLogger = applicationContext.getBean(WebRequestLogger.class);
            webRequestLogger.warn(logger, message);
        } catch (Exception ex) {
            logger.warn(message);
        }
    }
}
