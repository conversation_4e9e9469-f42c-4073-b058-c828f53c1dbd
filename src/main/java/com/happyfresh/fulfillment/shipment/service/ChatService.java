package com.happyfresh.fulfillment.shipment.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.ImmutableMap;
import com.happyfresh.fulfillment.common.exception.type.DisableChatException;
import com.happyfresh.fulfillment.common.exception.type.InvalidChatActionException;
import com.happyfresh.fulfillment.common.service.CoralogixAPIService;
import com.happyfresh.fulfillment.common.util.LocaleUtil;
import com.happyfresh.fulfillment.entity.Chat;
import com.happyfresh.fulfillment.entity.Country;
import com.happyfresh.fulfillment.entity.Shipment;
import com.happyfresh.fulfillment.repository.ChatRepository;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import com.happyfresh.fulfillment.shipment.form.ChatForm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityNotFoundException;
import java.util.Locale;

@Service
public class ChatService {
    private final Logger logger = LoggerFactory.getLogger(ChatService.class);

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private ChatRepository chatRepository;

    @Autowired
    private CoralogixAPIService coralogixAPIService;

    @Transactional
    public Shipment openChat(String shipmentNumber, ChatForm chatForm) {
        Shipment shipment = findByNumber(shipmentNumber);

        boolean enableChat = shipment.getSlot().getStockLocation().isEnableChat();
        if (!enableChat)
            throw new DisableChatException();

        Chat chat = shipment.getChat();
        if (chat == null) {
            chat = new Chat();
            chat.setShipment(shipment);
        }

        if (!chat.isUpdateable()) {
            throw new InvalidChatActionException();
        }

        chat.setState(Chat.State.ACTIVE);
        chat.setCurrentJob(chatForm.getCurrentJob());
        chat.setChannelUrl(chatForm.getChannelUrl());
        chatRepository.save(chat);

        shipment.setChat(chat);
        shipmentRepository.save(shipment);

        Country shopperCountry = shipment.getSlot().getStockLocation().getState().getCountry();
        String customerLanguage = shipment.getOrderCustomerLanguage() != null ? shipment.getOrderCustomerLanguage() : "en";
        Locale shopperLocale = LocaleUtil.findLocaleByCountry(shopperCountry.getIsoName());
        Locale customerLocale = LocaleUtil.findLocaleByLanguage(customerLanguage);
        boolean isChatTranslated = shopperCountry.isEnableChatTranslation() && shopperLocale != customerLocale;

        // Tracking
        ImmutableMap properties = ImmutableMap.builder().put("is_chat_translated", isChatTranslated).build();
        try {
            coralogixAPIService.sendLog(CoralogixAPIService.LogSeverity.INFO, null, "Chat Initiated", getClass().getSimpleName(),
                    "openChat", properties);
        } catch (JsonProcessingException e) {
            logger.error("Tracking error", e);
        }

        return shipment;
    }

    @Transactional
    public Shipment closeChat(String shipmentNumber) {
        Shipment shipment = findByNumber(shipmentNumber);

        Chat chat = shipment.getChat();
        if (chat == null)
            throw new EntityNotFoundException();

        chat.setState(Chat.State.CLOSED);
        chatRepository.save(chat);
        trackChat();
        return shipment;
    }

    @Transactional
    public Shipment closeChatWithForm(String shipmentNumber, ChatForm chatForm) {
        Shipment shipment = findByNumber(shipmentNumber);

        Chat chat = shipment.getChat();
        if (chat == null)
            throw new EntityNotFoundException();

        chat.setCurrentJob(chatForm.getCurrentJob());
        chat.setState(Chat.State.CLOSED);
        chatRepository.save(chat);
        trackChat();
        return shipment;
    }

    private void trackChat() {
        // Segment Tracking
        ImmutableMap properties = ImmutableMap.builder().build();
        try {
            coralogixAPIService.sendLog(CoralogixAPIService.LogSeverity.INFO, null, "Chat Session Ended", getClass().getSimpleName(),
                    "trackChat", properties);
        } catch (JsonProcessingException e) {
            logger.error("Tracking error", e);
        }
    }

    private Shipment findByNumber(String number) {
        Shipment shipment = shipmentRepository.findByNumber(number);

        if (shipment == null)
            throw new EntityNotFoundException();

        return shipment;
    }
}
