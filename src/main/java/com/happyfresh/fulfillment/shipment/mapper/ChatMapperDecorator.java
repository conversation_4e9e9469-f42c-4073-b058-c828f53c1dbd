package com.happyfresh.fulfillment.shipment.mapper;

import com.happyfresh.fulfillment.entity.Chat;
import com.happyfresh.fulfillment.entity.Country;
import com.happyfresh.fulfillment.shipment.presenter.ChatPresenter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

public abstract class ChatMapperDecorator implements ChatMapper {

    @Autowired
    @Qualifier("delegate")
    private ChatMapper delegate;

    @Override
    public ChatPresenter chatToChatPresenter(Chat chat) {
        ChatPresenter presenter = delegate.chatToChatPresenter(chat);

        if (presenter != null) {
            Country shopperCountry = chat.getShipment().getSlot().getStockLocation().getState().getCountry();

            presenter.setShopperLanguage(shopperCountry.getLanguageCode());
            presenter.setCurrentJob(chat.getCurrentJob());
        }

        return presenter;
    }

}
