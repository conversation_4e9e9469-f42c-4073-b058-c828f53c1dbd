package com.happyfresh.fulfillment.shipment.mapper;

import com.happyfresh.fulfillment.common.util.CurrencyUtil;
import com.happyfresh.fulfillment.entity.Item;
import com.happyfresh.fulfillment.entity.Payment;
import com.happyfresh.fulfillment.shipment.presenter.PaymentPresenter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.math.BigDecimal;
import java.util.List;

public abstract class PaymentMapperDecorator implements PaymentMapper {

    @Autowired
    @Qualifier("delegate")
    private PaymentMapper delegate;

    @Autowired
    private PaymentOptionMapper paymentOptionMapper;

    @Override
    public PaymentPresenter toPaymentPresenter(Payment payment) {
        PaymentPresenter presenter = delegate.toPaymentPresenter(payment);
        if (presenter != null) {
            BigDecimal total = getTotal(payment);
            presenter.setTotal(total);
            presenter.setPaymentOptions(paymentOptionMapper.toPaymentOptionsPresenters(payment.getPaymentOptions()));
            List<Item> items = payment.getShipment().getItems();
            if (!items.isEmpty()) {
                String currency = payment.getShipment().getItems().get(0).getCurrency();
                presenter.setDisplayCash(CurrencyUtil.format(payment.getCash(), currency));
                presenter.setDisplayCashless(CurrencyUtil.format(payment.getCashless(), currency));
                presenter.setDisplayTotal(CurrencyUtil.format(total, currency));
            }
        }

        return presenter;
    }

    private BigDecimal getTotal(Payment payment) {
        if (payment.getCash() == null && payment.getCashless() == null)
            return payment.getShipment().getOrderTotal();
        else
            return payment.getCash().add(payment.getCashless());
    }
}
