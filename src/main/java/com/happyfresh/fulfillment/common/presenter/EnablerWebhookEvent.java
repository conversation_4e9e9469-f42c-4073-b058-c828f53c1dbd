package com.happyfresh.fulfillment.common.presenter;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.happyfresh.fulfillment.common.annotation.type.WebhookType;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class EnablerWebhookEvent extends EnablerEvent {

    public enum Status {
        ORDER_ITEM_PICKED, ORDER_ITEM_PACKED, // Valid status for Hypermart
        CREATED, CONFIRMED, SHIPPED, DELIVERED, CANCELLED;

        @Override
        public String toString() {
            return name().toLowerCase();
        }
    }

    private String eventType;

    private String status;

    private String payload;

    public EnablerWebhookEvent() {
        super();
        this.eventType = WebhookType.ENABLER_CALLBACK.toString();
    }

}
