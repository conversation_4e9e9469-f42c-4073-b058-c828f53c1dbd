package com.happyfresh.fulfillment.common.property;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "redis")
@Getter
@Setter
public class RedisProperty {

    private String namespace;

    private String password;

    private String url;

}
