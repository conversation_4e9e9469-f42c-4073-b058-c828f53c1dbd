package com.happyfresh.fulfillment.common.interceptor;

import com.happyfresh.fulfillment.common.util.ApplicationUtil;
import com.happyfresh.fulfillment.user.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.support.WebApplicationContextUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Order(Integer.MAX_VALUE-2)
public class AuthenticationFilter extends OncePerRequestFilter {

    private UserService userService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        if(userService == null) {
            WebApplicationContext webApplicationContext = WebApplicationContextUtils.getWebApplicationContext(request.getServletContext());
            userService = webApplicationContext.getBean(UserService.class);
        }

        final String tenantToken = request.getHeader(ApplicationUtil.X_FULFILLMENT_TENANT_TOKEN);
        RequestContextHolder.currentRequestAttributes().setAttribute(ApplicationUtil.X_FULFILLMENT_TENANT_TOKEN, tenantToken,  RequestAttributes.SCOPE_REQUEST);

        String locale = request.getHeader(ApplicationUtil.LOCALE);
        if (StringUtils.isEmpty(locale))
            locale = "en";
        RequestContextHolder.currentRequestAttributes().setAttribute(ApplicationUtil.LOCALE, locale,  RequestAttributes.SCOPE_REQUEST);

        String authorization = request.getHeader("Authorization");
        if (StringUtils.isEmpty(authorization)) {
            final String userToken = request.getHeader(ApplicationUtil.X_FULFILLMENT_USER_TOKEN);

            if (StringUtils.isNotEmpty(userToken) || StringUtils.isNotEmpty(tenantToken)) {
                AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(userToken, tenantToken);
                SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            }
        }

        filterChain.doFilter(request, response);
    }

}
