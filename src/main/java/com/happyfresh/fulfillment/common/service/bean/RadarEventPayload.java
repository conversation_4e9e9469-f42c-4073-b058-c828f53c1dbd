package com.happyfresh.fulfillment.common.service.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.happyfresh.fulfillment.common.util.DateTimeUtil;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class RadarEventPayload {
    public static final String RADAR_WAREHOUSE_TAG = "Warehouse";
    public static final String RADAR_SUPERMARKET_TAG = "Supermarket";
    public static final String RADAR_SHIPMENT_NUMBER_TAG = "shipment_number";

    public static final String RADAR_EVENT_ENTER_GEOFENCE = "user.entered_geofence";

    public static final String RADAR_EVENT_EXIT_GEOFENCE = "user.exited_geofence";

    @JsonProperty("_id")
    private String id;

    @JsonIgnore
    private LocalDateTime createdAt;

    @JsonProperty("createdAt")
    public String getCreatedAtString() {
        return DateTimeUtil.localDateTimeToSpreeStringDateTime(createdAt);
    }

    public void setCreatedAtString(String createdAtString) {
        if (createdAtString == null)
            createdAt = null;
        else
            createdAt = DateTimeUtil.spreeStringToLocalDateTime(createdAtString);
    }

    private Boolean live;

    private String type;

    private RadarLocation location;

    @Getter
    @Setter
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RadarLocation {
        private String type;

        private List<Double> coordinates;
    }

    private Long locationAccuracy;

    private Integer confidence;

    @JsonIgnore
    private LocalDateTime actualCreatedAt;

    @JsonProperty("actualCreatedAt")
    public String getActualCreatedAtString() {
        return DateTimeUtil.localDateTimeToSpreeStringDateTime(actualCreatedAt);
    }

    public void setActualCreatedAtString(String actualCreatedAtString) {
        if (actualCreatedAtString == null)
            actualCreatedAt = null;
        else
            actualCreatedAt = DateTimeUtil.spreeStringToLocalDateTime(actualCreatedAtString);
    }

    private RadarUserPayload user;

    private RadarGeofence geofence;

    @Getter
    @Setter
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RadarGeofence {
        @JsonProperty("_id")
        private String id;

        private String description;

        private String type;

        private Integer geometryRadius;

        private String tag;

        private String externalId;

        private GeometryCenter geometryCenter;
    }

    @Getter
    @Setter
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class GeometryCenter {
        private List<Double> coordinates;
        private String type;
    }

    public boolean trackToSegment(){
        return this.getType().equals(RADAR_EVENT_ENTER_GEOFENCE) ||
                this.getType().equals(RADAR_EVENT_EXIT_GEOFENCE);
    }

    public boolean isRadarWarehouseTag(){
        return this.getGeofence().getTag().equalsIgnoreCase(RadarEventPayload.RADAR_WAREHOUSE_TAG);
    }

    public boolean isSupermarketTag(){
        return this.getGeofence().getTag().equalsIgnoreCase(RadarEventPayload.RADAR_SUPERMARKET_TAG);
    }

    public boolean isShipmentNumberTag(){
        return this.getGeofence().getTag().equals(RadarEventPayload.RADAR_SHIPMENT_NUMBER_TAG);
    }
}
