package com.happyfresh.fulfillment.common.exception.type;

import com.happyfresh.fulfillment.common.exception.UnprocessableEntityException;

public class UserAlreadyExistException extends UnprocessableEntityException {

    private static final String DEFAULT_MESSAGE = "User already exist.";

    public UserAlreadyExistException() {
        super(DEFAULT_MESSAGE);
    }

    public UserAlreadyExistException(String message) {
        super(message);
    }
}
