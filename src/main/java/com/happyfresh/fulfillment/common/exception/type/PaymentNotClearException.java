package com.happyfresh.fulfillment.common.exception.type;

import com.happyfresh.fulfillment.common.exception.UnprocessableEntityException;

public class PaymentNotClearException extends UnprocessableEntityException {

    private static final String DEFAULT_MESSAGE = "Order payment is not clear.";

    public PaymentNotClearException() {
        super(DEFAULT_MESSAGE);
    }

    public PaymentNotClearException(String message) {
        super(message);
    }
}
