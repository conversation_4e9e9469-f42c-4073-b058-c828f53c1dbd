package com.happyfresh.fulfillment.common.exception.type;

import com.happyfresh.fulfillment.common.exception.UnprocessableEntityException;

public class InvalidReplacementItemException extends UnprocessableEntityException {

    private static final String DEFAULT_MESSAGE = "Replacement item is not valid.";

    public InvalidReplacementItemException() {
        super(DEFAULT_MESSAGE);
    }

    public InvalidReplacementItemException(String message) {
        super(message);
    }
}
