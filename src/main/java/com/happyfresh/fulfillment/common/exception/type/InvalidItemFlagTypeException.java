package com.happyfresh.fulfillment.common.exception.type;

import com.happyfresh.fulfillment.common.exception.BadRequestException;

public class InvalidItemFlagTypeException extends BadRequestException {

    private static final String DEFAULT_MESSAGE = "Flag type is invalid.";

    public InvalidItemFlagTypeException() {
        super(DEFAULT_MESSAGE);
    }

    public InvalidItemFlagTypeException(String message) {
        super(message);
    }
}
