package com.happyfresh.fulfillment.common.exception.type;

import com.happyfresh.fulfillment.common.exception.UnprocessableEntityException;

public class ClockInException extends UnprocessableEntityException {
    public static final String SHIFT_NOT_FOUND = "Shift is not found.";

    public static final String INVALID_SHIFT_TIME = "Shift has already ended.";

    public static final String INVALID_SHIFT_USER_ROLE = "Shift type does not match with user role.";
    
    public ClockInException(String message) {
        super(message);
    }
}
