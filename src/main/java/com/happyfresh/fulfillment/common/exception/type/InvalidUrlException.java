package com.happyfresh.fulfillment.common.exception.type;

import com.happyfresh.fulfillment.common.exception.UnprocessableEntityException;

public class InvalidUrlException extends UnprocessableEntityException {

    private static final String DEFAULT_MESSAGE = "Your requested url has been replaced or moved.";

    public InvalidUrlException() {
        super(DEFAULT_MESSAGE);
    }

    public InvalidUrlException(String message) {
        super(message);
    }
}
