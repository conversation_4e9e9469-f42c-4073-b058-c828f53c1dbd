package com.happyfresh.fulfillment.common.tracking.slot;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.happyfresh.fulfillment.common.tracking.CoralogixTracker;
import com.happyfresh.fulfillment.entity.Cluster;
import com.happyfresh.fulfillment.entity.Shipment;
import com.happyfresh.fulfillment.entity.StockLocation;
import com.happyfresh.fulfillment.slot.bean.SlotAvailabilityContext;
import com.happyfresh.fulfillment.slot.mappper.SlotAvailabilityContextMapper;
import com.happyfresh.fulfillment.slot.presenter.tracking.selection.SlotAvailabilityContextPayload;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationContext;

import java.time.LocalDateTime;
import java.time.ZoneOffset;

@Setter
@Getter
public class SlotAvailabilityRequestTracker extends CoralogixTracker {

    private String trackingId;
    private SlotAvailabilityContext context;

    private ApplicationContext applicationContext;
    private SlotAvailabilityContextMapper contextMapper;

    public SlotAvailabilityRequestTracker(ApplicationContext applicationContext, SlotAvailabilityContextMapper contextMapper) {
        super("Slot Availability Log - Request", applicationContext);
        this.contextMapper = contextMapper;
    }

    public String getTrackingId() {
        if (this.trackingId == null) {
            this.trackingId = "SA-" + System.currentTimeMillis();
            return this.trackingId;
        }
        return this.trackingId;
    }

    @Override
    protected String getSubsystemSuffix() {
        return null;
    }

    protected ImmutableMap<String, Object> buildProperties() {
        Shipment shipment = this.context.getShipment();
        StockLocation stockLocation = this.context.getStockLocation();
        Cluster cluster = stockLocation.getCluster();
        int cutoffMinutes = stockLocation.getOngoingSlotCutOff();

        ObjectMapper mapper = new ObjectMapper();
        ImmutableMap.Builder<String, Object> builder = ImmutableMap.builder();
        putOrEmpty(builder, "tracking_id", getTrackingId());
        putOrEmpty(builder, "timestamp", LocalDateTime.now().toEpochSecond(ZoneOffset.UTC));
        putOrEmpty(builder, "order_number", shipment.getOrderNumber());
        putOrEmpty(builder, "stock_location_name", stockLocation.getName());
        // Configurations:
        putOrEmpty(builder, "shopper_average_picking_time_per_uniq_item", stockLocation.getShopperAveragePickingTimePerUniqItem());
        putOrEmpty(builder, "shopper_queue_replacement_time", stockLocation.getShopperQueueReplacementTime());
        putOrEmpty(builder, "shopper_handover_to_driver_time", stockLocation.getShopperHandoverToDriverTime());
        putOrEmpty(builder, "delivery_time_per_shipment", cluster.deliveryTimePerShipment());
        putOrEmpty(builder, "max_delivery_handover", stockLocation.getMaxDeliveryHandover());
        putOrEmpty(builder, "max_delivery_volume", stockLocation.getMaxDeliveryVolume());
        putOrEmpty(builder, "max_shopping_volume", stockLocation.getMaxShoppingVolume());
        putOrEmpty(builder, "ongoing_slot_cutoff_minutes", cutoffMinutes);
        try {
            SlotAvailabilityContextPayload contextPayload = contextMapper.toSlotAvailabilityContextPayload(context);
            putOrEmpty(builder, "context", mapper.writeValueAsString(contextPayload)); // Convert to JSON
        } catch (Exception e) {
            logger.error(eventCategory + " Error when serialize SlotAvailabilityContext.", e);
        }
        return builder.build();
    }

}
