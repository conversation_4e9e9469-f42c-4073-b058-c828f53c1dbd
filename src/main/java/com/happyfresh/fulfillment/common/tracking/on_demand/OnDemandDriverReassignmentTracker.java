package com.happyfresh.fulfillment.common.tracking.on_demand;

import com.google.common.collect.ImmutableMap;
import com.happyfresh.fulfillment.common.tracking.CoralogixTrackerService;
import com.happyfresh.fulfillment.entity.*;
import lombok.Setter;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Setter
@Service
@Scope("prototype")
public class OnDemandDriverReassignmentTracker extends CoralogixTrackerService {

    private static final String EVENT_NAME = "On Demand Driver Reassignment Log";

    private String trackingId;
    private Shipment currentShipment;
    private Batch deliveryBatch;
    private StockLocation stockLocation;
    private Agent selectedAgent;
    private Agent replacedAgent;
    private LocalDateTime shoppingFinishedTime;
    private int totalAvailableFleet;

    @Override
    protected String getSubsystemSuffix() {
        return null;
    }

    @Override
    protected String getCategory() {
        return EVENT_NAME;
    }

    @Override
    protected ImmutableMap<String, Object> buildProperties() {
        ImmutableMap.Builder<String, Object> builder = ImmutableMap.builder();
        builder.put("tracking_id", trackingId);
        builder.put("order_number", currentShipment.getOrderNumber());
        builder.put("batch_id", deliveryBatch.getId());
        builder.put("store_id", stockLocation.getExternalId());
        builder.put("selected_user_id", selectedAgent.getUser().getId());
        builder.put("replaced_user_id", replacedAgent.getUser().getId());
        builder.put("selected_user_current_position_lat", selectedAgent.getLat());
        builder.put("selected_user_current_position_lon", selectedAgent.getLon());
        builder.put("delivery_address_lat", currentShipment.getLatLon().lat());
        builder.put("delivery_address_lon", currentShipment.getLatLon().lon());
        builder.put("on_demand_sla_config", stockLocation.getOnDemandDeliveryTime());
        builder.put("timestamp", LocalDateTime.now().toString());
        builder.put("shopping_finished_time", shoppingFinishedTime.toString());
        builder.put("total_available_fleet", totalAvailableFleet);

        return builder.build();
    }
}
