package com.happyfresh.fulfillment.common.validator;

import com.happyfresh.fulfillment.common.annotation.NotEquals;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class NotEqualsValidator implements ConstraintValidator<NotEquals, Double> {

    private Double constraintValue;

    @Override
    public void initialize(NotEquals constraintAnnotation) {
        this.constraintValue = constraintAnnotation.value();
    }

    @Override
    public boolean isValid(Double inputValue, ConstraintValidatorContext context) {
        return inputValue == null || !inputValue.equals(constraintValue);
    }
}
