package com.happyfresh.fulfillment.common.annotation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;
import com.happyfresh.fulfillment.common.validator.ReceiptTaxValidator;

@Documented
@Constraint(validatedBy = ReceiptTaxValidator.class) // marks an annotation as being a Bean Validation constraint
@Target({ ElementType.TYPE, ElementType.ANNOTATION_TYPE }) // is where our annotations can be used
@Retention(RetentionPolicy.RUNTIME) // how the marked annotation is stored
public @interface ReceiptTaxConstraint {

    String message() default "Invalid tax number";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};

}