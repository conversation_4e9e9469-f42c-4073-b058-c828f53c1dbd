package com.happyfresh.fulfillment.lezcash.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.lezcash.model.LezCashPayload;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;

public class LezCashHeaderGenerator {
    private static final String X_API_KEY = "X-API-KEY";

    private static final String X_TIMESTAMP = "X-TIMESTAMP";

    private static final String X_SIGNATURE = "X-SIGNATURE";


    public static HttpHeaders getHeadersWithPayload(String apiKey, LezCashPayload body, Date date, String algorithm) throws NoSuchAlgorithmException, JsonProcessingException {
        String requestBodyAsJson = new ObjectMapper().writeValueAsString(body);
        Map<String, String> headers = getCustomHeaders(apiKey, requestBodyAsJson, date, algorithm);
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);
        for (String key : headers.keySet()) {
            requestHeaders.add(key, headers.get(key));
        }
        return requestHeaders;
    }

    public static Map<String,String> getCustomHeaders(String apiKey, String body, Date date, String algorithm) throws NoSuchAlgorithmException {
        Map<String, String> headers = new HashMap<>();
        String timestamp = null;
        if (date != null) {
            timestamp = getTimestampJakarta(date);
            headers.put(X_TIMESTAMP, timestamp);
        }

        if (apiKey != null) {
            headers.put(X_API_KEY, apiKey);
        }

        if (apiKey != null && body != null) {
            headers.put(X_SIGNATURE, hash(apiKey + "." + timestamp + "." + body, algorithm));
        }

        return headers;
    }

    public static String getTimestampJakarta(Date date) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss+07:00");
        TimeZone timezone = TimeZone.getTimeZone("Asia/Jakarta");
        dateFormat.setTimeZone(timezone);
        return dateFormat.format(date);
    }

    public static String hash(String text, String algorithm) throws NoSuchAlgorithmException {
        return toHexString(getSHA(text, algorithm));
    }

    public static byte[] getSHA(String input, String algorithm) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance(algorithm);
        return md.digest(input.getBytes(StandardCharsets.UTF_8));
    }

    public static String toHexString(byte[] hash) {
        BigInteger number = new BigInteger(1, hash);
        StringBuilder hexString = new StringBuilder(number.toString(16));
        while (hexString.length() < 64) {
            hexString.insert(0, '0');
        }
        return hexString.toString();
    }
}
