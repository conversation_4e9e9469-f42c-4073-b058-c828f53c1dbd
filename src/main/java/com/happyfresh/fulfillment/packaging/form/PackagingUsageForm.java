package com.happyfresh.fulfillment.packaging.form;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class PackagingUsageForm {

    @NotNull
    private String internalName;

    @NotNull
    private String name;

    @NotNull @Min(0)
    private Integer quantity;
}
