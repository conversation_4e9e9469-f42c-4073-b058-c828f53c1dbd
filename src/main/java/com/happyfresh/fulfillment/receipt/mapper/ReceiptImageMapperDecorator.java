package com.happyfresh.fulfillment.receipt.mapper;

import com.happyfresh.fulfillment.common.property.S3Property;
import com.happyfresh.fulfillment.entity.ReceiptImage;
import com.happyfresh.fulfillment.shipment.presenter.ReceiptImagePresenter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;


public abstract class ReceiptImageMapperDecorator implements ReceiptImageMapper{
    @Autowired
    @Qualifier("delegate")
    private ReceiptImageMapper delegate;

    @Autowired
    private S3Property s3Property;

    @Override
    public ReceiptImagePresenter receiptImageToReceiptImagePresenter(ReceiptImage receiptImage) {
        ReceiptImagePresenter presenter = delegate.receiptImageToReceiptImagePresenter(receiptImage);
        if (!s3Property.getCdnUrl().equalsIgnoreCase(S3Property.NO_CDN)) {
            String urlWithCdn = receiptImage.getUrl().replace(s3Property.getUrl() + "/" + s3Property.getBucketName(), s3Property.getCdnUrl());
            presenter.setUrl(urlWithCdn);
        }
        return presenter;
    }
}
