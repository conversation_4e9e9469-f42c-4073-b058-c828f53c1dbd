package com.happyfresh.fulfillment.tpl.delyva.model;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DelyvaDistance {
    private Unit unit;
    private double value;

    public void setUnit(String unit) {
        this.unit = Unit.valueOf(unit.toUpperCase());
    }

    public enum Unit {
        M, KM;

        @Override
        public String toString() {
            return name().toLowerCase();
        }
    }
}
