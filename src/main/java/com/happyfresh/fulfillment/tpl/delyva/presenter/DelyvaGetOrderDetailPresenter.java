package com.happyfresh.fulfillment.tpl.delyva.presenter;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.happyfresh.fulfillment.tpl.delyva.model.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DelyvaGetOrderDetailPresenter {

    private DelyvaOrderDetail data;
    private DelyvaError.DelyvaErrorDetail error;

}
