package com.happyfresh.fulfillment.admin.controller;

import com.happyfresh.fulfillment.admin.form.*;
import com.happyfresh.fulfillment.admin.mapper.*;
import com.happyfresh.fulfillment.admin.presenter.*;
import com.happyfresh.fulfillment.admin.services.DDFService;
import com.happyfresh.fulfillment.admin.services.PingService;
import com.happyfresh.fulfillment.batch.controller.BatchV3Controller;
import com.happyfresh.fulfillment.batch.service.BatchSndService;
import com.happyfresh.fulfillment.common.annotation.Publish;
import com.happyfresh.fulfillment.common.annotation.RequestWrapper;
import com.happyfresh.fulfillment.common.annotation.ResponseWrapper;
import com.happyfresh.fulfillment.common.annotation.type.WebhookType;
import com.happyfresh.fulfillment.common.exception.BadRequestException;
import com.happyfresh.fulfillment.common.exception.UnprocessableEntityException;
import com.happyfresh.fulfillment.common.exception.type.RedisLockTimeoutException;
import com.happyfresh.fulfillment.common.service.JedisLockService;
import com.happyfresh.fulfillment.enabler.service.JubelioService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.entity.ddf.DDFMatrix;
import com.happyfresh.fulfillment.repository.*;
import com.happyfresh.fulfillment.shift.service.ShiftService;
import com.happyfresh.fulfillment.shipment.mapper.ShipmentMapper;
import com.happyfresh.fulfillment.shipment.presenter.ShipmentPresenter;
import com.happyfresh.fulfillment.shipment.service.ShipmentService;
import com.happyfresh.fulfillment.slot.mappper.SlotMapper;
import com.happyfresh.fulfillment.slot.service.SlotService;
import com.happyfresh.fulfillment.stockLocation.mapper.ClusterMapper;
import com.happyfresh.fulfillment.stockLocation.mapper.CountryMapper;
import com.happyfresh.fulfillment.stockLocation.mapper.OnDemandClusterMapper;
import com.happyfresh.fulfillment.stockLocation.mapper.StockLocationMapper;
import com.happyfresh.fulfillment.stockLocation.presenter.ClusterPresenter;
import com.happyfresh.fulfillment.stockLocation.presenter.CountryPresenter;
import com.happyfresh.fulfillment.stockLocation.presenter.OnDemandClusterPresenter;
import com.happyfresh.fulfillment.stockLocation.presenter.StockLocationPresenter;
import com.happyfresh.fulfillment.stockLocation.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/admin")
@Validated
public class AdminController {

    @Autowired
    private StockLocationRepository stockLocationRepository;

    @Autowired
    private CountryRepository countryRepository;

    @Autowired
    private ClusterRepository clusterRepository;

    @Autowired
    private SlotRepository slotRepository;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private StockLocationMapper stockLocationMapper;

    @Autowired
    private CountryMapper countryMapper;

    @Autowired
    private ClusterMapper clusterMapper;

    @Autowired
    private SlotMapper slotMapper;

    @Autowired
    private StockLocationService stockLocationService;

    @Autowired
    private StockLocationMassUpdateService stockLocationMassUpdateService;

    @Autowired
    private CountryService countryService;

    @Autowired
    private ClusterService clusterService;

    @Autowired
    private StateRepository stateRepo;

    @Autowired
    private StateService stateService;

    @Autowired
    private StatePageMapper statePageMapper;

    @Autowired
    private SupplierService supplierService;

    @Autowired
    private SupplierPageMapper supplierPageMapper;

    @Autowired
    private DDFMatrixRepository ddfMatrixRepository;

    @Autowired
    private DDFMatrixMapper ddfMatrixMapper;

    @Autowired
    private DDFService ddfService;

    @Autowired
    private SlotService slotService;

    @Autowired
    private ShiftService shiftService;

    @Autowired
    private StockLocationPageMapper stockLocationPageMapper;

    @Autowired
    private ClusterPageMapper clusterPageMapper;

    @Autowired
    private CountryPageMapper countryPageMapper;

    @Autowired
    private SlotPageMapper slotPageMapper;

    @Autowired
    private ShipmentMapper shipmentMapper;

    @Autowired
    private ShipmentPageMapper shipmentPageMapper;

    @Autowired
    private PingService pingService;

    @Autowired
    private BatchSndService batchSndService;

    @Autowired
    private JubelioService jubelioService;

    @Autowired
    private ShipmentService shipmentService;

    @Autowired
    private OnDemandClusterService onDemandClusterService;

    @Autowired
    private OnDemandClusterPageMapper onDemandClusterPageMapper;

    @Autowired
    private OnDemandClusterMapper onDemandClusterMapper;

    @Autowired
    private ApplicationContext applicationContext;

    @GetMapping(value = "/ping")
    public ResponseEntity ping() throws Exception {
        if (pingService.ping())
            return new ResponseEntity(Collections.singletonMap("status", "UP"), HttpStatus.OK);
        else {
            return new ResponseEntity(HttpStatus.SERVICE_UNAVAILABLE);
        }
    }

    @PreAuthorize("isAdminAuthenticated()")
    @GetMapping(value = "/stock_locations")
    public StockLocationPagePresenter listStockLocation(@RequestParam(name = "page", defaultValue = "1") int page,
                                                        @RequestParam(name = "size", defaultValue = "20") int size,
                                                        @RequestParam(required = false, name = "type") Optional<String> type,
                                                        @RequestParam(required = false, name = "country") Optional<String> countryIsoName,
                                                        @RequestParam(required = false, name = "supplier_id") Optional<Long> supplierId) throws Exception {
        Page<StockLocation> stockLocations = stockLocationService.findAll(type, countryIsoName, supplierId, page, size, Sort.Direction.ASC, "name");
        return stockLocationPageMapper.stockLocationPageToStockLocationPagePresenter(page, stockLocations);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @GetMapping(value = "/stock_locations/{id}")
    @ResponseWrapper(rootName = "stock_location")
    public StockLocationPresenter showStockLocation(@PathVariable Long id) throws Exception {
        StockLocation stockLocation = stockLocationRepository.getOne(id);
        return stockLocationMapper.stockLocationToStockLocationPresenter(stockLocation);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PutMapping(value = "/stock_locations/{id}")
    @ResponseWrapper(rootName = "stock_location")
    public StockLocationPresenter updateStockLocation(@PathVariable Long id,
                                                      @Valid @RequestBody AdminStockLocationForm stockLocationForm) throws Exception {
        StockLocation stockLocation = stockLocationService.update(id, stockLocationForm);
        return stockLocationMapper.stockLocationToStockLocationPresenter(stockLocation);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PostMapping(value = "/stock_locations/mass_update")
    @ResponseWrapper(ignore = true)
    public void bulkUpdateStockLocation(@RequestParam("file") MultipartFile file, HttpServletResponse response) throws Exception {
        StockLocationMassUpdateFormWrapper formWrapper;
        try {
            formWrapper = stockLocationMassUpdateService.parseCsvFile(file);
        } catch (Exception e) {
            throw new BadRequestException("Failed to parse CSV. " + e.getMessage());
        }

        formWrapper.validateAll();
        stockLocationMassUpdateService.update(formWrapper);

        byte[] bytes = stockLocationMassUpdateService.toResultCsv(formWrapper);
        long timestamp = LocalDateTime.now().atZone(ZoneOffset.UTC).toEpochSecond();
        String filename = "stock_location_mass_update_result_" + timestamp + ".csv";
        response.setStatus(formWrapper.isAllValid() ? 200 : 207);
        response.setContentType("text/csv");
        response.addHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename);
        ServletOutputStream outputStream = response.getOutputStream();
        outputStream.write(bytes);
        outputStream.flush();
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PutMapping(value = "/stock_locations/{id}/express_configurations")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @ResponseWrapper(ignore = true)
    public void updateExpressConfiguration(
            @PathVariable Long id,
            @Valid @RequestBody AdminStockLocationExpressConfigurationForm expressForm
    ) {
        stockLocationService.updateExpressConfiguration(id, expressForm);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @GetMapping(value = "/stock_locations/{id}/express_configurations")
    @ResponseWrapper(rootName = "express_configurations")
    public AdminExpressConfigurationPresenter getExpressConfiguration(@PathVariable Long id) {
        return stockLocationService.getExpressConfigurations(id);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @GetMapping(value = "/states")
    public StatePagePresenter listState(@RequestParam(name = "page", defaultValue = "1") int page,
                                        @RequestParam(name = "size", defaultValue = "20") int size) throws Exception {
        Page<State> states = stateService.paginate(page, size, Sort.Direction.ASC, "name");
        return statePageMapper.statePageToStatePagePresenter(page, states);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @GetMapping(value = "/suppliers")
    public SupplierPagePresenter listSupplier(@RequestParam(name = "page", defaultValue = "1") int page,
                                              @RequestParam(name = "size", defaultValue = "20") int size) throws Exception {
        Page<Supplier> supplier = supplierService.paginate(page, size, Sort.Direction.ASC, "name");
        return supplierPageMapper.supplierPageToSupplierPagePresenter(page, supplier);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @GetMapping(value = "/clusters")
    public ClusterPagePresenter listCluster(@RequestParam(name = "page", defaultValue = "1") int page,
                                            @RequestParam(name = "size", defaultValue = "20") int size,
                                            @RequestParam(name = "country", required = false) String countryIsoName) throws Exception {
        Page<Cluster> clusters = null;
        if (countryIsoName != null)
            clusters = clusterService.paginateByCountryIso(page, size, Sort.Direction.ASC, "name", countryIsoName);
        else
            clusters = clusterService.paginate(page, size, Sort.Direction.ASC, "name");
        return clusterPageMapper.clusterPageToClusterPagePresenter(page, clusters);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @GetMapping(value = "/clusters/{id}")
    @ResponseWrapper(rootName = "cluster")
    public ClusterPresenter showCluster(@PathVariable Long id) throws Exception {
        Cluster cluster = clusterRepository.getOne(id);
        return clusterMapper.clusterToClusterPresenter(cluster);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PutMapping(value = "/clusters/{id}")
    @ResponseWrapper(rootName = "cluster")
    public ClusterPresenter updateCluster(@PathVariable Long id,
                                          @RequestBody AdminClusterForm clusterForm) throws Exception {
        Cluster cluster = clusterService.update(id, clusterForm);
        return clusterMapper.clusterToClusterPresenter(cluster);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @GetMapping(value = "/on_demand_clusters")
    public OnDemandClusterPagePresenter listOnDemandClusters(@RequestParam(name = "page", defaultValue = "1") int page,
                                            @RequestParam(name = "size", defaultValue = "20") int size,
                                            @RequestParam(name = "country", required = false) String countryIsoName) throws Exception {
        Page<OnDemandCluster> onDemandClusters = null;
        if (countryIsoName != null)
            onDemandClusters = onDemandClusterService.paginateByCountryIso(page, size, Sort.Direction.ASC, "name", countryIsoName);
        else
            onDemandClusters = onDemandClusterService.paginate(page, size, Sort.Direction.ASC, "name");
        return onDemandClusterPageMapper.onDemandClusterPageToOnDemandClusterPagePresenter(page, onDemandClusters);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PostMapping(value = "/on_demand_clusters")
    @ResponseWrapper(rootName = "on_demand_cluster")
    public OnDemandClusterPresenter createOnDemandCluster(@RequestBody AdminOnDemandClusterForm form) throws Exception {
        OnDemandCluster onDemandCluster = onDemandClusterService.createOrUpdate(form);
        return onDemandClusterMapper.onDemandClusterToOnDemandClusterPresenter(onDemandCluster);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PutMapping(value = "/on_demand_clusters/{id}")
    @ResponseWrapper(rootName = "on_demand_cluster")
    public OnDemandClusterPresenter updateOnDemandCluster(@PathVariable Long id, @RequestBody AdminOnDemandClusterForm form) throws Exception {
        form.setId(id);
        OnDemandCluster onDemandCluster = onDemandClusterService.createOrUpdate(form);
        return onDemandClusterMapper.onDemandClusterToOnDemandClusterPresenter(onDemandCluster);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @GetMapping(value = "/on_demand_clusters/{id}")
    @ResponseWrapper(rootName = "on_demand_cluster")
    public OnDemandClusterPresenter showOnDemandCluster(@PathVariable Long id) throws Exception {
        OnDemandCluster onDemandCluster = onDemandClusterService.getOne(id);
        return onDemandClusterMapper.onDemandClusterToOnDemandClusterPresenter(onDemandCluster);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @GetMapping(value = "/countries")
    public CountryPagePresenter listCountry(@RequestParam(value = "page", defaultValue = "1") Integer page,
                                            @RequestParam(value = "size", defaultValue = "10") Integer size) throws Exception {
        Page<Country> country = countryService.paginate(page, size, Sort.Direction.ASC, "name");
        return countryPageMapper.countryPageToCountryPagePresenter(page, country);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @GetMapping(value = "/countries/{isoName}")
    @ResponseWrapper(rootName = "country")
    public CountryPresenter showCountry(@PathVariable String isoName) throws Exception {
        Country country = countryRepository.findByIsoName(isoName);
        return countryMapper.countryToCountryPresenter(country);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PutMapping(value = "/countries/{isoName}")
    @ResponseWrapper(rootName = "country")
    public CountryPresenter updateCountry(@PathVariable String isoName,
                                          @Valid @RequestBody AdminCountryForm countryForm) throws Exception {
        Country country = countryService.update(isoName, countryForm);
        return countryMapper.countryToCountryPresenter(country);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @GetMapping(value = "/stock_locations/{id}/ddf")
    @ResponseWrapper(rootName = "ddf")
    public List<DDFMatrixPresenter> showDDF(@PathVariable Long id) throws Exception {
        StockLocation stockLocation = stockLocationRepository.getOne(id);
        List<DDFMatrix> ddfMatrices = ddfMatrixRepository.findAllByStockLocationOrderByIdAsc(stockLocation);
        return ddfMatrixMapper.toDDFMatrixPresenters(ddfMatrices);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PutMapping(value = "/stock_locations/{id}/ddf")
    @ResponseWrapper(rootName = "ddf")
    @RequestWrapper(rootName = "ddf")
    public List<DDFMatrixPresenter> updateDDF(@PathVariable Long id,
                                              @RequestBody List<AdminDDFMatrixForm> adminDDFMatrixFormList) throws Exception {
        return ddfMatrixMapper.toDDFMatrixPresenters(ddfService.update(id, adminDDFMatrixFormList));

    }

    @PreAuthorize("isAdminAuthenticated()")
    @GetMapping(value = "/slots")
    public SlotPagePresenter listSlot(@RequestParam("page") int page, @RequestParam("size") int size) throws Exception {
        Page<Slot> slot = slotService.paginate(page, size, Sort.Direction.DESC, "id");
        return slotPageMapper.slotPageToSlotPagePresenter(page, slot);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @GetMapping(value = "/slots/{id}")
    @ResponseWrapper(rootName = "slot")
    public AdminSlotPresenter showSlot(@PathVariable Long id) throws Exception {
        Slot slot = slotRepository.getOne(id);
        return slotMapper.toAdminSlotPresenter(slot);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PutMapping(value = "/slots/{id}")
    @ResponseWrapper(rootName = "slot")
    public AdminSlotPresenter updateSlot(@PathVariable Long id,
                                         @RequestBody AdminSlotForm slotForm) throws Exception {
        Slot slot = slotService.update(id, slotForm);
        return slotMapper.toAdminSlotPresenter(slot);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @DeleteMapping(value = "/slots")
    public ResponseEntity<Map<String, String>> deleteSlot(@RequestBody @Valid AdminDeleteSlotForm form) {
        try {
            slotService.delete(form.getSlotIds());
        } catch (UnprocessableEntityException e) {
            return new ResponseEntity<>(Collections.singletonMap("message", e.getMessage()), HttpStatus.UNPROCESSABLE_ENTITY);
        }
        return new ResponseEntity<>(Collections.singletonMap("message", "ok"), HttpStatus.OK);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PutMapping(value = "/slots/lds")
    public ResponseEntity<Map<String, String>> updateLDSSlots(@RequestBody @Valid AdminBatchUpdateLDSSlotForm form) {
        try {
            slotService.updateLDSSlots(form);
        } catch (UnprocessableEntityException e) {
            return new ResponseEntity<>(Collections.singletonMap("message", e.getMessage()), HttpStatus.UNPROCESSABLE_ENTITY);
        }
        return new ResponseEntity<>(Collections.singletonMap("message", "ok"), HttpStatus.OK);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @GetMapping(value = "/shifts")
    public ShiftPagePresenter getAllShifts(@RequestParam("store_external_id") @NotNull Long storeExternalId,
                                          @RequestParam(name = "date", required = false) @DateTimeFormat(pattern="yyyy-MM-dd") LocalDate date,
                                          @RequestParam(defaultValue = "1") @Min(1) int page,
                                          @RequestParam(name = "size", defaultValue = "20") int size) {
        if (date == null)
            date = LocalDate.now();
        return shiftService.getAllShiftInClusterPaginated(storeExternalId, date, page, size, Sort.Direction.DESC, "id");
    }

    @PreAuthorize("isAdminAuthenticated()")
    @DeleteMapping(value = "/shifts")
    public ResponseEntity<Map<String, String>> deleteShifts(@RequestBody @Valid AdminDeleteShiftForm form) {
        try {
            shiftService.delete(form.getShiftIds());
        } catch (UnprocessableEntityException e) {
            return new ResponseEntity<>(Collections.singletonMap("message", e.getMessage()), HttpStatus.UNPROCESSABLE_ENTITY);
        }
        return new ResponseEntity<>(Collections.singletonMap("message", "ok"), HttpStatus.OK);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PutMapping(value = "/shifts")
    public ResponseEntity<Map<String, String>> updateShifts(@RequestBody @Valid AdminBatchUpdateShiftForm form) {
        try {
            shiftService.update(form);
        } catch (UnprocessableEntityException e) {
            return new ResponseEntity<>(Collections.singletonMap("message", e.getMessage()), HttpStatus.UNPROCESSABLE_ENTITY);
        }
        return new ResponseEntity<>(Collections.singletonMap("message", "ok"), HttpStatus.OK);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @GetMapping(value = "/cluster_slots")
    public ClusterSlotPagePresenter listSlotsInOneClusterPerDay(@RequestParam("store_external_id") @NotNull Long storeExternalId,
                                                                @RequestParam(name = "date", required = false) @DateTimeFormat(pattern="yyyy-MM-dd") LocalDate date,
                                                                @RequestParam(name = "show_shipment_count", required = false) Boolean showShipmentCount,
                                                                @RequestParam(defaultValue = "1") @Min(1) int page,
                                                                @RequestParam(name = "size", defaultValue = "20") int size) {
        if (date == null)
            date = LocalDate.now();

        return slotService.getAllSlotInClusterPaginated(storeExternalId, date, showShipmentCount, page, size, Sort.Direction.DESC, "id");
    }

    @PreAuthorize("isAdminAuthenticated()")
    @GetMapping(value = "/v2/cluster_slots")
    public ClusterSlotPagePresenter listSlotsInOneClusterPerDayLocalTime(@RequestParam("store_external_id") @NotNull Long storeExternalId,
                                                                @RequestParam(name = "date", required = false) @DateTimeFormat(pattern="yyyy-MM-dd") LocalDate date,
                                                                @RequestParam(name = "show_shipment_count", required = false) Boolean showShipmentCount,
                                                                @RequestParam(defaultValue = "1") @Min(1) int page,
                                                                @RequestParam(name = "size", defaultValue = "20") int size) {
        if (date == null)
            date = LocalDate.now();

        return slotService.getAllSlotInClusterPaginatedV2(storeExternalId, date, showShipmentCount, page, size, Sort.Direction.DESC, "id");
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PutMapping(value = "/shipments/{number}/cancel_job")
    @ResponseWrapper(rootName = "shipment")
    @Publish(WebhookType.CANCEL_JOB_BY_SHIPMENT)
    public ShipmentPresenter cancelJobByShipment(@PathVariable String number) throws Exception {
        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        long batchId = shipmentService.getShoppingBatchIdByShipmentNumber(number);
        try {
            if (jedisLockService.lock(String.format(BatchV3Controller.BATCH_FINALIZE_LOCK_KEY, batchId))) {
                return shipmentMapper.shipmentToShipmentPresenter(batchSndService.cancelJobByShipment(number));
            } else {
                throw new RedisLockTimeoutException();
            }
        } finally {
            jedisLockService.unlock();
        }
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PutMapping(value = "/shipments/{number}/cancel_shopping_job")
    @ResponseWrapper(rootName = "shipment")
    @Publish(WebhookType.CANCEL_JOB_BY_SHIPMENT)
    public ShipmentPresenter cancelShoppingJobByShipment(@PathVariable String number) throws Exception {
        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        long batchId = shipmentService.getShoppingBatchIdByShipmentNumber(number);
        try {
            if (jedisLockService.lock(String.format(BatchV3Controller.BATCH_FINALIZE_LOCK_KEY, batchId))) {
                return shipmentMapper.shipmentToShipmentPresenter(batchSndService.cancelShoppingJobByShipment(number));
            } else {
                throw new RedisLockTimeoutException();
            }
        } finally {
            jedisLockService.unlock();
        }
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PutMapping(value = "/shipments/{number}/cancel_delivery_job")
    @ResponseWrapper(rootName = "shipment")
    @Publish(WebhookType.CANCEL_JOB_BY_SHIPMENT)
    public ShipmentPresenter cancelDeliveryJobByShipment(@PathVariable String number) throws Exception {
        return shipmentMapper.shipmentToShipmentPresenter(batchSndService.cancelDeiveryJobByShipment(number));
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PostMapping(value = "/shipments/{number}/sync_to_jubelio")
    public ResponseEntity<Map<String, String>> syncToJubelio(@PathVariable String number) {
        Shipment shipment = shipmentRepository.findByNumber(number);
        if (shipment == null)
            return new ResponseEntity<>(Collections.singletonMap("status", "Shipment not found"), HttpStatus.NOT_FOUND);

        JubelioDelivery jubelioDelivery = shipment.getCurrentJubelioDelivery();
        if (jubelioDelivery == null)
            return new ResponseEntity<>(Collections.singletonMap("status", "Jubelio Delivery not found"), HttpStatus.UNPROCESSABLE_ENTITY);

        boolean success = jubelioService.syncSalesOrderToJubelio(jubelioDelivery);
        if (!success) {
            return new ResponseEntity<>(Collections.singletonMap("status", "error"), HttpStatus.INTERNAL_SERVER_ERROR);
        }

        return new ResponseEntity<>(Collections.singletonMap("status", "ok"), HttpStatus.OK);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PutMapping(value = "/shipments/{number}/status")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @ResponseWrapper(ignore = true)
    public void modifyJobStatus(@PathVariable String number, @RequestBody @Valid AdminShipmentStatusUpdateForm form) {
        batchSndService.handleManualShipmentStatusUpdate(number, form);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @GetMapping(value = "/shipments")
    public ShipmentPagePresenter getShipments(@RequestParam(name = "page", defaultValue = "1") int page,
                                              @RequestParam(name = "size", defaultValue = "10") int size,
                                              @RequestParam("order_number") String orderNumber,
                                              @RequestParam("email") String email,
                                              @RequestParam(name = "job_state", defaultValue = "FINISHED") Job.State state) throws Exception {
        PageRequest pageRequest =  PageRequest.of(page - 1 , size, Sort.Direction.DESC, "id");
        Page<Shipment> shipmentPage = shipmentRepository.findAllByOrderNumberAndEmailAndJobState(orderNumber, email, state, pageRequest);
        return shipmentPageMapper.shipmentToShipmentPagePresenter(page, shipmentPage);
    }
}
