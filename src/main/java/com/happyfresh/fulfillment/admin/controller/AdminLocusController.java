package com.happyfresh.fulfillment.admin.controller;

import com.happyfresh.fulfillment.admin.form.*;
import com.happyfresh.fulfillment.common.annotation.ResponseWrapper;
import com.happyfresh.fulfillment.locus.presenter.*;
import com.happyfresh.fulfillment.locus.service.LocusBatchPlanService;
import com.happyfresh.fulfillment.locus.service.LocusService;
import com.happyfresh.fulfillment.locus.service.LocusTeamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/admin/locus")
public class AdminLocusController {

    @Autowired
    private LocusTeamService locusTeamService;

    @Autowired
    private LocusBatchPlanService locusBatchPlanService;

    @Autowired
    private LocusService locusService;

    @PreAuthorize("isAdminAuthenticated()")
    @PostMapping(value = "/teams")
    @ResponseWrapper(rootName = "team")
    public LocusTeamPresenter createTeam(@Valid @RequestBody AdminLocusTeamForm form) {
        return locusTeamService.create(form.getClusterId());
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PostMapping(value = "/batches")
    @ResponseWrapper(rootName = "batches")
    public List<LocusBatchPresenter> createBatchesForCluster(@Valid @RequestBody AdminLocusBatchForm form) {
        return locusBatchPlanService.createEmptyBatches(form);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PostMapping(value = "/plans")
    @ResponseWrapper(rootName = "plans")
    public List<LocusPlanPresenter> createPlansForCluster(@Valid @RequestBody AdminLocusPlanForm form) {
        return locusBatchPlanService.createEmptyPlans(form);
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PostMapping(value = "/homebase_and_vehicle_model")
    @ResponseWrapper(rootName = "homebase_and_vehicle_model")
    public LocusHomebaseVehicleModelPresenter createHomebaseAndVehicleModel(@Valid @RequestBody AdminLocusHomebaseVehicleModelForm form) {
        return locusService.createHomebaseAndVehicleModel(form.getStockLocationId());
    }

    @PreAuthorize("isAdminAuthenticated()")
    @PostMapping(value = "/cluster_activation")
    @ResponseWrapper(rootName = "cluster_activation")
    public LocusClusterActivationPresenter activateCluster(@Valid @RequestBody AdminLocusClusterActivationForm form) {
        return locusService.activateCluster(form.getClusterId());
    }

}
