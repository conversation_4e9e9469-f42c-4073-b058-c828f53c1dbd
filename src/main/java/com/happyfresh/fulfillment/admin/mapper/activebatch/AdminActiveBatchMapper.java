package com.happyfresh.fulfillment.admin.mapper.activebatch;

import com.happyfresh.fulfillment.admin.presenter.activebatch.AdminActiveBatchPresenter;
import com.happyfresh.fulfillment.entity.Batch;
import com.happyfresh.fulfillment.entity.Shipment;
import org.mapstruct.DecoratedWith;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring", uses = {AdminBatchStockLocationMapper.class})
@DecoratedWith(AdminActiveBatchMapperDecorator.class)
public interface AdminActiveBatchMapper {

    @Mapping(source = "batch.handoverTime", target = "plannedHandoverTime")
    AdminActiveBatchPresenter batchToBatchPresenter(Batch batch, List<Shipment> completedShipments);
}
