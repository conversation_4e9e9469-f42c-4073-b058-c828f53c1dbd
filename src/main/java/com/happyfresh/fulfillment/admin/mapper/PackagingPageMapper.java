package com.happyfresh.fulfillment.admin.mapper;

import com.happyfresh.fulfillment.admin.presenter.PackagingPagePresenter;
import com.happyfresh.fulfillment.entity.Packaging;
import org.mapstruct.DecoratedWith;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.springframework.data.domain.Page;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
@DecoratedWith(PackagingPageMapperDecorator.class)
public interface PackagingPageMapper {

    PackagingPagePresenter packagingPageToPackagingPagePresenter(Integer page, Page<Packaging> packagingPage);
}
