package com.happyfresh.fulfillment.shift.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.util.StdDateFormat;
import com.google.common.collect.Lists;
import com.happyfresh.fulfillment.admin.mapper.ShiftMapper;
import com.happyfresh.fulfillment.common.exception.UnprocessableEntityException;
import com.happyfresh.fulfillment.common.service.CSVFileReaderService;
import com.happyfresh.fulfillment.common.tracking.LongerDeliverySlotOptimizationEventTracker;
import com.happyfresh.fulfillment.common.util.function.Result;
import com.happyfresh.fulfillment.entity.Shift;
import com.happyfresh.fulfillment.entity.StockLocation;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.repository.ShiftRepository;
import com.happyfresh.fulfillment.repository.StockLocationRepository;
import com.happyfresh.fulfillment.shift.presenter.ShiftPresenter;
import com.happyfresh.fulfillment.slot.service.SlotUploadV2Service;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.csv.CSVRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.EntityNotFoundException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ShiftUploadService {

    @Autowired
    private CSVFileReaderService csvFileReaderService;

    @Autowired
    private StockLocationRepository stockLocationRepository;

    @Autowired
    private ShiftRepository shiftRepository;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private LongerDeliverySlotOptimizationEventTracker ldsTracker;

    @Autowired
    private ShiftMapper shiftMapper;

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Transactional
    public void uploadShiftsByFile(MultipartFile file) throws Exception {
        Result result = csvFileReaderService.processFile(
            file.getInputStream(),
            this::processShiftUpload);

        if (result.getException() != null)
            throw result.getException();
    }

    private Result<Boolean, Exception> processShiftUpload(String[] headers, Iterator<CSVRecord> csvRecordIterator) {
        try {
            List<CSVRecord> csvRecords = Lists.newArrayList(csvRecordIterator);
            List<ShiftUploadRecord> shiftUploadRecords = convertToShiftUploadRecords(csvRecords);

            List<Shift> uploadedShifts = new ArrayList<>();
            shiftUploadRecords.forEach( record -> {
                record.validate();
                Shift shift = convertRecordToShift(record);
                uploadedShifts.add(shift);
            });
            shiftRepository.saveAll(uploadedShifts);
            try {
                Shift shift = uploadedShifts.get(0);
                ObjectMapper mapper = new ObjectMapper();
                mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
                mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
                mapper.setDateFormat(new StdDateFormat().withColonInTimeZone(true));

                List<ShiftPresenter> shiftPresenters = shiftMapper.toShiftPresenters(uploadedShifts);
                String payload = mapper.writeValueAsString(shiftPresenters);
                ldsTracker.construct(LongerDeliverySlotOptimizationEventTracker.EVENT_UPLOAD_SHIFT, shift.getStockLocation().getCluster(), null, null, batchRepository, null);
                ldsTracker.setValue(payload);
                ldsTracker.track();
            } catch (Exception e) {
                logger.error("Error when serialize upload shift payload", e);
            }
            return new Result<>(true);
        } catch (Exception e) {
            return new Result<>(e);
        }
    }

    private Shift convertRecordToShift(ShiftUploadRecord record) {
        StockLocation stockLocation = stockLocationRepository.findByExternalId(record.getStoreId());
        if (stockLocation == null)
            throw new EntityNotFoundException("Store with code " + record.getStoreId() + " does not exist");
        String timeZoneString = stockLocation.getState().getTimeZone();
        ZoneId zoneId = ZoneId.of(timeZoneString);
        record.setZoneId(zoneId);

        Shift existingShift = shiftRepository.findByStockLocationAndStartTimeAndEndTimeAndType(
                stockLocation,
                record.getUtcStartTime(),
                record.getUtcEndTime(),
                record.getType());
        Shift shift = existingShift != null ? existingShift : new Shift(stockLocation, record.getType(), record.getUtcStartTime(), record.getUtcEndTime());
        shift.setCount(record.getCount());
        return shift;
    }

    private List<ShiftUploadRecord> convertToShiftUploadRecords(List<CSVRecord> csvRecords) {
        return csvRecords
            .stream()
            .map(ShiftUploadRecord::new)
            .collect(Collectors.toList());
    }

    private static class ShiftUploadRecord {
        private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        private static final int STORE_ID_COLUMN = 0;
        private static final int TYPE_COLUMN = 1;
        private static final int START_TIME_COLUMN = 2;
        private static final int END_TIME_COLUMN = 3;
        private static final int COUNT_COLUMN = 4;

        private CSVRecord csvRecord;
        private Long storeId;
        private Shift.Type type;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private Integer count;
        @Getter @Setter private ZoneId zoneId;

        public ShiftUploadRecord(CSVRecord csvRecord) {
            this.csvRecord = csvRecord;
        }

        public Long getStoreId() {
            if (storeId == null)
                storeId = Long.parseLong(csvRecord.get(STORE_ID_COLUMN));
            return storeId;
        }

        public Shift.Type getType() {
            if (type == null) {
                String typeString = csvRecord.get(TYPE_COLUMN);
                if (typeString.equalsIgnoreCase("shopper")) type = Shift.Type.SHOPPER;
                else if (typeString.equalsIgnoreCase("driver")) type = Shift.Type.DRIVER;
            }
            return type;
        }

        public LocalDateTime getStartTime() {
            if (startTime == null)
                startTime = LocalDateTime.parse(csvRecord.get(START_TIME_COLUMN), DATE_TIME_FORMATTER);
            return startTime;
        }

        public LocalDateTime getEndTime() {
            if (endTime == null)
                endTime = LocalDateTime.parse(csvRecord.get(END_TIME_COLUMN), DATE_TIME_FORMATTER);
            return endTime;
        }

        public LocalDateTime getUtcStartTime() {
            return getStartTime()
                    .atZone(zoneId)
                    .withZoneSameInstant(ZoneId.of("UTC"))
                    .toLocalDateTime();
        }

        public LocalDateTime getUtcEndTime() {
            return getEndTime()
                    .atZone(zoneId)
                    .withZoneSameInstant(ZoneId.of("UTC"))
                    .toLocalDateTime();
        }

        public int getCount() {
            if (count == null)
                count = Integer.parseInt(csvRecord.get(COUNT_COLUMN));
            return count;
        }

        public void validate() throws InvalidShiftUploadRecordException {
            if (!hasProperStartEndOrder())
                throw new InvalidShiftUploadRecordException("Invalid shift with end time < start time");
            if (!hasSameStartAndEndDate())
                throw new InvalidShiftUploadRecordException("Invalid shift with different local start & end date");
        }

        private boolean hasSameStartAndEndDate() {
            LocalDate startDateLocal = this.getStartTime().toLocalDate();
            LocalDate endDateLocal = this.getEndTime().toLocalDate();
            return startDateLocal.equals(endDateLocal);
        }

        private boolean hasProperStartEndOrder() {
            return this.getStartTime().isBefore(this.getEndTime());
        }

        public static class InvalidShiftUploadRecordException extends UnprocessableEntityException {
            public InvalidShiftUploadRecordException(String message) {
                super(message);
            }
        }
    }
}
