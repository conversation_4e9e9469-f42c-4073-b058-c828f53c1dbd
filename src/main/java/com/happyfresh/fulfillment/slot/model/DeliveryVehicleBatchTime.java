package com.happyfresh.fulfillment.slot.model;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
public class DeliveryVehicleBatchTime {

    private Long shiftId;

    private Integer vehicleNumber;

    private LocalDateTime batchStartTime;

    private LocalDateTime batchEndTime;

    private LocalDateTime slotStartTime;

    private LocalDateTime slotEndTime;

    private Long userId;

}