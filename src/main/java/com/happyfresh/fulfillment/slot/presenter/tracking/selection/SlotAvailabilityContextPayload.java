package com.happyfresh.fulfillment.slot.presenter.tracking.selection;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.happyfresh.fulfillment.locus.model.LocusTimeSlot;
import com.happyfresh.fulfillment.slot.model.BatchCount;
import com.happyfresh.fulfillment.slot.model.BatchCountId;
import com.happyfresh.fulfillment.slot.model.BatchVehicle;
import com.happyfresh.fulfillment.slot.model.VehicleSlotCount;
import com.happyfresh.fulfillment.slot.presenter.SimpleSlotPresenter;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SlotAvailabilityContextPayload {

    private List<SimpleSlotPresenter> slots;

    private Map<LocalDateTime, List<SimpleSlotPresenter>> clusteredSlots;

    private boolean containsAlcohol;

    private boolean tplEligible;

    private boolean avoidTolls;

    private boolean reservedSlot;

    private boolean deliveryChecker;

    private boolean overMaximumDeliveryRadius;

    private Integer deliveryTimePerShipment;

    private Map<BatchCountId, BatchCount> batchCounts;

    private Map<Long, Boolean> shoppingAvailable;

    private Map<String, Long> deliveryDurations;

    private Map<String, Long> deliveryDistances;

    private String slotTrackingId;

    private Integer days;

    private List<LocusTimeSlot> locusAvailableSlots;

    private Double airDistance;

    private List<VehicleSlotCount> driverVehicleSlotCounts;

    private List<VehicleSlotCount> shopperVehicleSlotCounts;

    private List<BatchVehicle> shopperBatchVehicle;

    private LocalDateTime shoppingEndTime;

}
