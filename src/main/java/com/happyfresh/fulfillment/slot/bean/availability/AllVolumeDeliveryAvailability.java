package com.happyfresh.fulfillment.slot.bean.availability;

import com.happyfresh.fulfillment.common.exception.type.ReservedSlotException;
import com.happyfresh.fulfillment.entity.Slot;
import com.happyfresh.fulfillment.slot.bean.SlotAvailabilityContext;
import com.happyfresh.fulfillment.slot.service.SlotReservedService;
import com.happyfresh.fulfillment.slot.tracking.SlotAvailabilityTracker;
import org.apache.logging.log4j.ThreadContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AllVolumeDeliveryAvailability extends AbstractAvailabilityChain {
    
    private final Logger LOGGER = LoggerFactory.getLogger(AllVolumeDeliveryAvailability.class);

    private SlotReservedService slotReservedService;
    
    public AllVolumeDeliveryAvailability(SlotAvailabilityContext context, SlotReservedService slotReservedService) {
        super(context);
        this.slotReservedService = slotReservedService;
    }

    @Override
    public boolean isAvailable(Slot slot, SlotAvailabilityTracker tracker) {
        try {
            boolean result = isDeliveryAllVolumeSlotAvailable(slot);

            setAndTrackAvailabilityChain(slot, tracker, result);
            return result;
        } catch (Exception e) {
            ThreadContext.put("SLOT_ID", slot.getId().toString());
            ThreadContext.put("SHIPMENT_NUMBER", context.getShipment().getNumber());
            ThreadContext.put("ORDER_NUMBER", context.getShipment().getOrderNumber());
            ThreadContext.put("DELIVERY_CHECKER", context.isDeliveryChecker() ? "true" : "false");
            ThreadContext.put("RESERVED_SLOT", context.isReservedSlot() ? "true" : "false");
            LOGGER.error("Failed on all volume delivery availability", e);
            ThreadContext.clearAll();

            setAndTrackAvailabilityChain(slot, tracker, false);
            if (context.isReservedSlot()) {
                throw new ReservedSlotException();
            }
            return false;
        }
    }

    private boolean isDeliveryAllVolumeSlotAvailable(Slot slot) {
        final int driverCount = getDriverCount(slot.getStartTime());
        final int totalBatchCount = getTotalRangerAndDeliveryBatchCount(slot.getStartTime());

        if (driverCount > totalBatchCount) {
            slot.setVehicleType(Slot.FleetVehicleType.FOUR_WHEELS);
            if (context.isReservedSlot()) {
                slotReservedService.deliveryAllVolumeSlot(context.getShipment(), context.getStockLocation(), slot, context.getAirDistance());
            }
            return true;
        } else {
            return false;
        }
    }
}
