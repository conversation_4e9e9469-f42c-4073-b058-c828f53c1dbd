package com.happyfresh.fulfillment.slot.bean;


import com.happyfresh.fulfillment.entity.Cluster;
import com.happyfresh.fulfillment.entity.Slot;
import com.happyfresh.fulfillment.slot.model.SlotUploadRecord;
import lombok.Getter;
import lombok.Setter;

import java.time.ZoneId;

@Getter
@Setter
public class SlotUploadExpired {

    private Cluster cluster;
    private ZoneId zoneId;
    private SlotUploadRecord firstRecord;
    private Slot.Type slotType;

    public SlotUploadExpired(Cluster cluster, ZoneId zoneId, SlotUploadRecord firstRecord, Slot.Type slotType) {
        this.cluster = cluster;
        this.zoneId = zoneId;
        this.firstRecord = firstRecord;
        this.slotType = slotType;
    }
}
