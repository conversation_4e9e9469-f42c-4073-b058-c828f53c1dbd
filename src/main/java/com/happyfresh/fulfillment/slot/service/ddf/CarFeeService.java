package com.happyfresh.fulfillment.slot.service.ddf;

import com.happyfresh.fulfillment.entity.Slot;
import com.happyfresh.fulfillment.entity.ddf.CarMinFeeMatrix;
import com.happyfresh.fulfillment.entity.ddf.CarPerKmMatrix;
import com.happyfresh.fulfillment.entity.ddf.DDFMatrix;
import com.happyfresh.fulfillment.slot.bean.AppliedFee;
import com.happyfresh.fulfillment.slot.bean.DDFCalculationContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;

public class CarFeeService extends DDFCalculationServiceChain {

    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    private final static BigDecimal MIN_FEE = BigDecimal.valueOf(25000);

    private final static BigDecimal PER_KM_FEE = BigDecimal.valueOf(3300);

    @Override
    protected AppliedFee doCalculate(DDFCalculationContext ddfCalculationContext, Slot slot, AppliedFee chainingResultFee) {
        DDFMatrix minFeeMatrix = ddfCalculationContext.getMatrixCell(CarMinFeeMatrix.class, slot.getStockLocation(), 0,0);
        BigDecimal minFee = minFeeMatrix != null ? minFeeMatrix.getValue() : MIN_FEE;

        DDFMatrix perKmMatrix = ddfCalculationContext.getMatrixCell(CarPerKmMatrix.class, slot.getStockLocation(), 0,0);
        BigDecimal perKmFee = perKmMatrix != null ? perKmMatrix.getValue() :PER_KM_FEE;

        int distance = (int) Math.ceil(ddfCalculationContext.getShippingDistance());
        BigDecimal fee = minFee.add(perKmFee.multiply(BigDecimal.valueOf(distance)));
        chainingResultFee.getDetail().setCarFee(fee);
        chainingResultFee.setTotalFourWheel(chainingResultFee.getTotalFourWheel().add(fee));

        LOGGER.info("[DDF] 4W minFee: {}", minFee);
        LOGGER.info("[DDF] 4W perKmFee: {}", perKmFee);
        LOGGER.info("[DDF] 4W shipping distance: {}", distance);
        LOGGER.info("[DDF] 4W Car fee: {}", fee);

        return callNextChainIfExist(ddfCalculationContext, slot, chainingResultFee);
    }
}
