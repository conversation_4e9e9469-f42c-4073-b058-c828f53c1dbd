package com.happyfresh.fulfillment.lalamove.mapper;

import com.happyfresh.fulfillment.entity.LalamoveDelivery;
import com.happyfresh.fulfillment.lalamove.presenter.LalamoveDeliveryPresenter;
import org.mapstruct.DecoratedWith;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
@DecoratedWith(LalamoveDeliveryMapperDecorator.class)
public interface LalamoveDeliveryMapper {

    LalamoveDeliveryPresenter toLalamoveDeliveryPresenter(LalamoveDelivery lalamoveDelivery);

    List<LalamoveDeliveryPresenter> toLalamoveDeliveryPresenters(List<LalamoveDelivery> lalamoveDeliveries);

}
