package com.happyfresh.fulfillment.lalamove.service.api.v3;

import com.happyfresh.fulfillment.entity.Country;
import com.happyfresh.fulfillment.entity.State;
import com.happyfresh.fulfillment.lalamove.presenter.v3.LalamoveV3CancelOrderPresenter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;

public class LalamoveApiV3CancelOrderRequest extends LalamoveApiV3BaseRequest<LalamoveV3CancelOrderPresenter>{

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    private static final String LALAMOVE_V3_PATH_CANCEL_ORDER = "/v3/orders/%s";
    private String orderId;
    private Country country;
    @Setter
    private State state;

    public LalamoveApiV3CancelOrderRequest(String orderId, State state) {
        this.orderId = orderId;
        this.state = state;
        this.country = state.getCountry();
    }

    @Override
    protected String getRequestCountry() {
        return country.getIsoName();
    }

    @Override
    protected String getRequestPath() {
        return String.format(LALAMOVE_V3_PATH_CANCEL_ORDER, orderId);
    }

    @Override
    protected String getRequestUrl() {
        return getBaseUrl() + getRequestPath();
    }

    @Override
    protected String getBody() throws Exception {
        return "";
    }

    @Override
    protected State getRequestState() {
        return state;
    }

    @Override
    protected HttpMethod getMethod() {
        return HttpMethod.DELETE;
    }

    @Override
    protected HttpEntity<String> getHttpEntity() throws Exception {
        return new HttpEntity<>(getBody(), getHeaders());
    }

    @Override
    protected Class<LalamoveV3CancelOrderPresenter> getReturnClass() {
        return LalamoveV3CancelOrderPresenter.class;
    }

    @Override
    protected Logger getLogger() {
        return logger;
    }
}
