package com.happyfresh.fulfillment.gosend.messaging;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.happyfresh.fulfillment.common.presenter.EnablerEvent;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class GosendWebhookEvent extends EnablerEvent {

    public static final String GOSEND_WEBHOOK_TYPE_CONFIRMED = "CONFIRMED";
    public static final String GOSEND_WEBHOOK_TYPE_ALLOCATED = "ALLOCATED";
    public static final String GOSEND_WEBHOOK_TYPE_OUT_FOR_PICKUP = "OUT_FOR_PICKUP";
    public static final String GOSEND_WEBHOOK_TYPE_PICKED = "PICKED";
    public static final String GOSEND_WEBHOOK_TYPE_OUT_FOR_DELIVERY = "OUT_FOR_DELIVERY";
    public static final String GOSEND_WEBHOOK_TYPE_CANCELLED = "CANCELLED";
    public static final String GOSEND_WEBHOOK_TYPE_ON_HOLD = "ON_HOLD";
    public static final String GOSEND_WEBHOOK_TYPE_DELIVERED = "DELIVERED";
    public static final String GOSEND_WEBHOOK_TYPE_REJECTED = "REJECTED";
    public static final String GOSEND_WEBHOOK_TYPE_NO_DRIVER = "NO_DRIVER";

    private String webhookType;

    private String payload;
}
