package com.happyfresh.fulfillment.gosend.presenter;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class GosendDeliveryPresenter {

    private Long id;

    private Long batchId;

    private String shipmentNumber;

    private String orderNumber;

    private String status;

    private String externalOrderId;

    private String driverName;

    private String driverPhone;

    private String driverPhotoUrl;

    private String bookingType;

    private String trackingUrl;

    @JsonIgnore
    private LocalDateTime latestStatusChangedAt;

    @JsonProperty("latest_status_changed_at")
    public String getLatestStatusChangedAtString() {
        if (latestStatusChangedAt != null)
            return latestStatusChangedAt.toString();
        return null;
    }

    private String pickupEta;

    private String deliveryEta;

    @JsonIgnore
    private LocalDateTime slotStartTime;

    @JsonProperty("slot_start_time")
    public String getSlotStartTimeString() {
        return slotStartTime.toString();
    }

    @JsonIgnore
    private LocalDateTime slotEndTime;

    @JsonProperty("slot_end_time")
    public String getSlotEndTimeString() {
        return slotEndTime.toString();
    }

    @JsonIgnore
    private LocalDateTime createdAt;

    @JsonProperty("created_at")
    public String getCreatedAtString() {
        return createdAt.toString();
    }

}
