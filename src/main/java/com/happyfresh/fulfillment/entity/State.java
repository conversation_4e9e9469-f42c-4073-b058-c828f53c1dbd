package com.happyfresh.fulfillment.entity;

import com.happyfresh.fulfillment.common.jpa.BaseEntityWithCreateAudit;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.util.List;
import java.util.Map;

@Setter
@Getter
@Entity
@Table(name = "hff_state")
public class State extends BaseEntityWithCreateAudit {

    @Id
    @SequenceGenerator(name = "hff_state_id_seq", sequenceName = "hff_state_id_seq", allocationSize = 1)
    @GeneratedValue(generator = "hff_state_id_seq")
    private Long id;

    private String name;

    private String timeZone;

    @Type(type = "hstore")
    private Map<String, String> preferences;

    @ManyToOne
    @JoinColumn(name = "country_id")
    private Country country;

    @OneToMany(mappedBy = "state")
    private List<StockLocation> stockLocations;

    public String getLalamoveCityCode() {
        if (preferences == null)
            return StringUtils.EMPTY;
        return preferences.getOrDefault("lalamove_city_code", StringUtils.EMPTY);
    }

}
