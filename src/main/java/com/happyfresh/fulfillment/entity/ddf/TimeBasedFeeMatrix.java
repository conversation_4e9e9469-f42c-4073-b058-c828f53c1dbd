package com.happyfresh.fulfillment.entity.ddf;

import com.happyfresh.fulfillment.entity.StockLocation;
import lombok.NoArgsConstructor;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.math.BigDecimal;

@NoArgsConstructor
@Entity
@DiscriminatorValue("TimeBasedFeeMatrix")
public class TimeBasedFeeMatrix extends DDFMatrix {

    public TimeBasedFeeMatrix(Integer columnId, Integer rowId, BigDecimal value, StockLocation stockLocation) {
        super(columnId, rowId, value, TimeBasedFeeMatrix.class.getSimpleName(), stockLocation);
    }
}
