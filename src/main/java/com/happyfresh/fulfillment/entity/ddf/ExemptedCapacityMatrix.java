package com.happyfresh.fulfillment.entity.ddf;

import com.happyfresh.fulfillment.entity.StockLocation;
import lombok.NoArgsConstructor;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.math.BigDecimal;

@NoArgsConstructor
@Entity
@DiscriminatorValue("ExemptedCapacityMatrix")
public class ExemptedCapacityMatrix extends DDFMatrix {

    public ExemptedCapacityMatrix(BigDecimal value, StockLocation stockLocation) {
        super(0, 0, value, ExemptedCapacityMatrix.class.getSimpleName(), stockLocation);
    }

}
