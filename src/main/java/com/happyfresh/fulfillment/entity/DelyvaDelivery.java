package com.happyfresh.fulfillment.entity;

import com.happyfresh.fulfillment.tpl.delyva.model.DelyvaStatusCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.Map;

@Setter
@Getter
@NoArgsConstructor
@Entity
@Table(name = "hff_delyva_delivery")
public class DelyvaDelivery extends TplDelivery {

    @Id
    @SequenceGenerator(name = "delyva_delivery_id_seq", sequenceName = "hff_delyva_delivery_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "delyva_delivery_id_seq")
    private Long id;

    private String externalId;

    private LocalDateTime latestStatusChangedAt;

    private LocalDateTime pickUpScheduledAt;

    private String vehicleType;

    private String initialServiceCode;

    private BigDecimal serviceFee;

    private String serviceFeeCurrency;

    private BigDecimal initialServiceFee;

    private Integer bookingAttemptCount;

    private String driverId;

    private String driverName;

    private String driverPhone;

    private String driverPlateNumber;

    private String driverPhoto;

    private String trackingUrl;

    @org.hibernate.annotations.Type(type = "hstore")
    private Map<String, String> miscellaneous;

    @Enumerated(EnumType.STRING)
    private DelyvaStatusCode status;

    @org.hibernate.annotations.Type(type = "hstore")
    private Map<String, String> statuses;

    private String serviceCode;

    private String serviceName;

    public void updateStatus(DelyvaStatusCode status) {
        this.status = status;

        if (this.statuses == null)
            this.statuses = new HashMap<>();

        this.statuses.put(status.toString(), String.valueOf(LocalDateTime.now().toInstant(ZoneOffset.UTC).toEpochMilli()));
        this.latestStatusChangedAt = LocalDateTime.now();
    }

    @Transient
    private boolean isDriverUpdated;

    public boolean enableSwitchHF() {
        return this.status != null
                && this.status.equals(DelyvaStatusCode.ORDER_CREATED);
    }

}
