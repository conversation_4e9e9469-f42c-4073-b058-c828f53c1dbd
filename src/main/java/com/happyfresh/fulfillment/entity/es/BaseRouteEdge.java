package com.happyfresh.fulfillment.entity.es;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.elasticsearch.common.geo.GeoPoint;

@Setter
@Getter
@AllArgsConstructor
public abstract class BaseRouteEdge {

    public enum Source {
        GOOGLE, GRAPHHOPPER;

        @Override
        public String toString() {
            return name();
        }
    }

    @SerializedName("start")
    protected GeoPoint start;

    @SerializedName("end")
    protected GeoPoint end;

    @SerializedName("duration")
    protected Long duration;

    @SerializedName("duration_in_traffic")
    protected Long durationInTraffic;

    @SerializedName("distance")
    protected Long distance;

    @SerializedName("source")
    protected String source;

    @SerializedName("graphhopper_vehicle_type")
    protected String graphhopperVehicleType;

    @SerializedName("timestamp")
    protected long timestamp;

}
