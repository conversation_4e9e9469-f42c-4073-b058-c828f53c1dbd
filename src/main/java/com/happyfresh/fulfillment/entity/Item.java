package com.happyfresh.fulfillment.entity;

import com.happyfresh.fulfillment.common.jpa.BaseEntityWithAudit;
import com.happyfresh.fulfillment.common.util.CurrencyUtil;
import com.happyfresh.fulfillment.common.util.I18nUtil;
import com.happyfresh.fulfillment.shipment.presenter.HighDefinitionItemImagePresenter;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import javax.validation.constraints.AssertTrue;
import java.math.BigDecimal;
import java.util.*;

@Setter
@Getter
@Entity
@Table(name = "hff_item")
public class Item extends BaseEntityWithAudit {

    @Id
    @SequenceGenerator(name = "item_id_seq", sequenceName = "hff_item_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "item_id_seq")
    private Long id;

    private String sku;

    @Type(type = "hstore")
    private Map<String, String> translationNames;

    @Type(type = "hstore")
    private Map<String, String> imageUrl;

    @Type(type = "hstore")
    private Map<String, String> attachmentUrl;

    private BigDecimal costPrice;

    private BigDecimal newCostPrice;

    private BigDecimal price;

    private BigDecimal normalPrice;

    private BigDecimal normalCostPrice;

    private BigDecimal supermarketUnitCostPrice;

    private Double height;

    private Double width;

    private Double depth;

    private Double weight;

    private Double actualWeight;

    private Double averageWeight;

    private String unit;

    private String supermarketUnit;

    private String replacementType;

    private String replacementPreferenceType;

    private String shopperPickType;

    private Integer requestedQty;

    private Integer requestedBeforeShopperStartedQty;

    private Integer bundleQty;

    private Integer freeQty;

    private Integer foundQty;

    private Integer oosQty;

    private Integer rejectedQty;

    private String shopperNotes;

    private Boolean shopperNotesFulfilled;

    private Boolean isProductMismatch;

    private String currency;

    private Integer oosType;

    private String oosDetail;

    private boolean consideredAsAlcohol;

    private boolean consideredAsGeRestrictedProduct;

    private Boolean consideredAsBulky;

    @Type(type = "hstore")
    private Map<String, String> translationDescriptions;


    @Type(type = "hstore")
    private Map<String, String> translationPromotions;

    private String rejectedReason;

    @ManyToOne
    @JoinColumn(name = "category_id")
    private Category category;

    @ManyToOne
    @JoinColumn(name = "category_order_id")
    private CategoryOrder categoryOrder;

    @Type(type = "hstore")
    private Map<String, String> productProperties;

    @ManyToOne
    @JoinColumn(name = "shipment_id")
    private Shipment shipment;

    @ManyToOne
    @JoinColumn(name = "replaced_id")
    private Item replacedItem;

    @OneToMany(cascade = CascadeType.ALL,
            mappedBy = "replacedItem", orphanRemoval = true)
    private Set<Item> replacements = new HashSet<>();

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "item", orphanRemoval = true)
    private Set<ItemReplacementPreference> replacementPreferences = new HashSet<>();

    public enum MismatchReason {
        DIFFERENT_PACKAGING,
        DIFFERENT_WEIGHT,
        DIFFERENT_SKU,
        PRICE_DISCREPANCY;

        @Override
        public String toString() {
            return StringUtils.upperCase(name());
        }
    }

    @Enumerated(EnumType.STRING)
    private MismatchReason mismatchReason;

    private String mismatchData;

    private String mismatchDescription;

    private Integer maximumOrderQuantity;

    private Integer maximumPromoQuantity;

    private String promotionSource;

    @AssertTrue
    public boolean isQtyValid() {
        return (requestedQty != null && requestedQty >= 0) &&
                (bundleQty == null || bundleQty >= 0) &&
                (freeQty == null || freeQty >= 0) &&
                (foundQty == null || foundQty <= requestedQty) &&
                (oosQty == null || oosQty <= requestedQty) &&
                (rejectedQty == null || rejectedQty <= foundQty);
    }

    public double getVolumeInML() {
        if (height == null || width == null || depth == null || requestedQty == null) {
            return 0;
        }

        return (height * width * depth) * requestedQty;
    }

    public double getVolumePerQtyInML() {
        if (height == null || width == null || depth == null) {
            return 0;
        }

        return height * width * depth;
    }

    public double getFinalizedVolumeInML() {
        if (height == null || width == null || depth == null || requestedQty == null) {
            return 0;
        }

        return (height * width * depth) * foundQty;
    }

    public double getTotalActualWeightAdjustment() {
        if (actualWeight != null && foundQty != null) {
            return foundQty * actualWeight;
        }
        return 0;
    }

    public String getName() {
        return I18nUtil.getDefaultMessage(translationNames);
    }

    public String getDescription() {
        return I18nUtil.getDefaultMessage(translationDescriptions);
    }

    public Double getTotalWeightInKG() {
        if (weight == null)
            return 0.0;

        return weight * requestedQty;
    }

    public Double getFinalizedTotalWeightInKG() {
        if (weight == null)
            return 0.0;

        return weight * foundQty;
    }

    public List<String> getPromotions() {
        String message = I18nUtil.getDefaultMessage(translationPromotions);

        List<String> promotions = null;
        if (StringUtils.isNotEmpty(message)) {
            promotions = Arrays.asList(StringUtils.split((message), "|"));
        }

        return promotions;
    }

    public List<String> getImages() {
        List<String> images = new ArrayList<>();
        imageUrl.keySet().stream().sorted().forEach(key -> {
            if (!key.contains("wide")) {
                images.add(imageUrl.get(key));
            }
        });

        return images;
    }

    public List<String> getWideQualityImages() {
        List<String> listOfHDImages = new ArrayList<>();
        List<HighDefinitionItemImagePresenter> images = getAllVariantImages();
        images.forEach(img -> {
            if (img.getWide() != null) {
                listOfHDImages.add(img.getWide());
            } else {
                listOfHDImages.add(img.getNormal());
            }
        });

        return listOfHDImages;
    }

    public List<HighDefinitionItemImagePresenter> getAllVariantImages() {
        Map<String, String> normalImage = new HashMap<>();
        Map<String, String> wideImages = new HashMap<>();
        imageUrl.keySet().stream().sorted().forEach(key -> {
            if (key.contains("wide")) {
                wideImages.put(key, imageUrl.get(key));
            } else {
                normalImage.put(key, imageUrl.get(key));
            }
        });

        List<HighDefinitionItemImagePresenter> presenter = new ArrayList<>();
        normalImage.keySet().stream().sorted().forEach(key -> {
            HighDefinitionItemImagePresenter img = new HighDefinitionItemImagePresenter();
            img.setPosition(key);
            img.setNormal(normalImage.get(key));
            String wideKey = key + "_wide";
            if (wideImages.get(wideKey) != null) {
                img.setWide(wideImages.get(wideKey));
            }

            presenter.add(img);
        });

        return presenter;
    }


    public String getFlag() {
        String flag = null;
        if (oosQty != null && oosQty > 0)
            flag = "out_of_stock";

        return flag;
    }

    public String getDisplayPrice() {
        return CurrencyUtil.format(price, currency);
    }

    public String getDisplayCostPrice() {
        return CurrencyUtil.format(costPrice, currency);
    }

    public String getDisplayNormalPrice() {
        if (normalPrice == null) {
            return null;
        }

        return CurrencyUtil.format(normalPrice, currency);
    }

    public String getDisplayNormalCostPrice() {
        if (normalCostPrice == null) {
            return null;
        }

        return CurrencyUtil.format(normalCostPrice, currency);
    }

    public String getDisplayNewCostPrice() {
        if (newCostPrice == null)
            return null;

        return CurrencyUtil.format(newCostPrice, currency);
    }

    public String getDisplaySupermarketUnitCostPrice() {
        if (supermarketUnitCostPrice == null)
            return null;

        return CurrencyUtil.format(supermarketUnitCostPrice, currency);
    }

    public String getDisplayActualWeight() {
        if (actualWeight != null)
            return actualWeight + " " + supermarketUnit;

        return null;
    }

    public String getDisplayAverageWeight() {
        if (averageWeight != null)
            return "~ " + averageWeight + " " + supermarketUnit;

        return null;
    }

    public BigDecimal getTotalPrice() {
        return price.multiply(new BigDecimal(requestedQty));
    }

    public String getDisplayTotalPrice() {
        return CurrencyUtil.format(getTotalPrice(), currency);
    }

    public BigDecimal getTotalCostPrice() {
        return costPrice.multiply(new BigDecimal(requestedQty));
    }

    public String getDisplayTotalCostPrice() {
        return CurrencyUtil.format(getTotalCostPrice(), currency);
    }

    public Integer getFoundQty() {
        return foundQty != null ? foundQty : 0;
    }

    public Integer getOosQty() {
        return oosQty != null ? oosQty : 0;
    }

    public Integer getRejectedQty() {
        return rejectedQty != null ? rejectedQty : 0;
    }

    public Integer getTotalReplaced() {
        return replacements.stream().mapToInt(Item::getFoundQty).sum();
    }

    public String getSkuWithOmittedSuffix() {
        return sku.replaceAll("(-[i|I][d|D]$)|(-[m|M][y|Y]$)|(-[t|T][h|H]$)", "");
    }

    public boolean isAlreadyShopped() {
        return (getFoundQty() + getOosQty()) > 0;
    }

    public void addAttachment(String type, String url) {
        if (this.attachmentUrl == null)
            this.attachmentUrl = new HashMap<>();

        this.attachmentUrl.put(type, url);
    }

    public void removeAttachment(String type) {
        if (this.attachmentUrl == null)
            return;

        this.attachmentUrl.remove(type);
    }

    public boolean isReplacement() {
        return replacedItem != null;
    }

    public void addReplacementPreference(ItemReplacementPreference replacementPreference) {
        if (!replacementPreferences.contains(replacementPreference)) {
            replacementPreferences.add(replacementPreference);
            replacementPreference.setItem(this);
        }
    }

    public void removeReplacementPreference(ItemReplacementPreference replacementPreference) {
        if (replacementPreferences.contains(replacementPreference)) {
            replacementPreferences.remove(replacementPreference);
            replacementPreference.setItem(null);
        }
    }

    public void clearReplacementPreferences() {
        replacementPreferences.clear();
    }

    public Boolean getConsideredAsBulky() {
        if (consideredAsBulky == null) {
            return false;
        } else {
            return consideredAsBulky;
        }
    }
}
