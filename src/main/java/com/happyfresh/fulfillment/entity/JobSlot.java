package com.happyfresh.fulfillment.entity;

import com.happyfresh.fulfillment.common.jpa.BaseEntityWithCreateAudit;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;

@Setter
@Getter
@Entity
@NoArgsConstructor
@Table(name = "hff_job_slot")
public class JobSlot extends BaseEntityWithCreateAudit {

    @Id
    @SequenceGenerator(name = "job_slot_id_seq", sequenceName = "hff_job_slot_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "job_slot_id_seq")
    private Long id;

    @ManyToOne
    @JoinColumn(name = "job_id")
    private Job job;

    @ManyToOne
    @JoinColumn(name = "slot_id")
    private Slot slot;

    public JobSlot(Job job, Slot slot) {
        this.job = job;
        this.slot = slot;
    }


}