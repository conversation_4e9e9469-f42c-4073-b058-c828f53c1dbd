package com.happyfresh.fulfillment.locus.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;

@Getter
@Setter
public class LocusPlanConfiguration {

    public enum Preset {
        AGGRESSIVE, CONSERVATIVE, BEST_GUESS;

        @Override
        public String toString() {
            return name();
        }
    }

    @Enumerated(EnumType.STRING)
    private Preset preset;

    public enum Option {
        ENABLED, DISABLED, DEFAULT;

        @Override
        public String toString() {
            return name();
        }
    }

    @Enumerated(EnumType.STRING)
    private Option clusteringMode;

    public enum Fairness {
        TASK, DISTANCE, DURATION, TXN_AMOUNT, MAX_VOLUME, NONE;

        @Override
        public String toString() {
            return name();
        }
    }

    @Enumerated(EnumType.STRING)
    private Fairness fairnessMode;

    private LocusVehicleAppointment vehicleAppointment;

    // in seconds
    private Integer minTravelDuration;

    public enum TrafficModel {
        BEST_GUESS, PESSIMISTIC, OPTIMISTIC;

        @Override
        public String toString() {
            return name();
        }
    }

    @Enumerated(EnumType.STRING)
    private TrafficModel trafficModel;

    @Enumerated(EnumType.STRING)
    private Option staticRouting;

    @Enumerated(EnumType.STRING)
    private Option forceUseAllVehicles;

}
