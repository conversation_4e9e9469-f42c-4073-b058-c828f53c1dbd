package com.happyfresh.fulfillment.user.service;

import com.happyfresh.fulfillment.entity.Agent;
import com.happyfresh.fulfillment.entity.AgentInGeofence;
import com.happyfresh.fulfillment.entity.StockLocation;
import com.happyfresh.fulfillment.repository.AgentInGeofenceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class AgentInGeofenceService {

    @Autowired
    private AgentInGeofenceRepository agentInGeofenceRepository;

    @Transactional
    public void createOrUpdateAgentInGeofence(Agent agent) {
        createOrUpdateAgentInGeofence(agent, agent.getStockLocation());
    }

    @Transactional
    public void createOrUpdateAgentInGeofence(Agent agent, StockLocation stockLocation) {
        if (!eligibleToModifyAgentInGeofence(stockLocation))
            return;

        AgentInGeofence agentInGeofence = agentInGeofenceRepository.findByAgentIdAndStockLocationId(agent.getId(), stockLocation.getId());
        if (agentInGeofence == null)
            agentInGeofence = new AgentInGeofence();
        agentInGeofence.setAgentId(agent.getId());
        agentInGeofence.setStockLocationId(stockLocation.getId());
        agentInGeofence.setTenant(stockLocation.getTenant());
        agentInGeofence.setCreatedBy(agent.getUser().getId());
        agentInGeofenceRepository.save(agentInGeofence);
    }

    @Transactional
    public void deleteAgentInGeofence(Agent agent, StockLocation stockLocation) {
        if (eligibleToModifyAgentInGeofence(stockLocation))
            agentInGeofenceRepository.deleteByAgentIdAndStockLocationId(agent.getId(), stockLocation.getId());
    }

    private boolean eligibleToModifyAgentInGeofence(StockLocation stockLocation) {
        return stockLocation.isFulfilledByStrato()
                && stockLocation.enableExpressHfsLogic();
    }
}
