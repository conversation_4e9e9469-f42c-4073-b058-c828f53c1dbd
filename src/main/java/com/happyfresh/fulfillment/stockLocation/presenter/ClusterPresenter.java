package com.happyfresh.fulfillment.stockLocation.presenter;


import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.happyfresh.fulfillment.entity.Slot;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ClusterPresenter {

    private Long id;

    private String name;

    private Slot.Type slotType;

    private Integer shopperSlotDdfOffsetTime;
}
