package com.happyfresh.fulfillment.batch.scheduler;

import com.happyfresh.fulfillment.batch.service.UnassignedBatchAssignmentService;
import net.javacrumbs.shedlock.core.LockAssert;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnProperty(name = "scheduler.enabled", matchIfMissing = true)
public class UnassignedBatchAssignmentScheduler {

    @Autowired
    UnassignedBatchAssignmentService unassignedBatchAssignmentService;

    @Scheduled(cron = "0 0 0/1 * * *")
    @SchedulerLock(name = "autoAssignDriver", lockAtMostFor = "10m", lockAtLeastFor = "10m")
    public void driverAutoAssignScheduler() {
        LockAssert.assertLocked();
        unassignedBatchAssignmentService.assignUnassignedDeliveryOrRangerBatch();
    }

    @Scheduled(cron = "0 1 0/1 * * *")
    @SchedulerLock(name = "autoAssignShopper", lockAtMostFor = "10m", lockAtLeastFor = "10m")
    public void shopperAutoAssignScheduler() {
        LockAssert.assertLocked();
        unassignedBatchAssignmentService.assignUnassignedShoppingBatch();
    }
}
