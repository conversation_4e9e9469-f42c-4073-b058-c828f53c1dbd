package com.happyfresh.fulfillment.batch.mapper;

import com.happyfresh.fulfillment.batch.form.ReceiptForm;
import com.happyfresh.fulfillment.batch.form.ReceiptV2Form;
import com.happyfresh.fulfillment.entity.Receipt;
import com.happyfresh.fulfillment.receipt.presenter.PendingReceiptPresenter;
import com.happyfresh.fulfillment.shipment.presenter.ReceiptPresenter;
import org.mapstruct.DecoratedWith;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

@Mapper(componentModel = "spring")
@DecoratedWith(ReceiptMapperDecorator.class)
public interface ReceiptMapper {

    Receipt receiptFormToReceipt(ReceiptForm receiptForm);

    Receipt receiptFormToReceipt(ReceiptV2Form receiptV2Form);

    ReceiptPresenter receiptToReceiptPresenter(Receipt receipt);

    @Mappings({
            @Mapping(target = "orderNumber", source = "shipment.orderNumber"),
            @Mapping(target = "shipmentNumber", source = "shipment.number"),
    })
    PendingReceiptPresenter receiptToPendingReceiptPresenter(Receipt receipt);
}
