package com.happyfresh.fulfillment.batch.controller;

import com.happyfresh.fulfillment.batch.form.BatchItemFinalizeForm;
import com.happyfresh.fulfillment.batch.form.DeliveryInfoV3Form;
import com.happyfresh.fulfillment.batch.mapper.BatchMapper;
import com.happyfresh.fulfillment.batch.presenter.BatchPresenter;
import com.happyfresh.fulfillment.batch.presenter.BatchPresenterWithException;
import com.happyfresh.fulfillment.batch.service.BatchAvailabilityService;
import com.happyfresh.fulfillment.batch.service.BatchSndService;
import com.happyfresh.fulfillment.common.annotation.Publish;
import com.happyfresh.fulfillment.common.annotation.ResponseWrapper;
import com.happyfresh.fulfillment.common.annotation.type.WebhookType;
import com.happyfresh.fulfillment.common.exception.ApiError;
import com.happyfresh.fulfillment.common.exception.type.RedisLockTimeoutException;
import com.happyfresh.fulfillment.common.service.JedisLockService;
import com.happyfresh.fulfillment.common.util.DistanceUtil;
import com.happyfresh.fulfillment.common.util.UserInfoFetcher;
import com.happyfresh.fulfillment.entity.Batch;
import com.happyfresh.fulfillment.entity.User;
import com.happyfresh.fulfillment.lezcash.service.LezCashService;
import com.happyfresh.fulfillment.repository.BatchRepository;
import com.happyfresh.fulfillment.repository.GrabExpressDeliveryRepository;
import com.happyfresh.fulfillment.repository.ShipmentRepository;
import org.elasticsearch.common.geo.GeoPoint;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/v3/batches")
@Validated
public class BatchV3Controller {

    @Autowired
    private BatchSndService batchSndService;

    @Autowired
    private BatchMapper batchMapper;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private GrabExpressDeliveryRepository grabExpressDeliveryRepository;

    @Autowired
    private UserInfoFetcher userInfoFetcher;

    @Autowired
    private BatchAvailabilityService batchAvailabilityService;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private LezCashService lezCashService;

    public static final String BATCH_FINALIZE_LOCK_KEY = "finalize_batch_%s";

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/finalize")
    @Publish(WebhookType.FINALIZE_BATCH)
    public ResponseEntity<BatchPresenterWithException> finalizeBatch(@PathVariable Long batchId, @Valid @RequestBody BatchItemFinalizeForm form) throws InterruptedException {
        JedisLockService jedisLockService = applicationContext.getBean(JedisLockService.class);
        try {
            if (jedisLockService.lock(String.format(BATCH_FINALIZE_LOCK_KEY, batchId))) {
                List<ApiError> exceptions = batchSndService.finalizeBatchByShipments(form);
                final Batch batch = batchRepository.fetchById(batchId);

                if (exceptions.isEmpty()) {
                    batchSndService.placeOrQueueTPLDeliveries(batchId, form);
                    ifShopperShouldUseLezCashThenTopUp(batch);
                }

                HttpStatus httpStatus = exceptions.isEmpty() ? HttpStatus.OK : HttpStatus.MULTI_STATUS;
                return new ResponseEntity<>(batchMapper.batchToBatchPresenterWithException(batch, exceptions), httpStatus);
            } else {
                throw new RedisLockTimeoutException();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            jedisLockService.unlock();
        }
    }

    private void ifShopperShouldUseLezCashThenTopUp(Batch batch) {
        lezCashService.mainInfo();
        lezCashService.info("Should user use LezCash? " + batch.payUseLezCash());
        lezCashService.info("is Store Blacklisted? " + batch.getStockLocation().isBlackListedForUsingLezCash());
        if (batch.getStockLocation().isBlackListedForUsingLezCash()) {
            return;
        }

        if (batch.payUseLezCash()) {
            lezCashService.topUpAsync(batch.getId());
        }
    }

    @PreAuthorize("isAgentAuthenticated()")
    @GetMapping(value = "/available")
    @ResponseWrapper(rootName = "batches")
    public List<BatchPresenter> availableBatches(@RequestParam(required = false, name = "lat") String latString,
                                                 @RequestParam(required = false, name = "lon") String lonString) {
        User currentUser = userInfoFetcher.getAuthenticatedUser();
        GeoPoint fleetGeoPoint = DistanceUtil.toGeoPoint(latString, lonString);

        return batchAvailabilityService.getAvailableBatchesV3(currentUser, fleetGeoPoint);
    }

    @PreAuthorize("isOwnerAuthenticated(#batchId)")
    @PostMapping("/{batchId}/shipments/{shipmentNumber}/finish")
    @ResponseWrapper(rootName = "batch")
    @Publish(WebhookType.FINISH_SHIPMENT)
    public ResponseEntity<BatchPresenter> finish(@PathVariable Long batchId, @PathVariable String shipmentNumber,
                                                 @Valid @RequestBody DeliveryInfoV3Form deliveryInfoForm) {
        final Batch batch = batchSndService.finishV3(shipmentNumber, deliveryInfoForm);
        return new ResponseEntity<>(batchMapper.batchToBatchPresenter(batch), HttpStatus.OK);
    }

}
