package com.happyfresh.fulfillment.enabler.form;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.happyfresh.fulfillment.common.util.DateTimeUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Setter
@Getter
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class StratoGetShopperCapacityV2Form {

    @NotEmpty
    private String orderNumber;

    private List<StratoOrderItemForm> orderItems;

    @NotEmpty
    private List<LocalDateTime> deliverySlots;

    @JsonProperty("delivery_slots")
    public List<String> getDeliverySlot() {
        return this.deliverySlots.stream()
                .map(DateTimeUtil::localDateTimeToStratoDateTimeString)
                .collect(Collectors.toList());
    }

}
