package com.happyfresh.fulfillment.enabler.form;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Setter
@Getter
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class StratoExpressCapacityForm {

    @NotEmpty
    private String orderNumber;

    @NotNull
    private Integer totalLineItems;

    @NotNull
    private Integer bufferInMinutes;

}
