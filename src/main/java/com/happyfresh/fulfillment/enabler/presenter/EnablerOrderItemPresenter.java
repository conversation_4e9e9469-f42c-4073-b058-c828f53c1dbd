package com.happyfresh.fulfillment.enabler.presenter;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class EnablerOrderItemPresenter {

    private String itemSku;

    private String name;

    private BigDecimal storeNormalPrice;

    private BigDecimal storePromoPrice;

    private Integer quantity;

    private String unit;

    private BigDecimal totalPrice;

    private Double averageWeight;

    private Double totalWeight;

    private Integer quantityOnHand;

    private Integer quantityOutOfStock;

    private String note;

}
