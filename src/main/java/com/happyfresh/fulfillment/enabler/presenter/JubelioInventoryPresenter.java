package com.happyfresh.fulfillment.enabler.presenter;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class JubelioInventoryPresenter {

    private Integer itemId;

    private String itemCode;

    private String itemName;

    private Integer itemGroupId;

    private Boolean isBundle;

    private String brandName;

    private String averageCost;

    private List<JubelioLocationStockPresenter> locationStocks;

    private Map<String, Integer> totalStocks;

}
