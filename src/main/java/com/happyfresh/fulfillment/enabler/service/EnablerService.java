package com.happyfresh.fulfillment.enabler.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.batch.service.JobSndService;
import com.happyfresh.fulfillment.common.annotation.type.WebhookType;
import com.happyfresh.fulfillment.common.exception.ApiError;
import com.happyfresh.fulfillment.common.exception.UnprocessableEntityException;
import com.happyfresh.fulfillment.common.exception.type.AllFinalizedItemsOutOfStockException;
import com.happyfresh.fulfillment.common.exception.type.InvalidItemQtyException;
import com.happyfresh.fulfillment.common.exception.type.InvalidPayloadException;
import com.happyfresh.fulfillment.common.messaging.kafka.KafkaTopicConfig;
import com.happyfresh.fulfillment.common.model.CustomRequestScopeAttr;
import com.happyfresh.fulfillment.common.presenter.EnablerWebhookEvent;
import com.happyfresh.fulfillment.common.service.EnablerEventPublisherService;
import com.happyfresh.fulfillment.common.service.WebhookCustomPublisherService;
import com.happyfresh.fulfillment.common.service.radar.RadarApiService;
import com.happyfresh.fulfillment.common.util.ApplicationUtil;
import com.happyfresh.fulfillment.enabler.form.EnablerItemForm;
import com.happyfresh.fulfillment.enabler.form.EnablerReceiptForm;
import com.happyfresh.fulfillment.enabler.form.EnablerReceiptItemForm;
import com.happyfresh.fulfillment.enabler.form.EnablerWebhookForm;
import com.happyfresh.fulfillment.enabler.mapper.EnablerMapper;
import com.happyfresh.fulfillment.enabler.mapper.EnablerReceiptMapper;
import com.happyfresh.fulfillment.enabler.presenter.EnablerOrderPresenter;
import com.happyfresh.fulfillment.enabler.presenter.EnablerReceiptPresenter;
import com.happyfresh.fulfillment.enabler.presenter.EnablerStorePresenter;
import com.happyfresh.fulfillment.enabler.presenter.hypermart.HypermartOrderStatusPresenter;
import com.happyfresh.fulfillment.enabler.presenter.hypermart.model.HypermartOrderDetail;
import com.happyfresh.fulfillment.enabler.service.api.hypermart.HypermartApiService;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.grabExpress.service.GrabExpressService;
import com.happyfresh.fulfillment.lalamove.service.LalamoveService;
import com.happyfresh.fulfillment.repository.*;
import com.happyfresh.fulfillment.shipment.service.OrderService;
import com.happyfresh.fulfillment.shipment.service.ShipmentService;
import com.happyfresh.fulfillment.tpl.delyva.service.DelyvaService;
import com.happyfresh.fulfillment.user.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.persistence.EntityNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class EnablerService {

    @Autowired
    private StockLocationRepository stockLocationRepository;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private EnablerMapper enablerMapper;

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private ItemRepository itemRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private LalamoveService lalamoveService;

    @Autowired
    private GrabExpressService grabExpressService;

    @Autowired
    private DelyvaService delyvaService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private JobSndService jobSndService;

    @Autowired
    private EnablerEventPublisherService enablerEventPublisherService;

    @Autowired
    private WebhookCustomPublisherService webhookCustomPublisherService;

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ReceiptRepository receiptRepository;

    @Autowired
    private ReceiptItemRepository receiptItemRepository;

    @Autowired
    private EnablerReceiptMapper enablerReceiptMapper;

    @Autowired
    private RadarApiService radarApiService;

    @Autowired
    private HypermartApiService hypermartApiService;

    @Autowired
    private ShipmentService shipmentService;

    public static final ApiError allItemsOutOfStockException = new ApiError(AllFinalizedItemsOutOfStockException.class.getSimpleName(), "All items are out of stock.", "allItemsOutOfStock");

    public List<EnablerStorePresenter> getStores(String enablerString) throws IllegalArgumentException {
        StockLocation.Enabler enabler = StockLocation.Enabler.valueOf(enablerString.toUpperCase());

        List<StockLocation> stockLocations = stockLocationRepository.findByEnabler(enabler);
        return enablerMapper.toEnablerStorePresenters(stockLocations);
    }


    @Transactional
    public void startBatch(Batch batch, User user) {
        for (Job job : batch.getJobs()) {
            job.setState(Job.State.STARTED);
            jobRepository.save(job);
        }

        if (batch.getUser() == null) {
            batch.setUser(user);
            batchRepository.save(batch);
        }
    }

    public void publishWebhookForBatches(WebhookType webhookType, List<Batch> batches) {
        for (Batch shoppingBatch : batches) {
            if (shoppingBatch.getUser() != null)
                webhookCustomPublisherService.publishWebhookForBatch(webhookType, shoppingBatch);
        }
    }

    @Transactional(readOnly = true)
    public List<EnablerOrderPresenter> getOrders(String enablerString, Long externalStoreId) throws IllegalArgumentException {
        StockLocation.Enabler enabler = StockLocation.Enabler.valueOf(enablerString.toUpperCase());
        StockLocation stockLocation = stockLocationRepository.findByExternalId(externalStoreId);

        if (stockLocation == null)
            throw new EntityNotFoundException("Unknown store id");

        if (stockLocation.getEnabler() == null || !stockLocation.getEnabler().equals(enabler))
            throw new EntityNotFoundException("Unknown store id");

        List<Shipment> shipments = shipmentRepository.findAllAvailableEnablerShipments(stockLocation.getId());
        return enablerMapper.toEnablerOrdersPresenter(shipments);
    }

    @Transactional(readOnly = true)
    public EnablerOrderPresenter updateItemStatus(String enablerString, EnablerWebhookForm enablerWebhookForm) {
        StockLocation.Enabler enabler = StockLocation.Enabler.valueOf(enablerString.toUpperCase());
        Shipment shipment = shipmentRepository.findByOrderNumber(enablerWebhookForm.getOrderNumber());
        if (shipment == null)
            throw new EntityNotFoundException("Order not found");

        StockLocation stockLocation = shipment.getSlot().getStockLocation();

        if (stockLocation.getEnabler() == null || !stockLocation.getEnabler().equals(enabler))
            throw new EntityNotFoundException("Order not found");

        List<ApiError> itemExceptions = checkEnablerWebhookForm(shipment, enablerWebhookForm);

        if (itemExceptions.isEmpty() || (itemExceptions.size() == 1 && itemExceptions.contains(allItemsOutOfStockException))) {
            sendEnablerWebhookEvent(enablerWebhookForm);
            EnablerOrderPresenter presenter = enablerMapper.toEnablerOrderPresenter(shipment);
            if (itemExceptions.size() == 1 && itemExceptions.contains(allItemsOutOfStockException)) {
                presenter.setStatus("order_cancelled");
            } else {
                presenter.setStatus(enablerWebhookForm.getStatus());
            }
            List<EnablerItemForm> itemForms = enablerWebhookForm.getItems();
            presenter.getOrderItems().forEach(orderItemPresenter -> {
                EnablerItemForm itemForm = itemForms.stream()
                        .filter(enablerItemForm -> enablerItemForm.getItemSku().equals(orderItemPresenter.getItemSku()))
                        .findFirst().orElse(null);
                if (itemForm != null) {
                    orderItemPresenter.setQuantityOnHand(itemForm.getQuantityOnHand());
                    orderItemPresenter.setQuantityOutOfStock(itemForm.getQuantityOutOfStock());
                }
            });

            return presenter;
        } else {
            throw new InvalidPayloadException("Payload was invalid.", itemExceptions);
        }
    }

    private List<ApiError> checkEnablerWebhookForm(Shipment shipment, EnablerWebhookForm enablerWebhookForm) {
        List<ApiError> itemExceptions = new ArrayList<>();
        Map<String, Integer> itemsRequested = new HashMap<>();
        for (Item item : shipment.getItems()) {
            if (item.isReplacement())
                continue;
            itemsRequested.put(item.getSku(), item.getRequestedQty());
        }

        Map<String, Integer> itemsFound = new HashMap<>();
        Map<String, Integer> itemsOutOfStock = new HashMap<>();

        // Check sku sent by enabler
        for (EnablerItemForm enablerItemForm : enablerWebhookForm.getItems()) {
            itemsFound.put(enablerItemForm.getItemSku(), enablerItemForm.getFoundQuantity());
            itemsOutOfStock.put(enablerItemForm.getItemSku(), enablerItemForm.getQuantityOutOfStock());
        }

        int countItemsOutOfStock = 0;
        for (Map.Entry<String, Integer> requestedPerSku : itemsRequested.entrySet()) {
            Integer requested = requestedPerSku.getValue();
            String sku = requestedPerSku.getKey();
            Integer found = itemsFound.get(sku);
            if (found != null && found > 0 && found.equals(itemsOutOfStock.get(sku)))
                countItemsOutOfStock++;

            if (enablerWebhookForm.isPackStatus()) {
                validateItemPack(itemExceptions, sku, requested, found);
            } else {
                validateItemPick(itemExceptions, sku, requested, found);
            }
        }
        if (itemExceptions.isEmpty() && (countItemsOutOfStock == itemsFound.size()))
            itemExceptions.add(allItemsOutOfStockException);
        return itemExceptions;
    }

    private void validateItemPick(List<ApiError> itemExceptions, String sku, Integer requested, Integer found) {
        if (found != null && requested != null
                && found > 0 && !requested.equals(found))
            itemExceptions.add(new ApiError(InvalidItemQtyException.class.getSimpleName(), "Quantity on hand or quantity out of stock is not valid for item.", sku));
    }

    private void validateItemPack(List<ApiError> itemExceptions, String sku, Integer requested, Integer found) {
        if (found == null) {
            itemExceptions.add(new ApiError(InvalidItemQtyException.class.getSimpleName(), "Missing finalize item(s)", sku));
            return;
        }

        if (!requested.equals(found))
            itemExceptions.add(new ApiError(InvalidItemQtyException.class.getSimpleName(), "Quantity on hand or quantity out of stock is not valid for item.", sku));
    }

    private void sendEnablerWebhookEvent(EnablerWebhookForm enablerWebhookForm) {
        EnablerWebhookEvent event;
        try {
            event = new EnablerWebhookEvent();
            event.setStatus(enablerWebhookForm.getStatus());
            event.setPayload(new ObjectMapper().writeValueAsString(enablerWebhookForm));
        } catch (JsonProcessingException e) {
            logger.error("Error when reading webhook body.", e);
            return;
        }

        enablerEventPublisherService.publish(
                event,
                KafkaTopicConfig.ENABLER_PROCESSING_TOPIC,
                enablerWebhookForm.getOrderNumber()
        );
    }

    @Transactional
    public void handleEvent(EnablerWebhookEvent event) {
        EnablerWebhookForm form;
        try {
            form = mapper.readValue(event.getPayload(), EnablerWebhookForm.class);
        } catch (IOException e) {
            logger.error("Failed mapping EnablerEvent payload to EnablerWebhookForm", e);
            return;
        }

        Shipment shipment = shipmentRepository.findByOrderNumber(form.getOrderNumber());
        if (shipment == null)
            return;

        Job shoppingJob = getShoppingJob(shipment);
        if (shoppingJob.getState().equals(Job.State.FINISHED)) {
            return;
        }

        StockLocation stockLocation = shipment.getSlot().getStockLocation();

        setSystemAdminAuthToken(shipment);

        RequestContextHolder.setRequestAttributes(new CustomRequestScopeAttr());
        RequestContextHolder.currentRequestAttributes().setAttribute(ApplicationUtil.LOCALE, "en",  RequestAttributes.SCOPE_REQUEST);

        List<ApiError> itemExceptions = new ArrayList<>();
        if (form.getStatus().equalsIgnoreCase(EnablerWebhookEvent.Status.ORDER_ITEM_PACKED.toString()))
            itemExceptions = checkEnablerWebhookForm(shipment, form);

        if (itemExceptions.size() == 1 && itemExceptions.contains(allItemsOutOfStockException)) {
            failShopping(shipment);
            RequestContextHolder.resetRequestAttributes();
            return;
        }

        for (EnablerItemForm itemForm : form.getItems()) {
            try {
                finalizeItem(itemForm, shipment);
            } catch (Exception exception) {
                itemExceptions.add(new ApiError(exception.getClass().getSimpleName(), exception.getMessage(), itemForm.getItemSku()));
            }
        }

        if (!itemExceptions.isEmpty()) {
            logExceptions(event.getEventType(), form.getOrderNumber(), itemExceptions);
        }

        Role shopperRole = roleRepository.findByName(Role.Name.ENABLER_SHOPPER);
        List<User> shoppers = userRepository.findAllByRolesContainingAndTenantId(shopperRole, shipment.getTenant().getId());
        if (shoppers.isEmpty())
            return;

        User shopper = shoppers.get(0);

        if (itemExceptions.isEmpty() && form.getStatus().equalsIgnoreCase(EnablerWebhookEvent.Status.ORDER_ITEM_PACKED.toString())) {
            finalizeShipment(shipment, stockLocation, shopper, shoppingJob);
        }
        RequestContextHolder.resetRequestAttributes();
    }

    private void finalizeShipment(Shipment shipment, StockLocation stockLocation, User shopper, Job shoppingJob) {
        // Finalize shipment
        if (shoppingJob.getState().equals(Job.State.INITIAL)) {
            transitionToStartShopping(shopper,shoppingJob);
        }
        jobSndService.changeJobState(shoppingJob, Job.State.FINALIZING);

        // Calculate order total for every shipment in a batch
        Double newOrderTotal = orderService.getOrderTotal(shipment.getNumber());
        BigDecimal orderTotal = BigDecimal.valueOf(newOrderTotal);
        shipment.setOrderTotal(orderTotal);
        shipmentRepository.save(shipment);

        // Finish Batch
        jobSndService.changeJobState(shoppingJob, Job.State.FINISHED);
        grabExpressService.bookByShipment(stockLocation, shipment);

        Batch shoppingBatch = shoppingJob.getBatch();

        webhookCustomPublisherService.publishWebhookForBatch(WebhookType.FINALIZE_BATCH, shoppingBatch);
        webhookCustomPublisherService.publishWebhookForBatchShipment(WebhookType.PAY_SHIPMENT, shoppingBatch, shipment);

        Country country = stockLocation.getState().getCountry();
        Optional<Job> deliveryOrRanger = shipment.getDeliveryOrRangerJob();
        if (deliveryOrRanger.isPresent() && deliveryOrRanger.get().getBatch().getTplType() == null) {
            // Create geofence for fleet tracking
            // Radar service disabled
//            radarApiService.createDeliveryGeofence(shipment, country);
        }
    }

    private void finalizeItem(EnablerItemForm form, Shipment shipment) {
        Item item = itemRepository.findByShipmentIdAndReplacedItemIdAndSku(shipment.getId(), null, form.getItemSku());

        if ((form.getQuantityOnHand() == 0 && form.getQuantityOutOfStock() == 0) ||
                (item.getRequestedQty() != form.getQuantityOnHand() + form.getQuantityOutOfStock())) {
            throw new InvalidItemQtyException("Finalize quantity is not valid.");
        }

        item.setFoundQty(form.getQuantityOnHand());
        item.setOosQty(form.getQuantityOutOfStock());
        if (item.getOosQty() > 0) {
            // Set default oos
            item.setOosType(0);
            item.setOosDetail("Other reason...");
        } else {
            item.setOosType(null);
            item.setOosDetail(null);
        }
        item.setShopperNotesFulfilled(true);

        if (item.getAverageWeight() != null) {
            item.setActualWeight(item.getAverageWeight() * item.getFoundQty());
        }

        itemRepository.save(item);
    }

    public AbstractAuthenticationToken setSystemAdminAuthToken(Shipment shipment) {
        Tenant tenant = shipment.getTenant();
        Role role = roleRepository.findByNameAndTenantId(Role.Name.SYSTEM_ADMIN, tenant.getId());
        User user = userRepository.findByRolesContainingAndTenantId(role, tenant.getId());

        AbstractAuthenticationToken authenticationToken = userService.getAuthenticationToken(user.getToken(), user.getTenant().getToken());
        SecurityContextHolder.getContext().setAuthentication(authenticationToken);
        return authenticationToken;
    }

    public Job getShoppingJob(Shipment shipment) {
        Optional<Job> shoppingJob = shipment.getJobs().stream().filter(job -> job.isShopping() || job.isOnDemandShopping() || job.isRanger() || job.isOnDemandRanger()).findAny();

        if (!shoppingJob.isPresent())
            throw new EntityNotFoundException();

        return shoppingJob.get();
    }

    public void transitionToStartShopping(User user, Job job) {
        Batch batch = job.getBatch();
        if (batch.getUser() == null) {
            batch.setUser(user);
            batchRepository.save(batch);
        }

        job.setState(Job.State.STARTED);
        jobRepository.save(job);
    }

    private void logExceptions(String eventName, String orderNumber, List<ApiError> exceptions) {
        try {
            logger.error("["+this.getClass().getName()+"] Failed to process "+ eventName +" for order " + orderNumber + ". Exceptions: " + mapper.writeValueAsString(exceptions));
        } catch (Exception e) {
            logger.error("["+this.getClass().getName()+"] Failed to process "+ eventName +" for order " + orderNumber + " and failed mapping exceptions to string", e);
        }
    }

    @Transactional
    public EnablerReceiptPresenter createReceipt(String enablerString,
                                                       String orderNumber,
                                                       EnablerReceiptForm form) throws IllegalArgumentException {
        StockLocation.Enabler enabler = StockLocation.Enabler.valueOf(enablerString.toUpperCase());

        if (!orderNumber.equals(form.getOrderNumber()))
            throw new UnprocessableEntityException("Order number is mismatched with payload");

        Shipment shipment = shipmentRepository.findByOrderNumber(orderNumber);
        if (shipment == null)
            throw new UnprocessableEntityException("Order is not exists");

        StockLocation stockLocation = shipment.getSlot().getStockLocation();
        if (!stockLocation.getEnabler().equals(enabler))
            throw new UnprocessableEntityException("Unknown order number");

        List<String> receiptNumbers = shipment.getReceipts().stream().map(Receipt::getNumber).collect(Collectors.toList());
        if (receiptNumbers.contains(form.getReceiptNumber()))
            throw new UnprocessableEntityException("Receipt with the same number already exists");

        Receipt receipt = enablerReceiptMapper.toReceipt(form);
        receipt.setCompleted(true);
        receipt.setShipment(shipment);
        receiptRepository.save(receipt);

        List<ReceiptItem> receiptItems = new ArrayList<>();
        for (EnablerReceiptItemForm receiptItemForm : form.getItems()) {
            ReceiptItem receiptItem = enablerReceiptMapper.toReceiptItem(receiptItemForm);
            receiptItem.setReceipt(receipt);
            receiptItems.add(receiptItem);
        }
        receiptItemRepository.saveAll(receiptItems);
        receipt.setReceiptItems(receiptItems);

        return enablerReceiptMapper.toEnablerReceiptPresenter(receipt);
    }

    @Transactional
    public void placeOrderTPL(EnablerWebhookEvent event) {
        EnablerWebhookForm form;
        try {
            form = mapper.readValue(event.getPayload(), EnablerWebhookForm.class);
        } catch (IOException e) {
            logger.error("Failed mapping EnablerEvent payload to EnablerWebhookForm", e);
            return;
        }

        Shipment shipment = shipmentRepository.findByOrderNumber(form.getOrderNumber());
        if (shipment == null)
            return;

        Job shoppingJob = getShoppingJob(shipment);
        if (!shoppingJob.getState().equals(Job.State.FINISHED)) {
            return;
        }

        AbstractAuthenticationToken authenticationToken = setSystemAdminAuthToken(shipment);

        delyvaService.placeOrderInOneBatchAsync(shoppingJob.getBatch(), authenticationToken);
        lalamoveService.placeOrderInOneBatchAsync(shoppingJob.getBatch(), authenticationToken);
    }

    @Transactional(readOnly = true)
    public void checkHypermartLateOrder(Tenant tenant) {
        if(Boolean.FALSE.equals(tenant.isEnableHypermartCheckStatusScheduler()))
            return;

        List<StockLocation> stockLocations = stockLocationRepository.findByEnablerAndTenant(StockLocation.Enabler.HYPERMART, tenant);
        if (stockLocations.isEmpty())
            return;

        LocalDateTime minimumStartTime = LocalDateTime.now().minusWeeks(1L);
        List<Long> stockLocationIds = stockLocations.stream().map(StockLocation::getId).collect(Collectors.toList());
        List<Batch> batches = batchRepository.findAllLateShoppingBatches(stockLocationIds, minimumStartTime, Job.getOngoingShoppingStatesValue());

        if(batches.isEmpty()) {
            logger.info("[checkHypermartLateOrder] No matching batch found");
        }else{
            List<Long> batchIdList = batches.stream().map(batch -> batch.getId()).collect(Collectors.toList());
            logger.info("[checkHypermartLateOrder] Found matching batch {}", batchIdList);
        }

        for (Batch batch : batches) {
            fetchOrderDetailAndUpdateOrderStatus(batch);
        }
    }

    @Async
    private void fetchOrderDetailAndUpdateOrderStatus(Batch batch) {
        Optional<Job> optShoppingJob = batch.getJobs().stream().filter(Job::isShopping).findFirst();
        if (optShoppingJob.isEmpty()) {
            logger.info("[checkHypermartLateOrder] cannot find shopping job from batch: {}", batch.getId());
            return;
        }

        Job shoppingJob = optShoppingJob.get();
        Shipment shipment = shoppingJob.getShipment();
        Optional<HypermartOrderStatusPresenter> optOrderStatus = hypermartApiService.getOrderStatus(shipment);
        if (optOrderStatus.isPresent()) {
            HypermartOrderStatusPresenter orderStatusPresenter = optOrderStatus.get();
            handleOrderStatus(shipment, orderStatusPresenter);
        }else{
            logger.info("[checkHypermartLateOrder] Order not found: {}", shipment.getOrderNumber());
        }

    }

    private void handleOrderStatus(Shipment shipment, HypermartOrderStatusPresenter orderStatusPresenter) {
        if (orderStatusPresenter.getOrderStatusDetail() != null) {
            logger.info("[checkHypermartLateOrder] Order Status: {} is Completed", shipment.getOrderNumber());
            // when order complete, sent it as order_item_packed event
            EnablerWebhookForm form = convertHypermartOrderDetailToWebhookForm(orderStatusPresenter.getOrderStatusDetail());
            updateItemStatus(StockLocation.Enabler.HYPERMART.toString(), form);
        } else if (HypermartApiService.MESSAGE_CANCELLED_ORDER.equals(orderStatusPresenter.getErrorMsg())) {
            logger.info("[checkHypermartLateOrder] Order Status: {} is Cancelled", shipment.getOrderNumber());
            // when order cancelled, sent it as order_item_packed event with all item oos
            EnablerWebhookForm form = convertShipmentToOosWebhookForm(shipment);
            updateItemStatus(StockLocation.Enabler.HYPERMART.toString(), form);
        } else if (HypermartApiService.MESSAGE_ONGOING_ORDER.equals(orderStatusPresenter.getErrorMsg())) {
            logger.info("[checkHypermartLateOrder] Order Status: {} is Ongoing", shipment.getOrderNumber());
        } else {
            logger.error("[checkHypermartLateOrder] Invalid Order Status for {}: {}", shipment.getOrderNumber(), orderStatusPresenter.getErrorMsg());
        }
    }

    private EnablerWebhookForm convertHypermartOrderDetailToWebhookForm(HypermartOrderDetail orderDetail) {
        EnablerWebhookForm form = new EnablerWebhookForm();
        form.setStatus(EnablerWebhookEvent.Status.ORDER_ITEM_PACKED.toString());
        form.setOrderNumber(orderDetail.getHappyFreshID());

        List<EnablerItemForm> itemForms = orderDetail.getResult().stream().map(
                item -> getEnablerItemForm(item.getItemSku(), item.getQuantityOnHand(), item.getQuantityOutOfStock())
        ).collect(Collectors.toList());

        form.setItems(itemForms);
        return form;
    }

    private EnablerWebhookForm convertShipmentToOosWebhookForm(Shipment shipment) {
        EnablerWebhookForm form = new EnablerWebhookForm();
        form.setStatus(EnablerWebhookEvent.Status.ORDER_ITEM_PACKED.toString());
        form.setOrderNumber(shipment.getOrderNumber());

        List<EnablerItemForm> itemForms = shipment.getItems().stream().map(
                item -> getEnablerItemForm(item.getSku(), 0, item.getRequestedQty())
        ).collect(Collectors.toList());

        form.setItems(itemForms);
        return form;
    }

    private EnablerItemForm getEnablerItemForm(String sku, Integer qtyOnhand, Integer qtyOos) {
        EnablerItemForm itemForm = new EnablerItemForm();
        itemForm.setItemSku(sku);
        itemForm.setQuantityOnHand(qtyOnhand);
        itemForm.setQuantityOutOfStock(qtyOos);
        return itemForm;
    }

    @Transactional
    public void failShopping(Shipment shipment) {
        try {
            shipmentService.cancelByShopper(shipment, null, "OutOfStock");
            webhookCustomPublisherService.publishWebhookShipment(WebhookType.CANCEL_BY_SHOPPER, shipment);
        } catch (Exception e) {
            String errorMessage = String.format("[%s] Failed to fail shopping on order %s. Exception: %s", this.getClass().getName(), shipment.getOrderNumber(), e);
            logger.error(errorMessage);
        }
    }

}
