package com.happyfresh.fulfillment.enabler.service.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.happyfresh.fulfillment.enabler.form.StratoGetShopperCapacityForm;
import com.happyfresh.fulfillment.enabler.presenter.StratoGetShopperCapacityPresenter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;

public class StratoApiGetShopperCapacityRequest extends StratoApiBaseRequest<StratoGetShopperCapacityPresenter[]> {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private final Long externalStockLocationId;
    private final ObjectMapper mapper;
    private final StratoGetShopperCapacityForm form;

    public StratoApiGetShopperCapacityRequest(Long externalStockLocationId, StratoGetShopperCapacityForm form, ObjectMapper mapper) {
        this.externalStockLocationId = externalStockLocationId;
        this.form = form;
        this.mapper = mapper;
    }

    @Override
    protected String getRequestUrl() {
        return getBaseUrl() + getRequestPath();
    }

    @Override
    protected String getRequestPath() {
        return "/store/" + externalStockLocationId + "/capacity";
    }

    @Override
    protected String getBody() throws Exception {
        return mapper.writer().writeValueAsString(this.form);
    }

    @Override
    protected HttpMethod getMethod() {
        return HttpMethod.GET;
    }

    @Override
    protected HttpEntity<String> getHttpEntity() throws Exception {
        return new HttpEntity<>(getBody(), getHeaders());
    }

    @Override
    protected Class<StratoGetShopperCapacityPresenter[]> getReturnClass() {
        return StratoGetShopperCapacityPresenter[].class;
    }

    @Override
    protected Logger getLogger() {
        return logger;
    }

}
