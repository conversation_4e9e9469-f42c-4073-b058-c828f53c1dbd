package com.happyfresh.fulfillment.enabler.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.happyfresh.fulfillment.batch.mapper.BatchMapper;
import com.happyfresh.fulfillment.common.annotation.type.WebhookType;
import com.happyfresh.fulfillment.common.jpa.TransactionHelper;
import com.happyfresh.fulfillment.common.model.CustomRequestScopeAttr;
import com.happyfresh.fulfillment.common.presenter.JubelioEvent;
import com.happyfresh.fulfillment.common.presenter.ResiEvent;
import com.happyfresh.fulfillment.common.service.JubelioApiService;
import com.happyfresh.fulfillment.common.service.WebhookPublisherService;
import com.happyfresh.fulfillment.common.util.DateTimeUtil;
import com.happyfresh.fulfillment.enabler.form.JubelioCreateSalesOrderForm;
import com.happyfresh.fulfillment.enabler.form.ResiWebhookForm;
import com.happyfresh.fulfillment.enabler.mapper.JubelioDeliveryMapper;
import com.happyfresh.fulfillment.enabler.presenter.JubelioDeliveryPresenter;
import com.happyfresh.fulfillment.enabler.presenter.JubelioInventoriesPresenter;
import com.happyfresh.fulfillment.enabler.presenter.JubelioInventoryPresenter;
import com.happyfresh.fulfillment.enabler.presenter.JubelioSalesOrderPresenter;
import com.happyfresh.fulfillment.entity.*;
import com.happyfresh.fulfillment.repository.*;
import com.happyfresh.fulfillment.shipment.mapper.ItemMapper;
import com.happyfresh.fulfillment.shipment.service.OrderService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;

import javax.persistence.EntityNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class JubelioService {

    private static String JUBELIO_WEBHOOK_PATH = "/api/jubelio_delivery/webhook/";

    @Autowired
    private JubelioDeliveryRepository jubelioDeliveryRepository;

    @Autowired
    private JubelioApiService jubelioApiService;

    @Autowired
    private JubelioDeliveryMapper jubelioDeliveryMapper;

    @Autowired
    private ItemRepository itemRepository;

    @Autowired
    private WebhookPublisherService webhookPublisherService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private BatchRepository batchRepository;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private OrderService orderService;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private BatchMapper batchMapper;

    @Autowired
    private ItemMapper itemMapper;

    @Autowired
    private ResiService resiService;

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private TransactionHelper transactionHelper;

    private final Logger LOGGER = LoggerFactory.getLogger(JubelioService.class);


    @Transactional
    public void createJubelioDelivery(Shipment shipment) {
        JubelioDelivery existingJubelioDelivery = jubelioDeliveryRepository.findByShipmentId(shipment.getId());
        if (existingJubelioDelivery != null)
            return;

        JubelioDelivery jubelioDelivery =  new JubelioDelivery();
        jubelioDelivery.setShipment(shipment);
        jubelioDelivery.setState(JubelioDelivery.State.INITIAL);
        jubelioDelivery.setLatestStateChangedAt(LocalDateTime.now());
        jubelioDeliveryRepository.save(jubelioDelivery);
    }

    public void createSalesOrderByScheduler(JubelioDelivery jubelioDelivery) {
        createSalesOrderForTomorrow(jubelioDelivery);
        transactionHelper.withNewTransaction(() -> {
            if (DateTimeUtil.isTomorrow(jubelioDelivery.getShipment().getSlot().getStartTime())) {
                publishSyncEvent(jubelioDelivery);
            }
        });
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void createSalesOrderForTomorrow(JubelioDelivery jubelioDelivery) {
        Shipment shipment = jubelioDelivery.getShipment();
        if (DateTimeUtil.isTomorrow(shipment.getSlot().getStartTime())) {
            syncSalesOrder(jubelioDelivery);
        }
    }

    public boolean syncSalesOrderToJubelio(JubelioDelivery jubelioDelivery) {
        boolean success = syncSalesOrder(jubelioDelivery);
        publishSyncEvent(jubelioDelivery);
        return success;
    }

    @Transactional
    public boolean syncSalesOrder(JubelioDelivery jubelioDelivery) {
        Shipment shipment = jubelioDelivery.getShipment();

        Role shopperRole = roleRepository.findByName(Role.Name.JUBELIO_SHOPPER);
        User shopper = userRepository.findByRolesContainingAndTenantId(shopperRole, shipment.getTenant().getId());

        Batch shoppingBatch = getShoppingJob(shipment).getBatch();

        StockLocation stockLocation = shipment.getSlot().getStockLocation();

        boolean success = true;

        try {
            RequestContextHolder.setRequestAttributes(new CustomRequestScopeAttr());

            JubelioInventoriesPresenter jubelioInventoriesPresenter = jubelioApiService.getInventories(stockLocation);
            if (jubelioInventoriesPresenter != null) {
                List<JubelioInventoryPresenter> jubelioInventories = jubelioInventoriesPresenter.getData();
                Map<String, JubelioInventoryPresenter> jubelioSKUs = jubelioInventories.stream().collect(Collectors.toMap(JubelioInventoryPresenter::getItemCode, jubelioInventoryPresenter -> jubelioInventoryPresenter));
                JubelioCreateSalesOrderForm jubelioCreateSalesOrderForm = jubelioDeliveryMapper.jubelioDeliveryToJubelioSalesOrderForm(jubelioDelivery, jubelioSKUs);

                Long salesOrderId = jubelioApiService.createSalesOrder(jubelioCreateSalesOrderForm, stockLocation);

                if (salesOrderId != null) {
                    publishWebhook(WebhookType.START_BATCH, batchMapper.batchToBatchPresenter(shoppingBatch), "batch", "/api/batches/"+shoppingBatch.getId()+"/start", shipment.getTenant());
                    // Check OOS Item
                    for (Item item : shipment.getItems()) {
                        JubelioInventoryPresenter jubelioItem = jubelioSKUs.getOrDefault(item.getSkuWithOmittedSuffix(), null);
                        if (jubelioItem != null) {
                            int availableStock = jubelioItem.getTotalStocks().get("available");
                            if (availableStock >= item.getRequestedQty()) {
                                item.setFoundQty(item.getRequestedQty());
                            } else if (availableStock > 0) {
                                item.setFoundQty(availableStock);
                            } else {
                                item.setFoundQty(0);
                                item.setOosType(0);
                                item.setOosDetail("Out Of Stock");
                                publishWebhook(WebhookType.MARK_ITEM_OOS, itemMapper.toItemPresenter(item), "item", "/api/batches/"+shoppingBatch.getId()+"/shipments/"+shipment.getNumber()+"/items/"+item.getSku()+"/mark_oos", shipment.getTenant());
                            }
                        } else {
                            // Item which not available at enabler will be marked as OOS
                            item.setFoundQty(0);
                            item.setOosType(0);
                            item.setOosDetail("Out Of Stock");
                            publishWebhook(WebhookType.MARK_ITEM_OOS, itemMapper.toItemPresenter(item), "item", "/api/batches/"+shoppingBatch.getId()+"/shipments/"+shipment.getNumber()+"/items/"+item.getSku()+"/mark_oos", shipment.getTenant());
                        }
                        item.setOosQty(item.getRequestedQty() - item.getFoundQty());
                        itemRepository.save(item);
                    }

                    jubelioDelivery.setSalesOrderId(salesOrderId);
                    jubelioDelivery.setState(JubelioDelivery.State.PAID);
                    jubelioDelivery.setLatestStateChangedAt(LocalDateTime.now());
                    jubelioDeliveryRepository.save(jubelioDelivery);

                    transitionToFinishShopping(shopper, getShoppingJob(shipment));

                    Double newOrderTotal = orderService.getOrderTotal(shipment.getNumber(), shipment.getTenant());
                    BigDecimal orderTotal = new BigDecimal(newOrderTotal);
                    shipment.setOrderTotal(orderTotal);
                    shipmentRepository.save(shipment);
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            success = false;
        } finally {
            RequestContextHolder.resetRequestAttributes();
        }

        return success;
    }

    @Transactional
    public void handleEvent(JubelioEvent jubelioEvent) {
        if (jubelioEvent.getStatus().equalsIgnoreCase(JubelioDelivery.State.SHIPPED.toString())) {
            JubelioDelivery jubelioDelivery = jubelioDeliveryRepository.findBySalesOrderId(jubelioEvent.getSalesOrderId());
            if (jubelioDelivery == null) return;

            // PAID -> SHIPPED
            if (jubelioDelivery.getState() == JubelioDelivery.State.PAID) {
                checkSalesOrderDeliveryStatus(jubelioDelivery);
                return;
            }

            // SHIPPED -> SHIPPED (airwaybill changes)
            if (jubelioDelivery.getState() == JubelioDelivery.State.SHIPPED) {
                checkAirwayBillChanges(jubelioDelivery);
            }
        }
    }

    @Transactional
    public void checkSalesOrderDeliveryStatus(JubelioDelivery jubelioDelivery) {
        Shipment shipment = jubelioDelivery.getShipment();
        StockLocation stockLocation = shipment.getSlot().getStockLocation();
        try {
            jubelioApiService.getSalesOrder(jubelioDelivery.getSalesOrderId(), stockLocation)
                    .ifPresent(jubelioSalesOrderPresenter -> {
                        if (!StringUtils.isEmpty(jubelioSalesOrderPresenter.getCourier()) && !StringUtils.isEmpty(jubelioSalesOrderPresenter.getTrackingNo())) {
                            changeStateToShipped(jubelioDelivery, jubelioSalesOrderPresenter);
                        }
                    });
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void checkAirwayBillChanges(JubelioDelivery jubelioDelivery) {
        Shipment shipment = jubelioDelivery.getShipment();
        StockLocation stockLocation = shipment.getSlot().getStockLocation();
        try {
            jubelioApiService.getSalesOrder(jubelioDelivery.getSalesOrderId(), stockLocation)
                    .ifPresent(soPresenter -> updateAirwayBillAndCourierChanges(jubelioDelivery, soPresenter));
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    private void updateAirwayBillAndCourierChanges(JubelioDelivery jubelioDelivery, JubelioSalesOrderPresenter salesOrderPresenter) {
        if (salesOrderPresenter.getTrackingNumber().equalsIgnoreCase(jubelioDelivery.getAirwayBill()) &&
                salesOrderPresenter.getCourier().equalsIgnoreCase(jubelioDelivery.getCourier())
        ) { return; } // There are no changes.

        jubelioDelivery.setAirwayBill(salesOrderPresenter.getTrackingNo());
        jubelioDelivery.setCourier(salesOrderPresenter.getCourier());
        jubelioDeliveryRepository.save(jubelioDelivery);

        try {
            resiService.trackAirwayBill(jubelioDelivery);
        } catch (Exception e) {
            LOGGER.error("Failed to track airwaybill. " + e.getMessage(), e);
        }
    }

    @Transactional
    public JubelioDelivery changeStateToShipped(JubelioDelivery jubelioDelivery, JubelioSalesOrderPresenter jubelioSalesOrderPresenter) {
        jubelioDelivery.setState(JubelioDelivery.State.SHIPPED);
        if (StringUtils.isEmpty(jubelioDelivery.getAirwayBill())) {
            jubelioDelivery.setAirwayBill(jubelioSalesOrderPresenter.getTrackingNo());
        }
        if (StringUtils.isEmpty(jubelioDelivery.getCourier())) {
            jubelioDelivery.setCourier(jubelioSalesOrderPresenter.getCourier());
        }

        Shipment shipment = jubelioDelivery.getShipment();

        Role driverRole = roleRepository.findByName(Role.Name.JUBELIO_DRIVER);
        User driver = userRepository.findByRolesContainingAndTenantId(driverRole, shipment.getTenant().getId());

        transitionToDelivery(driver, getDeliveryJob(shipment));
        try {
            resiService.trackAirwayBill(jubelioDelivery);
        } catch (Exception e) {
            LOGGER.error("Failed to track airwaybill. " + e.getMessage(), e);
        }

        publishSyncEvent(jubelioDelivery);
        return jubelioDeliveryRepository.save(jubelioDelivery);
    }

    public void publishSyncEvent(JubelioDelivery jubelioDelivery) {
        Shipment shipment = jubelioDelivery.getShipment();
        JubelioDeliveryPresenter presenter = jubelioDeliveryMapper.toJubelioDeliveryPresenter(jubelioDelivery);

        publishWebhook(
            WebhookType.JUBELIO_SYNCHRONIZE,
            presenter,
            "jubelio_delivery",
            createJubelioSyncPath(jubelioDelivery),
            shipment.getTenant()
        );
    }

    @Transactional
    public void cancelSalesOrder(Shipment shipment) {
        JubelioDelivery jubelioDelivery = jubelioDeliveryRepository.findByShipmentId(shipment.getId());
        if (jubelioDelivery != null) {
            StockLocation stockLocation = shipment.getSlot().getStockLocation();
            if (jubelioDelivery.getState().equals(JubelioDelivery.State.INITIAL)) {
                jubelioDelivery.setState(JubelioDelivery.State.CANCELLED);
            } else if (jubelioDelivery.getState().equals(JubelioDelivery.State.PAID)) {
                try {
                    Optional<JubelioSalesOrderPresenter> salesOrderPresenterOpt = jubelioApiService.getSalesOrder(jubelioDelivery.getSalesOrderId(),stockLocation);
                    if (!salesOrderPresenterOpt.isPresent())
                        return;

                    JubelioSalesOrderPresenter salesOrderPresenter = salesOrderPresenterOpt.get();
                    salesOrderPresenter.setIsCanceled(true);
                    salesOrderPresenter.setCancelReason("Customer Request");

                    boolean isUpdated = jubelioApiService.editSalesOrder(jubelioDelivery.getSalesOrderId(), jubelioDeliveryMapper.jubelioSalesOrderPresenterToJubelioSalesOrderForm(salesOrderPresenter), stockLocation);
                    if (isUpdated) {
                        jubelioDelivery.setState(JubelioDelivery.State.CANCELLED);
                    }
                } catch (Exception e) {
                    LOGGER.error(e.getMessage(), e);
                }
            }
            jubelioDeliveryRepository.save(jubelioDelivery);
        }
    }

    @Transactional
    public void completeSalesOrder(JubelioDelivery jubelioDelivery) {
        Shipment shipment = jubelioDelivery.getShipment();
        StockLocation stockLocation = shipment.getSlot().getStockLocation();
        try {
            Optional<JubelioSalesOrderPresenter> salesOrderPresenterOpt = jubelioApiService.getSalesOrder(jubelioDelivery.getSalesOrderId(), stockLocation);
            if (!salesOrderPresenterOpt.isPresent())
                return;

            final boolean isUpdated = jubelioApiService.markSalesOrderAsComplete(jubelioDelivery.getSalesOrderId(), stockLocation);
            if (isUpdated) {
                Role driverRole = roleRepository.findByName(Role.Name.JUBELIO_DRIVER);
                User driver = userRepository.findByRolesContainingAndTenantId(driverRole, shipment.getTenant().getId());

                jubelioDelivery.setState(JubelioDelivery.State.COMPLETED);
                completeDelivery(driver, getDeliveryJob(shipment));
                publishSyncEvent(jubelioDelivery);
                jubelioDeliveryRepository.save(jubelioDelivery);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    @Transactional
    public void handleResiEvent(ResiEvent event) {
        ResiWebhookForm form;
        try {
            form = mapper.readValue(event.getPayload(), ResiWebhookForm.class);
        } catch (IOException e) {
            LOGGER.error("Failed mapping ResiEvent payload to ResiWebhookForm", e);
            return;
        }

        Shipment shipment = shipmentRepository.findByOrderNumber(form.getExternalId());
        if (shipment == null)
            return;

        JubelioDelivery jubelioDelivery = jubelioDeliveryRepository.findByShipmentId(shipment.getId());
        if (jubelioDelivery == null)
            return;

        completeSalesOrder(jubelioDelivery);
    }

    private Job getShoppingJob(Shipment shipment) {
        Optional<Job> shoppingJob = shipment.getJobs().stream().filter(job -> job.isShopping() || job.isOnDemandShopping() || job.isRanger() || job.isOnDemandRanger()).findAny();

        if (!shoppingJob.isPresent())
            throw new EntityNotFoundException();

        return shoppingJob.get();
    }

    private Job getDeliveryJob(Shipment shipment) {
        Optional<Job> deliveryJob = shipment.getJobs().stream().filter(job -> job.isDelivery() || job.isRanger() || job.isOnDemandRanger() || job.isOnDemandDelivery()).findAny();

        if (!deliveryJob.isPresent())
            throw new EntityNotFoundException();

        return deliveryJob.get();
    }

    private void transitionToFinishShopping(User user, Job job) {
        Batch batch = job.getBatch();
        if (batch.getUser() == null) {
            batch.setUser(user);
            batchRepository.save(batch);
        }

        job.setState(Job.State.STARTED);
        job.setState(Job.State.FINALIZING);
        job.setState(Job.State.FINISHED);
        jobRepository.save(job);

    }

    private void transitionToDelivery(User user, Job job) {
        Batch batch = job.getBatch();
        if (batch.getUser() == null) {
            batch.setUser(user);
            batchRepository.save(batch);
        }

        job.setState(Job.State.STARTED);
        job.setState(Job.State.ACCEPTED);
        job.setState(Job.State.DELIVERING);
        jobRepository.save(job);
    }

    private void completeDelivery(User user, Job job) {
        Batch batch = job.getBatch();
        if (batch.getUser() == null) {
            batch.setUser(user);
            batchRepository.save(batch);
        }

        job.setState(Job.State.FOUND_ADDRESS);
        job.setState(Job.State.FINALIZING);
        job.setState(Job.State.FINISHED);
        jobRepository.save(job);
    }

    private void publishWebhook(WebhookType webhookType, Object body, String rootName, String path, Tenant tenant) {
        ObjectNode wrapperNode = JsonNodeFactory.instance.objectNode();
        JsonNode bodyNode = mapper.convertValue(body, JsonNode.class);
        wrapperNode.set(rootName, bodyNode);

        webhookPublisherService.publish(webhookType, wrapperNode, path, tenant.getId());
    }

    private String createJubelioSyncPath(JubelioDelivery jubelioDelivery) {
        return JUBELIO_WEBHOOK_PATH +
                jubelioDelivery.getId().toString() +
                "/" +
                jubelioDelivery.getState().toString();
    }

}
