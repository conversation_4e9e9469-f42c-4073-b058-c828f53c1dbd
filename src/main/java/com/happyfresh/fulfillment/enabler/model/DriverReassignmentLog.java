package com.happyfresh.fulfillment.enabler.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.happyfresh.fulfillment.common.util.DateTimeUtil;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class DriverReassignmentLog {

    private String orderNumber;

    private LocalDateTime slotStartTime;

    private LocalDateTime slotEndTime;

    private LocalDateTime pickingFinishTime;

    private LocalDateTime reassignmentTime;

    private Long initialUserId;

    private Long newUserId;

    private String segmentTrackingId;

    private List<Candidate> candidates;

    @JsonProperty("slot_start_time")
    public String getSlotStartTimeString() {
        return DateTimeUtil.localDateTimeToStringSlotDateTime(slotStartTime);
    }

    @JsonProperty("slot_end_time")
    public String getSlotEndTimeString() {
        return DateTimeUtil.localDateTimeToStringSlotDateTime(slotEndTime);
    }

    @JsonProperty("picking_finish_time")
    public String getPickingFinishTimeString() {
        return DateTimeUtil.localDateTimeToStringSlotDateTime(pickingFinishTime);
    }

    @JsonProperty("reassignment_time")
    public String getReassignmentTimeString() {
        return DateTimeUtil.localDateTimeToStringSlotDateTime(reassignmentTime);
    }

    @Getter
    @Setter
    @JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class Candidate {

        private Long userId;

        private boolean available;

        private int activeBatchCount;

        private LocalDateTime arrivalAtWarehouse;

        private boolean insideGeofence;

        private LocalDateTime agentInGeofenceCreatedAt;

        @JsonProperty("arrival_at_warehouse")
        public String getArrivalAtWarehouseString() {
            return DateTimeUtil.localDateTimeToStringSlotDateTime(arrivalAtWarehouse);
        }

        @JsonProperty("agent_in_geofence_created_at")
        public String getAgentInGeofenceCreatedAtString() {
            return DateTimeUtil.localDateTimeToStringSlotDateTime(agentInGeofenceCreatedAt);
        }
    }

}
