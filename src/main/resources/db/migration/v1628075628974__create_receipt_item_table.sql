CREATE TABLE hff_receipt_item (
    id BIGSERIAL,
    receipt_id BIGINT REFERENCES hff_receipt (id),
    sku VARCHAR(255) NOT NULL,
    amount NUMERIC NOT NULL DEFAULT 0.0,

    tenant_id BIGINT NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITHOUT TIME ZONE,
    created_by BIGINT NOT NULL,
    updated_by B<PERSON>IN<PERSON>,

    PRIMARY KEY (id)
);

CREATE INDEX index_hff_receipt_item_on_receipt_id ON hff_receipt_item (receipt_id);