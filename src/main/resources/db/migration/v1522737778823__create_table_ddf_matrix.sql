CREATE TABLE hff_ddf_matrix (
    id BIGSERIAL,
    type VA<PERSON>HA<PERSON>(63) NOT NULL,
    column_id INT NOT NULL,
    row_id INT NOT NULL,
    value DOUBLE PRECISION NOT NULL,
    stock_location_id BIGINT REFERENCES hff_stock_location NOT NULL ,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITHOUT TIME ZONE,
    created_by BIGINT NOT NULL,
    updated_by BIGINT,
    tenant_id BIGINT NOT NULL,

    PRIMARY KEY (id)
);

CREATE UNIQUE INDEX unique_ddf_matrix_stock_location_column_row_id ON hff_ddf_matrix (stock_location_id, column_id, row_id);
CREATE INDEX index_ddf_matrix_type ON hff_ddf_matrix (type);
CREATE INDEX index_ddf_matrix_stock_location_id ON hff_ddf_matrix (stock_location_id);
CREATE INDEX index_ddf_matrix_tenant_id ON hff_ddf_matrix (tenant_id);
CREATE INDEX index_ddf_matrix_column_id ON hff_ddf_matrix (column_id);
CREATE INDEX index_ddf_matrix_row_id ON hff_ddf_matrix (row_id);
CREATE INDEX index_ddf_matrix_column_row_id ON hff_ddf_matrix (column_id, row_id);