CREATE TABLE hff_user_enabler (
    id BIGSERIAL,
    user_id BIGINT REFERENCES hff_user (id),
    enabler VARCHAR(255) NOT NULL,
    tenant_id BIGINT NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITHOUT TIME ZONE,
    created_by BIGINT NOT NULL,
    updated_by <PERSON><PERSON><PERSON><PERSON>,

    PRIMARY KEY (id)
);

CREATE INDEX index_hff_user_enabler_on_user_id ON hff_user_enabler (user_id);
CREATE INDEX index_hff_user_enabler_on_enabler ON hff_user_enabler (enabler);