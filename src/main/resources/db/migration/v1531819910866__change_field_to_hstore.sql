CREATE OR R<PERSON>LACE FUNCTION string_to_hstore(varchar, varchar)
  RETURNS hstore
  IMMUTABLE
  STRICT
  LANGUAGE sql
AS $func$
  SELECT hstore($1, $2)
$func$;

ALTER TABLE hff_item RENAME COLUMN name to translation_names;
ALTER TABLE hff_category RENAME COLUMN name to permalink;

ALTER TABLE hff_item ALTER COLUMN translation_names type HSTOR<PERSON> using string_to_hstore('en', translation_names);
ALTER TABLE hff_item ALTER COLUMN image_url type HSTORE using string_to_hstore('1', image_url);

ALTER TABLE hff_category ADD COLUMN translation_names HSTORE;
ALTER TABLE hff_item ADD COLUMN translation_promotions HSTORE;

ALTER TABLE hff_item ADD COLUMN currency VARCHAR(255);
UPDATE hff_item set currency = 'ID';
ALTER TABLE hff_item ALTER COLUMN currency SET NOT NULL;

ALTER TABLE hff_item DROP COLUMN category_sequence;