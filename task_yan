create model credit_card
id integer primary key autoincrement,
state int not null default 0
name string,
last_digits string,
month string,
year string,
oy_card_id string,
created_at timestamp withouttimezone,
updated_at timestamp withouttimezone

create entity user_credit_card
user_id foreign_key to user,
credit_card_id foreign_key to credit_card


create api in oy controller create api, and the implementation is in oy service for
get all user and credit card data that will return
user email, user phone, credit card name, credit card last digits, credit card month, credit card year


get all credit card data

post create credit card
post user credit card data


FSRV_API_DOCUMENTATION_PASSWORD=password;FSRV_API_DOCUMENTATION_USERNAME=user;FSRV_DATABASE_URL_KEY=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************RITE_KEY=SomeRandomString;SEGMENT_MOBILEWEB_WRITE_KEY=SomeRandomString;SEGMENT_SPRINKLES_WRITE_KEY=SomeRandomString;SEGMENT_WEB_WRITE_KEY=SomeRandomString


card_id
cd43c964-4d0f-47db-81bf-22562ed09f37
request.setAccountNumber("****************");
request.setCvv("345");
request.setExpDate("07/28");
request.setHolderName("HolderName1");