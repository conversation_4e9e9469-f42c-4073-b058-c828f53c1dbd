# ADR00015: Separate slot optimization kafka event to different topic based on slot start

* Created at: 2022-06-30
* Created by: Hard<PERSON>yah

## Status

Accepted

## Context

There was an increase in Slot Optimization kafka event production due to increasing order. This cause lagging when consume
the kafka event and also increasing CPU usage.

## Decision

We decided to separate Slot Optimization kafka event published into different topic for today and tomorrow slots to reduce event consumption in slot optimization topic.

## Consequences

We need to check slot start time before deciding which topic the event will be published