image: gradle:6.9.2-jdk11

definitions:
  caches:
    gradlewrapper: ${GRADLE_USER_HOME}/wrapper
    sonar: ~/.sonar/cache # Caching SonarCloud artifacts will speed up your build
  services:
    postgres:
      image: postgres
      environment:
        POSTGRES_DB: "test"
        POSTGRES_USER: "postgres"
        POSTGRES_PASSWORD: "password"
    redis:
      image: redis
      memory: 512
    elasticsearch:
      image: elasticsearch:5.6-alpine
      environment:
        ES_JAVA_OPTS: -Xms512m -Xmx512m
    kafka:
      image: johnnypark/kafka-zookeeper:2.3.0
      memory: 512
    docker:
      memory: 2048
  steps:
    - step: &build-test-sonarcloud
        name: Build, test and analyze on SonarCloud
        caches:
          - gradle
        script:
          - gradle sonarqube -Dsonar.projectKey=makana14_fulfillment.v2 -Dsonar.projectName=fulfillment.v2 -Dsonar.organization=happyfresh -Dsonar.host.url=https://sonarcloud.io -Dsonar.login=$SONAR_TOKEN
        services:
          - postgres
        artifacts:
          - build/libs/**
    - step: &build-integration-test-sonarcloud
        name: Build, run unit test, and analyze on SonarCloud
        caches:
          - gradle
        script:
          - gradle sonarqube -Dsonar.projectKey=makana14_fulfillment.v2 -Dsonar.projectName=fulfillment.v2 -Dsonar.organization=happyfresh -Dsonar.host.url=https://sonarcloud.io -Dsonar.login=$SONAR_TOKEN
        services:
          - postgres
          - redis
          - elasticsearch
          - kafka
        artifacts:
          - build/libs/**
        size: 2x
    - step: &upload-new-ecstaskdefinition-to-lab
        name: Upload Task Definition to lab
        image: atlassian/default-image:2
        script:
          - pipe: atlassian/aws-ecs-deploy:1.0.0
            variables:
              AWS_ACCESS_KEY_ID: $LAB_AWSECR_ACCESS_KEY
              AWS_SECRET_ACCESS_KEY: $LAB_AWSECR_SECRET_KEY
              AWS_DEFAULT_REGION: $LAB_AWSECR_DEFAULT_REGION
              CLUSTER_NAME: $LAB_CLUSTER_NAME
              SERVICE_NAME: $LAB_SERVICE_NAME
              TASK_DEFINITION: "task-definition.json"
    - step: &build-docs-and-upload-to-backstage
        name: Build Docs and Upload to Backstage
        image: hfcoramil/pipeline-builder
        deployment: build-docs
        caches:
          - docker
        services:
          - docker
        script:
          - source /builder/scripts/ecs-deploy-lib.sh
          - login_aws ${AWS_ACCESS_KEY_ID} ${AWS_SECRET_ACCESS_KEY}
          - gen_techdocs coramil-backstage-techdocs
        condition:
          changesets:
            includePaths:
              - "mkdocs.yml"
              - "catalog-info.yaml"
              - "docs/**"
    - step: &sca-scan
        name: Scan software composition analysis
        image: public.ecr.aws/j9e7a9t4/coramil/pipeline-builder:gradle69-0.0.1
        script:
          - task inspect-security-sca
        artifacts:
          - image.tar
          - trivy-*
    - step: &container-scan
        name: Scan image container
        image: public.ecr.aws/j9e7a9t4/coramil/pipeline-builder:gradle69-0.0.1
        services:
          - docker
        script:
          - task inspect-security-container
        artifacts:
          - image.tar
          - trivy-*
    - step: &sast-scan
        name: Scan code quality Sonar
        image: public.ecr.aws/j9e7a9t4/coramil/pipeline-builder:gradle69-0.0.1
        caches:
          - gradle
        script:
          - task inspect-code-quality
    - step: &end-to-end-test-staging
        name: Run End to End Test on Staging
        image: public.ecr.aws/j9e7a9t4/coramil/pipeline-builder:mavenjdk11-latest
        deployment: staging-end-to-end-test
        caches:
          - maven
        script:
          - task test-e2e
        artifacts:
          - happy-fresh-api-test/target/cucumber-html-reports/**
    - step: &deploy_to_ecs_sandbox
        name: Build with Docker, upload to ECR, update ECS task
        image: public.ecr.aws/j9e7a9t4/coramil/pipeline-builder:gradle69-0.0.2
        deployment: sandbox
        caches:
          - docker
        services:
          - docker
        script:
          - export VERSION=$(cat ./VERSION)
          - source /builder/scripts/ecs-deploy-lib.sh
          - login_aws ${AWS_ACCESS_KEY_ID} ${AWS_SECRET_ACCESS_KEY}
          - login_docker_to_aws_ecr ${ECS_REGION} ${CONTAINER_REPOSITORY_URL}
          - FULL_IMAGE_REPO_NAME="${CONTAINER_REPOSITORY_URL}/${DOCKER_IMAGE_URL}:build-${BITBUCKET_BUILD_NUMBER}"
          - DEPLOY_DOCKER_IMAGE_URL=${DOCKER_IMAGE_URL} VERSION=${VERSION} make docker-build-bitbucket
          - task inspect-security-container
          - docker_tag_and_push ${DOCKER_IMAGE_URL} "${CONTAINER_REPOSITORY_URL}/${DOCKER_IMAGE_URL}:latest"
          - docker_tag_and_push ${DOCKER_IMAGE_URL} ${FULL_IMAGE_REPO_NAME}
          - update_task_definition ${AWS_SERVICE_NAME} ${AWS_CLUSTER_NAME} ${FULL_IMAGE_REPO_NAME} ${ECS_REGION} 0
        artifacts:
          - trivy-*
    - step: &deploy_to_ecs_staging
        name: Build with Docker, upload to ECR, update ECS task
        image: public.ecr.aws/j9e7a9t4/coramil/pipeline-builder:gradle69-0.0.2
        deployment: staging
        caches:
          - docker
        services:
          - docker
        trigger: manual
        script:
          - export VERSION=$(cat ./VERSION)
          - source /builder/scripts/ecs-deploy-lib.sh
          - login_aws ${AWS_ACCESS_KEY_ID} ${AWS_SECRET_ACCESS_KEY}
          - login_docker_to_aws_ecr ${STAGE_ECS_REGION} ${STAGE_CONTAINER_REPOSITORY_URL}
          - FULL_IMAGE_REPO_NAME="${STAGE_CONTAINER_REPOSITORY_URL}/${STAGE_DOCKER_IMAGE_URL}:build-${BITBUCKET_BUILD_NUMBER}"
          - DEPLOY_DOCKER_IMAGE_URL=${STAGE_DOCKER_IMAGE_URL} VERSION=${VERSION} make docker-build-bitbucket
          - task inspect-security-container
          - docker_tag_and_push ${STAGE_DOCKER_IMAGE_URL} "${STAGE_CONTAINER_REPOSITORY_URL}/${STAGE_DOCKER_IMAGE_URL}:latest"
          - docker_tag_and_push ${STAGE_DOCKER_IMAGE_URL} ${FULL_IMAGE_REPO_NAME}
          - update_task_definition ${STAGE_AWS_SERVICE_NAME} ${STAGE_AWS_CLUSTER_NAME} ${FULL_IMAGE_REPO_NAME} ${STAGE_ECS_REGION} 0
          - curl -X POST -d api_key=${SLEUTH_API_KEY} -d environment=${SLEUTH_ENV} -d sha=${BITBUCKET_COMMIT} https://app.sleuth.io/api/1/deployments/happyfresh/fulfillmentv2/register_deploy
        artifacts:
          - trivy-*
    - step: &deploy_to_ecs_production
        name: Build with Docker, upload to ECR, update ECS task
        image: public.ecr.aws/j9e7a9t4/coramil/pipeline-builder:gradle69-0.0.2
        deployment: production
        caches:
          - docker
        services:
          - docker
        trigger: manual
        script:
          - export VERSION=$(cat ./VERSION)
          - source /builder/scripts/ecs-deploy-lib.sh
          - login_aws ${AWS_ACCESS_KEY_ID} ${AWS_SECRET_ACCESS_KEY}
          - login_docker_to_aws_ecr ${PROD_ECS_REGION} ${PROD_CONTAINER_REPOSITORY_URL}
          - FULL_IMAGE_REPO_NAME="${PROD_CONTAINER_REPOSITORY_URL}/${PROD_DOCKER_IMAGE_URL}:build-${BITBUCKET_BUILD_NUMBER}"
          - DEPLOY_DOCKER_IMAGE_URL=${PROD_DOCKER_IMAGE_URL} VERSION=${VERSION} make docker-build-bitbucket
          - task inspect-security-container
          - docker_tag_and_push ${PROD_DOCKER_IMAGE_URL} "${PROD_CONTAINER_REPOSITORY_URL}/${PROD_DOCKER_IMAGE_URL}:latest"
          - docker_tag_and_push ${PROD_DOCKER_IMAGE_URL} ${FULL_IMAGE_REPO_NAME}
          - update_task_definition ${PROD_AWS_SERVICE_NAME} ${PROD_AWS_CLUSTER_NAME} ${FULL_IMAGE_REPO_NAME} ${PROD_ECS_REGION} 0
          - curl -X POST -d api_key=${SLEUTH_API_KEY} -d environment=${SLEUTH_ENV} -d sha=${BITBUCKET_COMMIT} https://app.sleuth.io/api/1/deployments/happyfresh/fulfillmentv2/register_deploy
        artifacts:
          - trivy-*
    - step: &deploy_replica_to_ecs_production
        name: Build with Docker, upload to ECR, update ECS task
        image: hfcoramil/pipeline-builder
        deployment: production
        caches:
          - docker
        services:
          - docker
        script:
          - export VERSION=$(cat ./VERSION)
          - source /builder/scripts/ecs-deploy-lib.sh
          - login_aws ${AWS_ACCESS_KEY_ID} ${AWS_SECRET_ACCESS_KEY}
          - login_docker_to_aws_ecr ${PROD_ECS_REGION} ${PROD_CONTAINER_REPOSITORY_URL}
          - FULL_IMAGE_REPO_NAME="${PROD_CONTAINER_REPOSITORY_URL}/${PROD_DOCKER_IMAGE_URL}-replica:build-${BITBUCKET_BUILD_NUMBER}"
          - DEPLOY_DOCKER_IMAGE_URL=${PROD_DOCKER_IMAGE_URL}-replica VERSION=${VERSION} make docker-build-bitbucket
          - docker_tag_and_push ${PROD_DOCKER_IMAGE_URL}-replica "${PROD_CONTAINER_REPOSITORY_URL}/${PROD_DOCKER_IMAGE_URL}-replica:latest"
          - docker_tag_and_push ${PROD_DOCKER_IMAGE_URL}-replica ${FULL_IMAGE_REPO_NAME}
          - update_task_definition ${PROD_AWS_SERVICE_NAME}-Replica ${PROD_AWS_CLUSTER_NAME} ${FULL_IMAGE_REPO_NAME} ${PROD_ECS_REGION} 0
          - curl -X POST -d api_key=${SLEUTH_API_KEY} -d environment=${SLEUTH_ENV} -d sha=${BITBUCKET_COMMIT} https://app.sleuth.io/api/1/deployments/happyfresh/fulfillmentv2/register_deploy
pipelines:
  branches:
    master:
      - parallel:
          - step: *build-test-sonarcloud
          - step: *build-docs-and-upload-to-backstage
      - step: *deploy_to_ecs_staging
      - step: *end-to-end-test-staging
      - step: *deploy_to_ecs_production
    feature/*:
      - step: *build-test-sonarcloud
    bugfix/*:
      - step: *build-test-sonarcloud
    release/*:
      - step: *build-test-sonarcloud
      - step: *deploy_to_ecs_staging
      - step: *end-to-end-test-staging
  custom:
    run_code_analysis:
      - step: *build-test-sonarcloud
    sca-sast-scan:
      - step: *sca-scan
      - step: *sast-scan
    deploy_to_ecs_sandbox:
      - variables:
          - name: VERSION
      - step: *deploy_to_ecs_sandbox
    deploy_to_ecs_staging:
      - step:
          name: Build with Docker, upload to ECR, update ECS task
          image: public.ecr.aws/j9e7a9t4/coramil/pipeline-builder:gradle69-0.0.2
          deployment: staging
          caches:
            - docker
          services:
            - docker
          script:
            - export VERSION=$(cat ./VERSION)
            - source /builder/scripts/ecs-deploy-lib.sh
            - login_aws ${AWS_ACCESS_KEY_ID} ${AWS_SECRET_ACCESS_KEY}
            - login_docker_to_aws_ecr ${STAGE_ECS_REGION} ${STAGE_CONTAINER_REPOSITORY_URL}
            - FULL_IMAGE_REPO_NAME="${STAGE_CONTAINER_REPOSITORY_URL}/${STAGE_DOCKER_IMAGE_URL}:build-${BITBUCKET_BUILD_NUMBER}"
            - DEPLOY_DOCKER_IMAGE_URL=${STAGE_DOCKER_IMAGE_URL} VERSION=${VERSION} make docker-build-bitbucket
            - task inspect-security-container
            - docker_tag_and_push ${STAGE_DOCKER_IMAGE_URL} "${STAGE_CONTAINER_REPOSITORY_URL}/${STAGE_DOCKER_IMAGE_URL}:latest"
            - docker_tag_and_push ${STAGE_DOCKER_IMAGE_URL} ${FULL_IMAGE_REPO_NAME}
            - update_task_definition ${STAGE_AWS_SERVICE_NAME} ${STAGE_AWS_CLUSTER_NAME} ${FULL_IMAGE_REPO_NAME} ${STAGE_ECS_REGION} 0
            - curl -X POST -d api_key=${SLEUTH_API_KEY} -d environment=${SLEUTH_ENV} -d sha=${BITBUCKET_COMMIT} https://app.sleuth.io/api/1/deployments/happyfresh/fulfillmentv2/register_deploy
          artifacts:
            - trivy-*
    deploy_to_ecs_production:
      - step:
          name: Build with Docker, upload to ECR, update ECS task
          image: public.ecr.aws/j9e7a9t4/coramil/pipeline-builder:gradle69-0.0.2
          deployment: production
          caches:
            - docker
          services:
            - docker
          script:
            - export VERSION=$(cat ./VERSION)
            - source /builder/scripts/ecs-deploy-lib.sh
            - login_aws ${AWS_ACCESS_KEY_ID} ${AWS_SECRET_ACCESS_KEY}
            - login_docker_to_aws_ecr ${PROD_ECS_REGION} ${PROD_CONTAINER_REPOSITORY_URL}
            - FULL_IMAGE_REPO_NAME="${PROD_CONTAINER_REPOSITORY_URL}/${PROD_DOCKER_IMAGE_URL}:build-${BITBUCKET_BUILD_NUMBER}"
            - DEPLOY_DOCKER_IMAGE_URL=${PROD_DOCKER_IMAGE_URL} VERSION=${VERSION} make docker-build-bitbucket
            - task inspect-security-container
            - docker_tag_and_push ${PROD_DOCKER_IMAGE_URL} "${PROD_CONTAINER_REPOSITORY_URL}/${PROD_DOCKER_IMAGE_URL}:latest"
            - docker_tag_and_push ${PROD_DOCKER_IMAGE_URL} ${FULL_IMAGE_REPO_NAME}
            - update_task_definition ${PROD_AWS_SERVICE_NAME} ${PROD_AWS_CLUSTER_NAME} ${FULL_IMAGE_REPO_NAME} ${PROD_ECS_REGION} 0
            - curl -X POST -d api_key=${SLEUTH_API_KEY} -d environment=${SLEUTH_ENV} -d sha=${BITBUCKET_COMMIT} https://app.sleuth.io/api/1/deployments/happyfresh/fulfillmentv2/register_deploy
          artifacts:
            - trivy-*
    deploy_replica_to_ecs_production:
      - variables:
          - name: VERSION
      - step: *deploy_replica_to_ecs_production
    build_and_upload_docs:
      - step: *build-docs-and-upload-to-backstage
    run_ent_to_end_test_staging:
      - step: *end-to-end-test-staging
  pull-requests:
    "**":
      - step: *build-test-sonarcloud
