# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Java Spring Boot application for fulfillment services (HappyFresh). The application handles order fulfillment, batch processing, delivery management, and integration with various third-party services.

## Build System and Commands

### Build Commands
```bash
# Clean build (skip tests)
gradle clean build -x test -i --stacktrace --build-cache

# Build with tests
gradle clean build --build-cache -i --stacktrace

# Run tests only
gradle test

# Create database migration file
gradle createMigrationFile -Pf=migration_name
# or for table creation
gradle createMigrationFile -Pc=table_name
```

### Docker Commands
```bash
# Build local Docker image
make docker-build-local ENVIRONMENT=dev  # or prod, pre-prod, base, test

# Run production image
make docker-run-prod

# Run development image (with hot reload)
make docker-run-dev

# Run with all dependencies
docker-compose up
```

### Running the Application
```bash
# With New Relic agent
java -javaagent:./newrelic/newrelic.jar -jar ./build/libs/fulfillment.v2-0.0.1-SNAPSHOT.jar

# Without New Relic
java -jar ./build/libs/fulfillment.v2-0.0.1-SNAPSHOT.jar
```

## Architecture Overview

### Core Application Structure
- **Entry Point**: `MainApp.java` - Spring Boot application with async configuration
- **Base Package**: `com.happyfresh.fulfillment`
- **Java Version**: 11
- **Spring Boot Version**: 2.1.3.RELEASE

### Key Domain Modules
- **batch**: Shopping batch management and processing
- **shipment**: Order shipment handling
- **slot**: Delivery slot management
- **agent**: Agent/driver management
- **common**: Shared utilities, configuration, and base classes
- **admin**: Administrative controllers and services
- **repository**: JPA repositories for data access
- **entity**: JPA entities representing database tables

### External Service Integrations
- **Grab Express**: Delivery service integration
- **Lalamove**: Delivery service integration
- **Locus**: Route optimization service
- **Jubelio**: ERP integration
- **Elasticsearch**: Search and indexing
- **Redis**: Caching and session management
- **Kafka**: Message queue system
- **PostgreSQL**: Primary database

### Technology Stack
- **Framework**: Spring Boot 2.1.3 with Spring Security, Spring Data JPA
- **Database**: PostgreSQL with Flyway migrations
- **Cache**: Redis with Jedis client
- **Message Queue**: Apache Kafka
- **Search**: Elasticsearch 5.5.1
- **Mapping**: MapStruct for object mapping
- **Scheduling**: ShedLock for distributed task scheduling
- **Monitoring**: New Relic, Datadog, HyperTrack
- **Testing**: JUnit with PowerMock and Mockito

## Database Management

### Migration Files
- Located in `src/main/resources/db/migration/`
- Use Flyway versioning: `v{timestamp}__{description}.sql`
- Create new migrations with `gradle createMigrationFile`

### Non-Locking Migrations
Avoid table locks in production by using this pattern:
```sql
-- Instead of this (locks table):
ALTER TABLE table_name ADD COLUMN column_name BOOLEAN NOT NULL DEFAULT FALSE;

-- Use this pattern:
ALTER TABLE table_name ADD COLUMN column_name BOOLEAN;
UPDATE table_name SET column_name = FALSE;
ALTER TABLE table_name ALTER COLUMN column_name SET DEFAULT FALSE;
ALTER TABLE table_name ALTER COLUMN column_name SET NOT NULL;
```

## Configuration

### Environment Variables
Key environment variables for local development:
- `FSRV_DATABASE_URL_KEY`: Database connection URL
- `FSRV_REDIS_URL`, `FSRV_REDIS_PASSWORD`: Redis configuration
- `FSRV_ES_CLUSTER_NODES`: Elasticsearch cluster URL
- `FSRV_KAFKA_SERVER_URL`: Kafka server URL
- `FSRV_GOOGLE_SERVICE_API_KEY`: Google Services API key

### Application Properties
- `application.properties`: Default configuration
- `application-{env}.properties`: Environment-specific configs
- Properties use Spring Boot conventions with profiles

## Development Guidelines

### Code Structure
- Use MapStruct for object mapping between DTOs and entities
- Controllers follow RESTful conventions with proper HTTP status codes
- Services contain business logic, repositories handle data access
- Use Spring Security for authentication and authorization
- Implement proper validation using Bean Validation annotations

### Database Entities
- All entities extend `BaseEntity` or `BaseEntityWithAudit`
- Use JPA auditing for created/updated timestamps
- Implement proper relationships with cascade settings
- Use PostgreSQL-specific types (hstore, arrays) where appropriate

### Testing
- Unit tests in `src/test/java/com/happyfresh/fulfillment/unit/`
- Integration tests in `src/test/java/com/happyfresh/fulfillment/integrationTest/`
- Test fixtures in `src/test/resources/fixtures/`
- Use PowerMock for static method mocking when necessary

### Scheduling and Background Jobs
- Use `@Scheduled` annotations for periodic tasks
- Implement `@SchedulerLock` for distributed task coordination
- Background processing uses Spring's async capabilities

## Development Environment

### Local Development Setup
1. Install Java 11, Gradle, Docker
2. Run `docker-compose up` to start dependencies
3. Build with `gradle clean build -x test`
4. Run application or use IDE debug configuration

### IDE Configuration
- **IntelliJ IDEA**: Follow engineering confluence setup guide
- **VS Code**: Install Java Extension Pack, Spring Boot Extension Pack, Lombok support
- Enable annotation processing for MapStruct and Lombok

### Dependencies
- External JARs in `library/` directory (jedis-lock, google-maps-services)
- Gradle manages most dependencies automatically
- Custom configurations in `gradle/deploy.gradle`